using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Serviço gerenciador de containers que abstrai Docker e Podman
/// </summary>
public class ContainerManagerService : IContainerService
{
    private readonly ILogger<ContainerManagerService> _logger;
    private readonly DockerService _dockerService;
    private readonly PodmanService _podmanService;
    private readonly ContainerEngineDetector _engineDetector;

    public ContainerManagerService(
        ILogger<ContainerManagerService> logger,
        DockerService dockerService,
        PodmanService podmanService,
        ContainerEngineDetector engineDetector)
    {
        _logger = logger;
        _dockerService = dockerService;
        _podmanService = podmanService;
        _engineDetector = engineDetector;
    }

    public async Task<ContainerListResponse> ListContainersAsync(ContainerListRequest request, CancellationToken cancellationToken = default)
    {
        var response = new ContainerListResponse();

        try
        {
            var engine = await GetAvailableEngineAsync(request.Engine, cancellationToken);
            if (engine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                response.Errors.Add("Docker e Podman não estão disponíveis");
                return response;
            }

            var filters = BuildFilters(request.Filters);
            var containers = await engine.ListContainersAsync(request.All, filters, cancellationToken);

            response.Success = true;
            response.Containers = containers.Select(MapToDto).ToList();
            response.TotalCount = response.Containers.Count;
            response.Engine = engine.EngineType;
            response.Message = $"Listados {response.TotalCount} containers usando {engine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar containers");
            response.Success = false;
            response.Message = "Erro interno ao listar containers";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerResponse> GetContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var container = await containerEngine.GetContainerAsync(containerId, cancellationToken);
            if (container == null)
            {
                response.Success = false;
                response.Message = $"Container {containerId} não encontrado";
                return response;
            }

            response.Success = true;
            response.Container = MapToDto(container);
            response.Message = $"Container encontrado usando {containerEngine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter container {ContainerId}", containerId);
            response.Success = false;
            response.Message = "Erro interno ao obter container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerRunResponse> RunContainerAsync(ContainerRunRequest request, CancellationToken cancellationToken = default)
    {
        var response = new ContainerRunResponse();

        try
        {
            var engine = await GetAvailableEngineAsync(request.Engine, cancellationToken);
            if (engine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var options = MapToRunOptions(request);
            var containerId = await engine.RunContainerAsync(options, cancellationToken);

            response.Success = true;
            response.ContainerId = containerId;
            response.ContainerName = request.Name;
            response.Engine = engine.EngineType;
            response.Message = $"Container criado com sucesso usando {engine.EngineName}";
            response.EstimatedStartTime = TimeSpan.FromSeconds(5); // Estimativa
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar container");
            response.Success = false;
            response.Message = "Erro ao executar container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerActionResponse> StartContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Start, engine, cancellationToken);
    }

    public async Task<ContainerActionResponse> StopContainerAsync(string containerId, int? timeout = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Stop, engine, cancellationToken, timeout);
    }

    public async Task<ContainerActionResponse> RestartContainerAsync(string containerId, int? timeout = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Restart, engine, cancellationToken, timeout);
    }

    public async Task<ContainerActionResponse> PauseContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Pause, engine, cancellationToken);
    }

    public async Task<ContainerActionResponse> UnpauseContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Unpause, engine, cancellationToken);
    }

    public async Task<ContainerActionResponse> RemoveContainerAsync(string containerId, bool force = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        return await ExecuteContainerActionAsync(containerId, ContainerAction.Remove, engine, cancellationToken, force: force);
    }

    public async Task<ContainerLogsResponse> GetContainerLogsAsync(ContainerLogsRequest request, CancellationToken cancellationToken = default)
    {
        var response = new ContainerLogsResponse();

        try
        {
            var engine = await GetAvailableEngineAsync(request.Engine, cancellationToken);
            if (engine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var logs = await engine.GetContainerLogsAsync(
                request.ContainerId,
                request.Tail,
                request.Follow,
                request.Timestamps,
                request.Since,
                request.Until,
                cancellationToken);

            response.Success = true;
            response.ContainerId = request.ContainerId;
            response.Logs = logs.Select(MapLogToDto).ToList();
            response.TotalLines = response.Logs.Count;
            response.Message = $"Obtidos {response.TotalLines} logs usando {engine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs do container {ContainerId}", request.ContainerId);
            response.Success = false;
            response.Message = "Erro ao obter logs do container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerStatsResponse> GetContainerStatsAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerStatsResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var stats = await containerEngine.GetContainerStatsAsync(containerId, cancellationToken);
            if (stats == null)
            {
                response.Success = false;
                response.Message = $"Não foi possível obter estatísticas do container {containerId}";
                return response;
            }

            response.Success = true;
            response.Stats = MapStatsToDto(stats);
            response.Message = $"Estatísticas obtidas usando {containerEngine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do container {ContainerId}", containerId);
            response.Success = false;
            response.Message = "Erro ao obter estatísticas do container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerExecResponse> ExecContainerAsync(string containerId, string command, string[]? args = null, bool interactive = false, bool tty = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerExecResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var result = await containerEngine.ExecContainerAsync(containerId, command, args, interactive, tty, cancellationToken);

            response.Success = result.Success;
            response.ExitCode = result.ExitCode;
            response.StandardOutput = result.StandardOutput;
            response.StandardError = result.StandardError;
            response.Duration = result.Duration;
            response.Message = result.Success ? "Comando executado com sucesso" : "Comando falhou";

            if (!result.Success)
            {
                response.Errors.Add($"Exit code: {result.ExitCode}");
                if (!string.IsNullOrEmpty(result.StandardError))
                    response.Errors.Add(result.StandardError);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar comando no container {ContainerId}", containerId);
            response.Success = false;
            response.Message = "Erro ao executar comando no container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    /// <summary>
    /// Obtém engine disponível com fallback automático
    /// </summary>
    private async Task<IContainerEngine?> GetAvailableEngineAsync(ContainerEngine? preferredEngine, CancellationToken cancellationToken)
    {
        // Se um engine específico foi solicitado, tentar usá-lo
        if (preferredEngine.HasValue)
        {
            var engine = GetEngine(preferredEngine.Value);
            if (await engine.IsAvailableAsync(cancellationToken))
                return engine;

            _logger.LogWarning("Engine preferido {Engine} não está disponível", preferredEngine.Value);
        }

        // Fallback: tentar Docker primeiro, depois Podman
        if (await _dockerService.IsAvailableAsync(cancellationToken))
        {
            _logger.LogDebug("Usando Docker como engine de container");
            return _dockerService;
        }

        if (await _podmanService.IsAvailableAsync(cancellationToken))
        {
            _logger.LogDebug("Usando Podman como engine de container");
            return _podmanService;
        }

        _logger.LogWarning("Nenhum engine de container disponível");
        return null;
    }

    /// <summary>
    /// Obtém engine específico
    /// </summary>
    private IContainerEngine GetEngine(ContainerEngine engine)
    {
        return engine switch
        {
            ContainerEngine.Docker => _dockerService,
            ContainerEngine.Podman => _podmanService,
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    /// <summary>
    /// Executa ação em container
    /// </summary>
    private async Task<ContainerActionResponse> ExecuteContainerActionAsync(
        string containerId, 
        ContainerAction action, 
        ContainerEngine? engine, 
        CancellationToken cancellationToken,
        int? timeout = null,
        bool force = false)
    {
        var response = new ContainerActionResponse
        {
            ContainerId = containerId,
            Action = action
        };

        var startTime = DateTime.UtcNow;

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            bool success = action switch
            {
                ContainerAction.Start => await containerEngine.StartContainerAsync(containerId, cancellationToken),
                ContainerAction.Stop => await containerEngine.StopContainerAsync(containerId, timeout, cancellationToken),
                ContainerAction.Restart => await containerEngine.RestartContainerAsync(containerId, timeout, cancellationToken),
                ContainerAction.Pause => await containerEngine.PauseContainerAsync(containerId, cancellationToken),
                ContainerAction.Unpause => await containerEngine.UnpauseContainerAsync(containerId, cancellationToken),
                ContainerAction.Remove => await containerEngine.RemoveContainerAsync(containerId, force, cancellationToken),
                _ => throw new ArgumentException($"Ação não suportada: {action}")
            };

            response.Success = success;
            response.Duration = DateTime.UtcNow - startTime;
            response.Message = success 
                ? $"Ação {action} executada com sucesso usando {containerEngine.EngineName}"
                : $"Falha ao executar ação {action}";

            if (!success)
            {
                response.Errors.Add($"A ação {action} falhou no container {containerId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar ação {Action} no container {ContainerId}", action, containerId);
            response.Success = false;
            response.Duration = DateTime.UtcNow - startTime;
            response.Message = $"Erro ao executar ação {action}";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    /// <summary>
    /// Mappers
    /// </summary>
    private Dictionary<string, string>? BuildFilters(ContainerFilters? filters)
    {
        if (filters == null) return null;

        var result = new Dictionary<string, string>();

        if (!string.IsNullOrEmpty(filters.Name))
            result["name"] = filters.Name;

        if (!string.IsNullOrEmpty(filters.Image))
            result["ancestor"] = filters.Image;

        if (filters.Status != null && filters.Status.Any())
            result["status"] = string.Join(",", filters.Status.Select(s => s.ToString().ToLowerInvariant()));

        return result.Any() ? result : null;
    }

    private ContainerRunOptions MapToRunOptions(ContainerRunRequest request)
    {
        return new ContainerRunOptions
        {
            Image = request.Image,
            Name = request.Name,
            Ports = request.Ports?.Select(MapPortFromDto).ToList() ?? new(),
            Volumes = request.Volumes?.Select(MapVolumeFromDto).ToList() ?? new(),
            Environment = request.Environment ?? new(),
            Command = request.Command,
            Args = request.Args?.ToArray() ?? Array.Empty<string>(),
            WorkingDir = request.WorkingDir,
            User = request.User,
            Detached = request.Detached,
            Interactive = request.Interactive,
            Tty = request.Tty,
            Remove = request.Remove,
            RestartPolicy = request.RestartPolicy,
            Resources = MapResourcesFromDto(request.Resources),
            NetworkMode = request.NetworkMode,
            Labels = request.Labels ?? new()
        };
    }

    private ContainerDto MapToDto(Container container)
    {
        return new ContainerDto
        {
            Id = container.ContainerId,
            Name = container.Name,
            Image = container.Image,
            ImageId = container.ImageId,
            Status = container.Status,
            State = container.State,
            Engine = container.Engine,
            Platform = container.Platform,
            Command = container.Command,
            Args = container.Args,
            WorkingDir = container.WorkingDir,
            User = container.User,
            NetworkMode = container.NetworkMode,
            RestartPolicy = container.RestartPolicy,
            CreatedAt = container.CreatedAt,
            StartedAt = container.StartedAt,
            FinishedAt = container.FinishedAt,
            ExitCode = container.ExitCode,
            Ports = container.Ports.Select(MapPortToDto).ToList(),
            Volumes = container.Volumes.Select(MapVolumeToDto).ToList(),
            Environment = container.Environment,
            Labels = container.Labels,
            Resources = MapResourcesToDto(container.Resources)
        };
    }

    private ContainerPort MapPortFromDto(ContainerPortDto dto)
    {
        return new ContainerPort
        {
            ContainerPortNumber = dto.ContainerPort,
            HostPort = dto.HostPort,
            HostIp = dto.HostIp,
            Protocol = dto.Protocol,
            Type = dto.Type
        };
    }

    private ContainerPortDto MapPortToDto(ContainerPort port)
    {
        return new ContainerPortDto
        {
            ContainerPort = port.ContainerPortNumber,
            HostPort = port.HostPort,
            HostIp = port.HostIp,
            Protocol = port.Protocol,
            Type = port.Type
        };
    }

    private ContainerVolume MapVolumeFromDto(ContainerVolumeDto dto)
    {
        return new ContainerVolume
        {
            Source = dto.Source,
            Destination = dto.Destination,
            Mode = dto.Mode,
            Type = dto.Type
        };
    }

    private ContainerVolumeDto MapVolumeToDto(ContainerVolume volume)
    {
        return new ContainerVolumeDto
        {
            Source = volume.Source,
            Destination = volume.Destination,
            Mode = volume.Mode,
            Type = volume.Type
        };
    }

    private ContainerResources? MapResourcesFromDto(ContainerResourcesDto? dto)
    {
        if (dto == null) return null;

        return new ContainerResources
        {
            Memory = dto.Memory,
            MemorySwap = dto.MemorySwap,
            CpuShares = dto.CpuShares,
            CpuQuota = dto.CpuQuota,
            CpuPeriod = dto.CpuPeriod,
            CpusetCpus = dto.CpusetCpus,
            CpusetMems = dto.CpusetMems,
            BlkioWeight = dto.BlkioWeight,
            OomKillDisable = dto.OomKillDisable
        };
    }

    private ContainerResourcesDto? MapResourcesToDto(ContainerResources? resources)
    {
        if (resources == null) return null;

        return new ContainerResourcesDto
        {
            Memory = resources.Memory,
            MemorySwap = resources.MemorySwap,
            CpuShares = resources.CpuShares,
            CpuQuota = resources.CpuQuota,
            CpuPeriod = resources.CpuPeriod,
            CpusetCpus = resources.CpusetCpus,
            CpusetMems = resources.CpusetMems,
            BlkioWeight = resources.BlkioWeight,
            OomKillDisable = resources.OomKillDisable
        };
    }

    private ContainerLogDto MapLogToDto(ContainerLog log)
    {
        return new ContainerLogDto
        {
            Timestamp = log.Timestamp,
            Stream = log.Stream,
            Message = log.Message,
            ContainerId = log.ContainerId
        };
    }

    private ContainerStatsDto MapStatsToDto(ContainerStats stats)
    {
        return new ContainerStatsDto
        {
            ContainerId = stats.ContainerId,
            Name = stats.Name,
            Cpu = new CpuStatsDto
            {
                Usage = stats.Cpu.Usage,
                SystemUsage = stats.Cpu.SystemUsage,
                OnlineCpus = stats.Cpu.OnlineCpus,
                ThrottledTime = stats.Cpu.ThrottledTime
            },
            Memory = new MemoryStatsDto
            {
                Usage = stats.Memory.Usage,
                Limit = stats.Memory.Limit,
                Percentage = stats.Memory.Percentage,
                Cache = stats.Memory.Cache,
                Swap = stats.Memory.Swap
            },
            Network = new NetworkStatsDto
            {
                RxBytes = stats.Network.RxBytes,
                TxBytes = stats.Network.TxBytes,
                RxPackets = stats.Network.RxPackets,
                TxPackets = stats.Network.TxPackets,
                RxErrors = stats.Network.RxErrors,
                TxErrors = stats.Network.TxErrors
            },
            BlockIO = new BlockIOStatsDto
            {
                ReadBytes = stats.BlockIO.ReadBytes,
                WriteBytes = stats.BlockIO.WriteBytes,
                ReadOps = stats.BlockIO.ReadOps,
                WriteOps = stats.BlockIO.WriteOps
            },
            Pids = stats.Pids,
            Timestamp = stats.Timestamp
        };
    }
}
