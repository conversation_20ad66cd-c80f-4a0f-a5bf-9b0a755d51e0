/**
 * Container Stats Component
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para exibição de estatísticas de container em tempo real
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  CpuChipIcon,
  CircleStackIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { 
  useContainerStats, 
  formatBytes, 
  formatPercentage 
} from '../../../services/container-service';
import type { 
  Container, 
  ContainerEngine,
  ContainerStats as ContainerStatsType 
} from '../../../../../shared/types/api.types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContainerStatsProps {
  containerId: string;
  containerName?: string;
  engine?: ContainerEngine;
  className?: string;
  refreshInterval?: number;
}

interface StatCardProps {
  title: string;
  value: string;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  trend?: 'up' | 'down' | 'stable';
  isLoading?: boolean;
}

// ============================================================================
// STAT CARD COMPONENT
// ============================================================================

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  color,
  trend,
  isLoading = false
}) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
    red: 'bg-red-50 text-red-600 border-red-200',
    purple: 'bg-purple-50 text-purple-600 border-purple-200'
  };

  const trendIcons = {
    up: ArrowUpIcon,
    down: ArrowDownIcon,
    stable: null
  };

  const TrendIcon = trend ? trendIcons[trend] : null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`relative overflow-hidden rounded-lg border p-4 ${colorClasses[color]}`}
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4 flex-1">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            {TrendIcon && (
              <TrendIcon className={`h-4 w-4 ${
                trend === 'up' ? 'text-red-500' : 
                trend === 'down' ? 'text-green-500' : 
                'text-gray-400'
              }`} />
            )}
          </div>
          <div className="mt-1">
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            ) : (
              <p className="text-2xl font-semibold text-gray-900">{value}</p>
            )}
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// ============================================================================
// PROGRESS BAR COMPONENT
// ============================================================================

interface ProgressBarProps {
  label: string;
  value: number;
  max: number;
  color: 'blue' | 'green' | 'yellow' | 'red';
  showPercentage?: boolean;
  formatValue?: (value: number) => string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  value,
  max,
  color,
  showPercentage = true,
  formatValue
}) => {
  const percentage = max > 0 ? (value / max) * 100 : 0;
  
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between text-sm">
        <span className="font-medium text-gray-700">{label}</span>
        <span className="text-gray-500">
          {formatValue ? formatValue(value) : formatBytes(value)}
          {showPercentage && ` (${formatPercentage(percentage)})`}
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${Math.min(percentage, 100)}%` }}
          transition={{ duration: 0.5 }}
          className={`h-2 rounded-full ${colorClasses[color]}`}
        />
      </div>
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const ContainerStats: React.FC<ContainerStatsProps> = ({
  containerId,
  containerName,
  engine,
  className = '',
  refreshInterval = 5000
}) => {
  // Query
  const { 
    data: statsResponse, 
    isLoading, 
    error,
    isRefetching 
  } = useContainerStats(containerId, engine, {
    refetchInterval: refreshInterval,
    enabled: !!containerId
  });

  // Memoized stats
  const stats = useMemo(() => {
    return statsResponse?.stats;
  }, [statsResponse]);

  // Calculate network total
  const networkTotal = useMemo(() => {
    if (!stats) return { rx: 0, tx: 0 };
    return {
      rx: stats.network.rxBytes,
      tx: stats.network.txBytes
    };
  }, [stats]);

  // Calculate disk total
  const diskTotal = useMemo(() => {
    if (!stats) return { read: 0, write: 0 };
    return {
      read: stats.blockIO.readBytes,
      write: stats.blockIO.writeBytes
    };
  }, [stats]);

  // Error state
  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Erro ao carregar estatísticas
            </h3>
            <p className="mt-1 text-sm text-red-700">{error.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">
            Estatísticas do Container
          </h3>
          {containerName && (
            <p className="text-sm text-gray-600">{containerName}</p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {isRefetching && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
          <ClockIcon className="h-4 w-4 text-gray-400" />
          <span className="text-xs text-gray-500">
            Atualiza a cada {refreshInterval / 1000}s
          </span>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="CPU"
          value={stats ? formatPercentage(stats.cpu.usage) : '0%'}
          subtitle={stats ? `${stats.cpu.onlineCpus} cores` : undefined}
          icon={CpuChipIcon}
          color="blue"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Memória"
          value={stats ? formatPercentage(stats.memory.percentage) : '0%'}
          subtitle={stats ? `${formatBytes(stats.memory.usage)} / ${formatBytes(stats.memory.limit)}` : undefined}
          icon={CircleStackIcon}
          color="green"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Rede RX"
          value={stats ? formatBytes(networkTotal.rx) : '0 B'}
          subtitle="Dados recebidos"
          icon={ArrowDownIcon}
          color="purple"
          isLoading={isLoading}
        />
        
        <StatCard
          title="Rede TX"
          value={stats ? formatBytes(networkTotal.tx) : '0 B'}
          subtitle="Dados enviados"
          icon={ArrowUpIcon}
          color="yellow"
          isLoading={isLoading}
        />
      </div>

      {/* Detailed Stats */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Memory Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-4">Uso de Memória</h4>
            <div className="space-y-4">
              <ProgressBar
                label="Memória Usada"
                value={stats.memory.usage}
                max={stats.memory.limit}
                color="green"
              />
              <ProgressBar
                label="Cache"
                value={stats.memory.cache}
                max={stats.memory.limit}
                color="blue"
              />
              {stats.memory.swap && (
                <ProgressBar
                  label="Swap"
                  value={stats.memory.swap}
                  max={stats.memory.limit}
                  color="yellow"
                />
              )}
            </div>
          </div>

          {/* Network & Disk Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-4">I/O de Rede e Disco</h4>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-gray-700">Rede</p>
                  <p className="text-gray-600">RX: {formatBytes(stats.network.rxBytes)}</p>
                  <p className="text-gray-600">TX: {formatBytes(stats.network.txBytes)}</p>
                  <p className="text-gray-600">Pacotes RX: {stats.network.rxPackets.toLocaleString()}</p>
                  <p className="text-gray-600">Pacotes TX: {stats.network.txPackets.toLocaleString()}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Disco</p>
                  <p className="text-gray-600">Leitura: {formatBytes(stats.blockIO.readBytes)}</p>
                  <p className="text-gray-600">Escrita: {formatBytes(stats.blockIO.writeBytes)}</p>
                  <p className="text-gray-600">Ops Leitura: {stats.blockIO.readOps.toLocaleString()}</p>
                  <p className="text-gray-600">Ops Escrita: {stats.blockIO.writeOps.toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Additional Info */}
      {stats && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700">PIDs</p>
              <p className="text-gray-600">{stats.pids}</p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Última Atualização</p>
              <p className="text-gray-600">
                {new Date(stats.timestamp).toLocaleTimeString()}
              </p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Container ID</p>
              <p className="text-gray-600 font-mono text-xs">
                {stats.containerId.substring(0, 12)}...
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
