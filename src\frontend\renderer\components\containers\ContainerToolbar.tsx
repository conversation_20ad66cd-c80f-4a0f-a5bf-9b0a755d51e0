/**
 * Container Toolbar - Barra de ferramentas para filtros e busca
 * Auto-Instalador V3 Lite
 * 
 * @description Toolbar com controles de busca, filtros e ações para containers
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import type { ContainerEngine, ContainerStatus } from '../../../../shared/types/api.types';

interface ContainerToolbarProps {
  searchTerm: string;
  statusFilter: ContainerStatus | 'all';
  selectedEngine: ContainerEngine;
  onSearch: (term: string) => void;
  onStatusFilter: (status: ContainerStatus | 'all') => void;
  onEngineChange: (engine: ContainerEngine) => void;
  className?: string;
}

export const ContainerToolbar: React.FC<ContainerToolbarProps> = ({
  searchTerm,
  statusFilter,
  selectedEngine,
  onSearch,
  onStatusFilter,
  onEngineChange,
  className = ''
}) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Opções de filtro de status
  const statusOptions = [
    { value: 'all' as const, label: 'Todos os status', icon: '📋' },
    { value: 'running' as const, label: 'Executando', icon: '🟢' },
    { value: 'exited' as const, label: 'Parado', icon: '🔴' },
    { value: 'paused' as const, label: 'Pausado', icon: '🟡' },
    { value: 'restarting' as const, label: 'Reiniciando', icon: '🔄' },
    { value: 'created' as const, label: 'Criado', icon: '🆕' }
  ];

  // Handler para busca com debounce
  const handleSearchChange = (value: string) => {
    setLocalSearchTerm(value);
    // Debounce simples
    const timeoutId = setTimeout(() => {
      onSearch(value);
    }, 300);
    
    return () => clearTimeout(timeoutId);
  };

  // Handler para mudança de status
  const handleStatusChange = (status: ContainerStatus | 'all') => {
    onStatusFilter(status);
  };

  // Handler para mudança de engine
  const handleEngineChange = (engine: ContainerEngine) => {
    onEngineChange(engine);
  };

  return (
    <div className={`bg-gray-700 border-b border-gray-600 px-6 py-4 ${className}`}>
      <div className="flex items-center justify-between gap-4">
        {/* Lado esquerdo - Busca e filtros */}
        <div className="flex items-center gap-4 flex-1">
          {/* Campo de busca */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400 text-sm">🔍</span>
            </div>
            <input
              type="text"
              value={localSearchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Buscar containers por nome ou imagem..."
              className="
                w-full pl-10 pr-4 py-2 bg-gray-600 border border-gray-500 rounded-md
                text-white placeholder-gray-400 text-sm
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                transition-colors duration-200
              "
            />
            {localSearchTerm && (
              <button
                onClick={() => {
                  setLocalSearchTerm('');
                  onSearch('');
                }}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
              >
                <span className="text-sm">✕</span>
              </button>
            )}
          </div>

          {/* Filtro de status */}
          <div className="relative">
            <select
              value={statusFilter}
              onChange={(e) => handleStatusChange(e.target.value as ContainerStatus | 'all')}
              className="
                appearance-none bg-gray-600 border border-gray-500 rounded-md
                px-4 py-2 pr-8 text-white text-sm
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                transition-colors duration-200
              "
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.icon} {option.label}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <span className="text-gray-400 text-sm">▼</span>
            </div>
          </div>

          {/* Seletor de engine (compacto) */}
          <div className="flex items-center gap-2 bg-gray-600 rounded-md p-1">
            <button
              onClick={() => handleEngineChange('docker')}
              className={`
                px-3 py-1 rounded text-xs font-medium transition-colors duration-200
                ${selectedEngine === 'docker'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-500'
                }
              `}
            >
              🐳 Docker
            </button>
            <button
              onClick={() => handleEngineChange('podman')}
              className={`
                px-3 py-1 rounded text-xs font-medium transition-colors duration-200
                ${selectedEngine === 'podman'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-500'
                }
              `}
            >
              🦭 Podman
            </button>
          </div>
        </div>

        {/* Lado direito - Ações e informações */}
        <div className="flex items-center gap-4">
          {/* Contador de containers */}
          <div className="text-sm text-gray-400">
            <span className="font-medium text-white">
              {/* TODO: Implementar contador baseado nos containers filtrados */}
              0
            </span>
            {' '}containers
          </div>

          {/* Botão de atualizar */}
          <button
            onClick={() => {
              // TODO: Implementar refresh dos dados
              window.location.reload();
            }}
            className="
              p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded-md
              transition-colors duration-200
            "
            title="Atualizar lista"
          >
            <span className="text-sm">🔄</span>
          </button>

          {/* Botão de configurações */}
          <button
            onClick={() => {
              // TODO: Implementar modal de configurações
              alert('Configurações em desenvolvimento');
            }}
            className="
              p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded-md
              transition-colors duration-200
            "
            title="Configurações"
          >
            <span className="text-sm">⚙️</span>
          </button>
        </div>
      </div>

      {/* Filtros ativos */}
      {(searchTerm || statusFilter !== 'all') && (
        <div className="flex items-center gap-2 mt-3 pt-3 border-t border-gray-600">
          <span className="text-xs text-gray-400">Filtros ativos:</span>
          
          {searchTerm && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-600 text-white text-xs rounded">
              Busca: "{searchTerm}"
              <button
                onClick={() => onSearch('')}
                className="text-blue-200 hover:text-white"
              >
                ✕
              </button>
            </span>
          )}
          
          {statusFilter !== 'all' && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-600 text-white text-xs rounded">
              Status: {statusOptions.find(opt => opt.value === statusFilter)?.label}
              <button
                onClick={() => onStatusFilter('all')}
                className="text-blue-200 hover:text-white"
              >
                ✕
              </button>
            </span>
          )}
          
          {(searchTerm || statusFilter !== 'all') && (
            <button
              onClick={() => {
                onSearch('');
                onStatusFilter('all');
              }}
              className="text-xs text-gray-400 hover:text-white underline"
            >
              Limpar todos
            </button>
          )}
        </div>
      )}
    </div>
  );
};
