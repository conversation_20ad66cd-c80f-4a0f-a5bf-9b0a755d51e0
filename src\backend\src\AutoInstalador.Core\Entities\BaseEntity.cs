namespace AutoInstalador.Core.Entities;

/// <summary>
/// Entidade base com propriedades comuns
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Identificador único
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime Created { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime? Updated { get; set; }

    /// <summary>
    /// Versão para controle de concorrência
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Indica se a entidade está ativa
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Metadados adicionais
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
