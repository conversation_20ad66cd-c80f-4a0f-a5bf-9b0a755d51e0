/**
 * Schemas de validação compartilhados
 * Auto-Instalador V3 Lite
 * 
 * @description Schemas Zod para validação de dados entre Frontend e Backend
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import { z } from 'zod';

// ============================================================================
// BASE SCHEMAS
// ============================================================================

export const BaseResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  message: z.string().optional(),
  errors: z.array(z.string()).optional(),
  timestamp: z.string().datetime()
});

export const PaginationSchema = z.object({
  page: z.number().int().min(1),
  pageSize: z.number().int().min(1).max(100),
  totalItems: z.number().int().min(0),
  totalPages: z.number().int().min(0),
  hasNext: z.boolean(),
  hasPrevious: z.boolean()
});

export const PaginatedResponseSchema = BaseResponseSchema.extend({
  pagination: PaginationSchema
});

// ============================================================================
// ENUM SCHEMAS
// ============================================================================

export const PackageCategorySchema = z.enum([
  'development',
  'productivity', 
  'media',
  'gaming',
  'security',
  'utilities',
  'communication',
  'education'
]);

export const PackageStatusSchema = z.enum([
  'available',
  'installing',
  'installed',
  'failed',
  'updating',
  'uninstalling'
]);

export const InstallationStatusSchema = z.enum([
  'queued',
  'downloading',
  'installing',
  'completed',
  'failed',
  'cancelled'
]);

export const LogLevelSchema = z.enum([
  'debug',
  'info',
  'warning',
  'error'
]);

export const OperatingSystemSchema = z.enum([
  'windows10',
  'windows11',
  'ubuntu',
  'macos'
]);

// ============================================================================
// SYSTEM SCHEMAS
// ============================================================================

export const SystemRequirementsSchema = z.object({
  minRam: z.number().int().min(0),
  minStorage: z.number().int().min(0),
  supportedOs: z.array(OperatingSystemSchema),
  requiredFeatures: z.array(z.string())
});

export const StorageDriveSchema = z.object({
  letter: z.string().length(1),
  type: z.enum(['HDD', 'SSD', 'USB', 'Network']),
  totalSize: z.number().int().min(0),
  availableSize: z.number().int().min(0),
  usage: z.number().min(0).max(100)
});

export const SystemInfoSchema = z.object({
  os: z.object({
    name: z.string().min(1),
    version: z.string().min(1),
    architecture: z.string().min(1),
    platform: z.string().min(1)
  }),
  hardware: z.object({
    cpu: z.object({
      model: z.string().min(1),
      cores: z.number().int().min(1),
      threads: z.number().int().min(1),
      frequency: z.number().min(0)
    }),
    memory: z.object({
      total: z.number().int().min(0),
      available: z.number().int().min(0),
      usage: z.number().min(0).max(100)
    }),
    storage: z.object({
      drives: z.array(StorageDriveSchema),
      totalSpace: z.number().int().min(0),
      availableSpace: z.number().int().min(0)
    })
  }),
  runtime: z.object({
    nodeVersion: z.string().min(1),
    electronVersion: z.string().min(1),
    dotnetVersion: z.string().min(1)
  })
});

// ============================================================================
// PACKAGE SCHEMAS
// ============================================================================

export const PackageSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(100),
  displayName: z.string().min(1).max(200),
  description: z.string().min(1).max(1000),
  version: z.string().regex(/^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/),
  category: PackageCategorySchema,
  status: PackageStatusSchema,
  size: z.number().int().min(0),
  downloadUrl: z.string().url(),
  installCommand: z.string().min(1),
  dependencies: z.array(z.string()),
  tags: z.array(z.string().min(1).max(50)),
  publisher: z.string().min(1).max(100),
  publishedDate: z.string().datetime(),
  lastUpdated: z.string().datetime(),
  isRequired: z.boolean(),
  isRecommended: z.boolean(),
  systemRequirements: SystemRequirementsSchema
});

export const SearchPackagesRequestSchema = z.object({
  query: z.string().max(200).optional(),
  category: PackageCategorySchema.optional(),
  tags: z.array(z.string().min(1).max(50)).optional(),
  page: z.number().int().min(1).default(1),
  pageSize: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['name', 'popularity', 'date', 'size']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

// ============================================================================
// INSTALLATION SCHEMAS
// ============================================================================

export const InstallationLogSchema = z.object({
  timestamp: z.string().datetime(),
  level: LogLevelSchema,
  message: z.string().min(1),
  details: z.record(z.any()).optional()
});

export const InstallationSchema = z.object({
  id: z.string().uuid(),
  packageId: z.string().uuid(),
  status: InstallationStatusSchema,
  progress: z.number().min(0).max(100),
  startedAt: z.string().datetime(),
  completedAt: z.string().datetime().optional(),
  errorMessage: z.string().optional(),
  logs: z.array(InstallationLogSchema),
  estimatedTimeRemaining: z.number().int().min(0).optional()
});

export const InstallPackageRequestSchema = z.object({
  packageId: z.string().uuid(),
  installDependencies: z.boolean().default(true),
  customInstallPath: z.string().optional(),
  installOptions: z.record(z.any()).optional()
});

export const BatchInstallRequestSchema = z.object({
  packages: z.array(InstallPackageRequestSchema).min(1).max(50),
  continueOnError: z.boolean().default(false)
});

// ============================================================================
// CONFIGURATION SCHEMAS
// ============================================================================

export const AppConfigurationSchema = z.object({
  general: z.object({
    theme: z.enum(['light', 'dark', 'auto']).default('auto'),
    language: z.string().length(2).default('pt'),
    autoStart: z.boolean().default(false),
    minimizeToTray: z.boolean().default(true)
  }),
  installation: z.object({
    defaultPath: z.string().min(1),
    parallelInstalls: z.number().int().min(1).max(10).default(3),
    retryAttempts: z.number().int().min(0).max(10).default(3),
    autoInstallDependencies: z.boolean().default(true),
    verifyChecksums: z.boolean().default(true)
  }),
  network: z.object({
    timeout: z.number().int().min(5).max(300).default(30),
    retryDelay: z.number().int().min(1).max(60).default(5),
    useProxy: z.boolean().default(false),
    proxyUrl: z.string().url().optional()
  }),
  logging: z.object({
    level: LogLevelSchema.default('info'),
    maxFileSize: z.number().int().min(1).max(100).default(10),
    maxFiles: z.number().int().min(1).max(50).default(5),
    enableConsole: z.boolean().default(true)
  })
});

export const ConfigurationRequestSchema = z.object({
  downloadPath: z.string().min(1),
  installPath: z.string().min(1),
  autoUpdate: z.boolean(),
  parallelInstalls: z.number().int().min(1).max(10),
  retryAttempts: z.number().int().min(0).max(10),
  logLevel: LogLevelSchema
});

// ============================================================================
// WEBSOCKET SCHEMAS
// ============================================================================

export const WebSocketMessageSchema = z.object({
  type: z.string().min(1),
  id: z.string().uuid().optional(),
  payload: z.any(),
  timestamp: z.string().datetime()
});

export const InstallationProgressUpdateSchema = z.object({
  installationId: z.string().uuid(),
  progress: z.number().min(0).max(100),
  status: InstallationStatusSchema,
  message: z.string().optional(),
  estimatedTimeRemaining: z.number().int().min(0).optional()
});

export const SystemMetricsUpdateSchema = z.object({
  cpuUsage: z.number().min(0).max(100),
  memoryUsage: z.number().min(0).max(100),
  diskUsage: z.number().min(0).max(100),
  networkActivity: z.boolean(),
  timestamp: z.string().datetime()
});

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export class ValidationHelper {
  
  static validatePackage(data: unknown) {
    return PackageSchema.safeParse(data);
  }
  
  static validateSearchRequest(data: unknown) {
    return SearchPackagesRequestSchema.safeParse(data);
  }
  
  static validateInstallRequest(data: unknown) {
    return InstallPackageRequestSchema.safeParse(data);
  }
  
  static validateBatchInstallRequest(data: unknown) {
    return BatchInstallRequestSchema.safeParse(data);
  }
  
  static validateConfiguration(data: unknown) {
    return AppConfigurationSchema.safeParse(data);
  }
  
  static validateSystemInfo(data: unknown) {
    return SystemInfoSchema.safeParse(data);
  }
  
  static validateWebSocketMessage(data: unknown) {
    return WebSocketMessageSchema.safeParse(data);
  }
  
  static getValidationErrors(result: z.SafeParseReturnType<any, any>) {
    if (result.success) return [];
    
    return result.error.errors.map(error => ({
      field: error.path.join('.'),
      message: error.message,
      code: error.code
    }));
  }
}

// ============================================================================
// CUSTOM VALIDATORS
// ============================================================================

export const CustomValidators = {
  
  // Validar se o caminho é válido para o sistema operacional
  isValidPath: (path: string, os: 'windows' | 'linux' | 'macos') => {
    const patterns = {
      windows: /^[a-zA-Z]:\\(?:[^<>:"/\\|?*]+\\)*[^<>:"/\\|?*]*$/,
      linux: /^\/(?:[^\/\0]+\/)*[^\/\0]*$/,
      macos: /^\/(?:[^\/\0]+\/)*[^\/\0]*$/
    };
    return patterns[os].test(path);
  },
  
  // Validar se a versão segue o padrão semver
  isValidSemver: (version: string) => {
    return /^\d+\.\d+\.\d+(-[a-zA-Z0-9]+)?$/.test(version);
  },
  
  // Validar se o UUID é válido
  isValidUuid: (uuid: string) => {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
  },
  
  // Validar se o tamanho do arquivo é razoável
  isValidFileSize: (size: number) => {
    return size >= 0 && size <= 10 * 1024 * 1024 * 1024; // 10GB max
  },
  
  // Validar se a URL de download é segura
  isSecureDownloadUrl: (url: string) => {
    try {
      const parsed = new URL(url);
      return parsed.protocol === 'https:' && 
             !parsed.hostname.includes('localhost') &&
             !parsed.hostname.includes('127.0.0.1');
    } catch {
      return false;
    }
  }
};

// ============================================================================
// EXPORTS
// ============================================================================

export type Package = z.infer<typeof PackageSchema>;
export type Installation = z.infer<typeof InstallationSchema>;
export type SystemInfo = z.infer<typeof SystemInfoSchema>;
export type AppConfiguration = z.infer<typeof AppConfigurationSchema>;
export type SearchPackagesRequest = z.infer<typeof SearchPackagesRequestSchema>;
export type InstallPackageRequest = z.infer<typeof InstallPackageRequestSchema>;
export type BatchInstallRequest = z.infer<typeof BatchInstallRequestSchema>;
export type ConfigurationRequest = z.infer<typeof ConfigurationRequestSchema>;
