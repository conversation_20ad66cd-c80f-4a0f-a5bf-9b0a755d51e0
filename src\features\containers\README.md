# 🐳 Container Management Feature

## 📋 **Resumo**

Sistema completo de gerenciamento de containers Docker e Podman integrado ao Auto-Instalador V3 Lite, oferecendo uma interface unificada para criação, monitoramento e controle de containers.

## 🚀 **Início Rápido**

### **1. Verificar Engines Disponíveis**

```typescript
import { useDetectEngines } from '@/components/features/containers';

function EngineStatus() {
  const { data: detection } = useDetectEngines();
  
  return (
    <div>
      {detection?.detectedEngines?.map(engine => (
        <div key={engine.engine}>
          {engine.engine}: {engine.isDetected ? '✅ Disponível' : '❌ Não encontrado'}
        </div>
      ))}
    </div>
  );
}
```

### **2. Listar Containers**

```typescript
import { useContainers } from '@/components/features/containers';

function ContainersList() {
  const { data: containers, isLoading } = useContainers({
    all: true, // Incluir containers parados
    filters: {
      status: ['running', 'paused'] // Filtrar por status
    }
  });

  if (isLoading) return <div>Carregando...</div>;

  return (
    <div>
      {containers?.containers?.map(container => (
        <div key={container.id}>
          {container.name} - {container.status}
        </div>
      ))}
    </div>
  );
}
```

### **3. Executar Novo Container**

```typescript
import { useRunContainer } from '@/components/features/containers';

function CreateContainer() {
  const runContainer = useRunContainer();

  const handleRunNginx = async () => {
    await runContainer.mutateAsync({
      image: 'nginx:latest',
      name: 'my-web-server',
      engine: 'docker',
      detached: true,
      ports: [
        { hostPort: 8080, containerPort: 80, protocol: 'tcp' }
      ]
    });
  };

  return (
    <button onClick={handleRunNginx} disabled={runContainer.isPending}>
      {runContainer.isPending ? 'Criando...' : 'Executar Nginx'}
    </button>
  );
}
```

### **4. Controlar Containers**

```typescript
import { useContainerAction } from '@/components/features/containers';

function ContainerControls({ container }) {
  const containerAction = useContainerAction();

  const handleStart = () => containerAction.mutateAsync({
    id: container.id,
    action: 'start',
    engine: container.engine
  });

  const handleStop = () => containerAction.mutateAsync({
    id: container.id,
    action: 'stop',
    engine: container.engine
  });

  return (
    <div>
      {container.status === 'exited' && (
        <button onClick={handleStart}>Iniciar</button>
      )}
      {container.status === 'running' && (
        <button onClick={handleStop}>Parar</button>
      )}
    </div>
  );
}
```

## 🎨 **Componentes Principais**

### **ContainerDashboard**
Dashboard completo com navegação e visões especializadas.

```typescript
import { ContainerDashboard } from '@/components/features/containers';

<ContainerDashboard className="h-full" />
```

### **ContainerList**
Lista de containers com filtros e ações rápidas.

```typescript
import { ContainerList } from '@/components/features/containers';

<ContainerList
  onCreateContainer={() => setShowModal(true)}
  onViewContainer={(container) => setSelected(container)}
/>
```

### **ContainerStats**
Estatísticas em tempo real com gráficos.

```typescript
import { ContainerStats } from '@/components/features/containers';

<ContainerStats
  containerId="container-id"
  engine="docker"
  refreshInterval={5000}
/>
```

### **ContainerLogs**
Visualização de logs com filtros e busca.

```typescript
import { ContainerLogs } from '@/components/features/containers';

<ContainerLogs
  containerId="container-id"
  engine="docker"
  autoScroll={true}
  maxLines={1000}
/>
```

## 🔧 **Configuração Avançada**

### **Customização de Queries**

```typescript
import { useContainers, containerQueryKeys } from '@/components/features/containers';

// Query customizada
const { data } = useContainers(
  { all: true },
  {
    refetchInterval: 10000, // 10 segundos
    staleTime: 5000,
    enabled: engineAvailable
  }
);

// Invalidação manual
const queryClient = useQueryClient();
queryClient.invalidateQueries({ 
  queryKey: containerQueryKeys.lists() 
});
```

### **Eventos em Tempo Real**

```typescript
import { useContainerEvents } from '@/components/features/containers';

function MyComponent() {
  // Setup automático de eventos
  useContainerEvents();

  // Ou setup manual
  useEffect(() => {
    const unsubscribe = window.containerAPI.onContainerStatusChanged((data) => {
      console.log(`Container ${data.containerId} mudou para ${data.status}`);
    });

    return unsubscribe;
  }, []);
}
```

## 🧪 **Testes**

### **Executar Testes**

```bash
# Testes unitários do backend
cd src/backend
dotnet test tests/AutoInstalador.UnitTests/Services/ContainerServiceTests.cs

# Testes de integração
dotnet test tests/AutoInstalador.IntegrationTests/Controllers/ContainersControllerTests.cs

# Testes do frontend
cd src/frontend
npm test -- containers
```

### **Exemplo de Teste**

```typescript
import { render, screen } from '@testing-library/react';
import { ContainerList } from '@/components/features/containers';

test('should render container list', async () => {
  // Mock API
  window.containerAPI.listContainers = vi.fn().mockResolvedValue({
    success: true,
    containers: [mockContainer],
    totalCount: 1
  });

  render(<ContainerList />);

  await waitFor(() => {
    expect(screen.getByText('nginx-web')).toBeInTheDocument();
  });
});
```

## 🔍 **Troubleshooting**

### **Problemas Comuns**

#### **Engine não detectado**
```typescript
// Verificar se Docker/Podman está instalado
const { data: detection } = useDetectEngines();
console.log(detection?.detectedEngines);

// Instalar engine se necessário
const installEngine = useInstallEngine();
await installEngine.mutateAsync({
  engine: 'docker',
  platform: 'windows',
  packageManager: 'winget'
});
```

#### **Container não inicia**
```typescript
// Verificar logs para diagnóstico
const { data: logs } = useContainerLogs({
  containerId: 'container-id',
  tail: 50,
  timestamps: true
});
```

#### **Permissões no Linux**
```bash
# Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER

# Ou usar Podman rootless
podman run --rm hello-world
```

## 📊 **Monitoramento**

### **Métricas Disponíveis**
- **CPU:** Uso percentual e número de cores
- **Memória:** Uso, limite, cache
- **Rede:** Bytes e pacotes RX/TX
- **Disco:** Operações de I/O
- **Processos:** Número de PIDs

### **Health Checks**
- `/health` - Status geral da API
- `/health/ready` - Engines de container disponíveis
- `/health/live` - Liveness probe

## 🔗 **Integração**

### **Com Electron**
```typescript
// main.ts
import { ContainerHandlers } from './handlers/container-handlers';

const containerHandlers = new ContainerHandlers(apiClient);
```

### **Com React Query**
```typescript
// App.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ContainerDashboard />
    </QueryClientProvider>
  );
}
```

## 📚 **Recursos Adicionais**

- [Documentação Completa](../../docs/features/CONTAINERS.md)
- [Exemplos de Uso](../../examples/containers/)
- [API Reference](../../docs/api/containers.md)
- [Troubleshooting Guide](../../docs/troubleshooting/containers.md)

---

**Desenvolvido com ❤️ para o Auto-Instalador V3 Lite**
