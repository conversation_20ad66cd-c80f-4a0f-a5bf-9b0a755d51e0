using AutoInstalador.Core.Interfaces.Services;
using AutoInstalador.Infrastructure.External.Containers;
using AutoInstalador.Infrastructure.Extensions;
using Microsoft.OpenApi.Models;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// ============================================================================
// SERVICES CONFIGURATION
// ============================================================================

// Add controllers
builder.Services.AddControllers();

// Add API Explorer
builder.Services.AddEndpointsApiExplorer();

// Add Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Auto-Instalador V3 Lite API",
        Version = "v1",
        Description = "API para gerenciamento de containers e instalação de software",
        Contact = new OpenApiContact
        {
            Name = "Auto-Instalador Team",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("ElectronApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:5173", "app://.")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

// Add logging
builder.Services.AddLogging(logging =>
{
    logging.ClearProviders();
    logging.AddConsole();
    logging.AddDebug();
    
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

// ============================================================================
// CONTAINER SERVICES REGISTRATION
// ============================================================================

// Add container services using extension method
builder.Services.AddContainerServices();

// ============================================================================
// HTTP CLIENT CONFIGURATION
// ============================================================================

builder.Services.AddHttpClient("DefaultClient", client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "AutoInstalador-V3-Lite/1.0");
});

// ============================================================================
// HEALTH CHECKS
// ============================================================================

builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

// Add container health checks
builder.Services.AddContainerHealthChecks();

// ============================================================================
// BUILD APPLICATION
// ============================================================================

var app = builder.Build();

// ============================================================================
// MIDDLEWARE PIPELINE
// ============================================================================

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Auto-Instalador V3 Lite API v1");
        c.RoutePrefix = "swagger";
        c.DisplayRequestDuration();
        c.EnableDeepLinking();
        c.EnableFilter();
        c.ShowExtensions();
    });
}

// Security headers
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");
    
    await next();
});

// CORS
app.UseCors("ElectronApp");

// HTTPS Redirection (only in production)
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Authentication & Authorization (if needed in future)
// app.UseAuthentication();
// app.UseAuthorization();

// Controllers
app.MapControllers();

// Health checks
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});
app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = _ => false
});

// ============================================================================
// STARTUP LOGGING
// ============================================================================

var logger = app.Services.GetRequiredService<ILogger<Program>>();

logger.LogInformation("🚀 Auto-Instalador V3 Lite API iniciando...");
logger.LogInformation("🌍 Ambiente: {Environment}", app.Environment.EnvironmentName);
logger.LogInformation("📍 URLs: {Urls}", string.Join(", ", builder.WebHost.GetSetting("urls")?.Split(';') ?? new[] { "http://localhost:5000" }));

// ============================================================================
// RUN APPLICATION
// ============================================================================

try
{
    await app.RunAsync();
}
catch (Exception ex)
{
    logger.LogCritical(ex, "❌ Erro fatal ao iniciar a aplicação");
    throw;
}
finally
{
    logger.LogInformation("🛑 Auto-Instalador V3 Lite API finalizada");
}


