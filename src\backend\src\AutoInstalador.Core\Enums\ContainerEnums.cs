namespace AutoInstalador.Core.Enums;

/// <summary>
/// Tipos de engines de container
/// </summary>
public enum ContainerEngine
{
    /// <summary>
    /// Docker Engine
    /// </summary>
    Docker = 1,

    /// <summary>
    /// Podman Engine
    /// </summary>
    Podman = 2
}

/// <summary>
/// Status de um container
/// </summary>
public enum ContainerStatus
{
    /// <summary>
    /// Container criado mas não iniciado
    /// </summary>
    Created = 1,

    /// <summary>
    /// Container em execução
    /// </summary>
    Running = 2,

    /// <summary>
    /// Container pausado
    /// </summary>
    Paused = 3,

    /// <summary>
    /// Container reiniciando
    /// </summary>
    Restarting = 4,

    /// <summary>
    /// Container sendo removido
    /// </summary>
    Removing = 5,

    /// <summary>
    /// Container morto
    /// </summary>
    Dead = 6,

    /// <summary>
    /// Container parado
    /// </summary>
    Exited = 7
}

/// <summary>
/// Estado de um container
/// </summary>
public enum ContainerState
{
    /// <summary>
    /// Container criado
    /// </summary>
    Created = 1,

    /// <summary>
    /// Container em execução
    /// </summary>
    Running = 2,

    /// <summary>
    /// Container pausado
    /// </summary>
    Paused = 3,

    /// <summary>
    /// Container reiniciando
    /// </summary>
    Restarting = 4,

    /// <summary>
    /// Container sendo removido
    /// </summary>
    Removing = 5,

    /// <summary>
    /// Container morto
    /// </summary>
    Dead = 6,

    /// <summary>
    /// Container parado
    /// </summary>
    Exited = 7,

    /// <summary>
    /// Estado desconhecido
    /// </summary>
    Unknown = 8
}

/// <summary>
/// Políticas de reinicialização de container
/// </summary>
public enum RestartPolicy
{
    /// <summary>
    /// Não reiniciar automaticamente
    /// </summary>
    No = 1,

    /// <summary>
    /// Sempre reiniciar
    /// </summary>
    Always = 2,

    /// <summary>
    /// Reiniciar apenas em caso de falha
    /// </summary>
    OnFailure = 3,

    /// <summary>
    /// Reiniciar a menos que seja parado manualmente
    /// </summary>
    UnlessStopped = 4
}

/// <summary>
/// Tipos de volume de container
/// </summary>
public enum VolumeType
{
    /// <summary>
    /// Bind mount (diretório do host)
    /// </summary>
    Bind = 1,

    /// <summary>
    /// Volume gerenciado
    /// </summary>
    Volume = 2,

    /// <summary>
    /// Filesystem temporário em memória
    /// </summary>
    Tmpfs = 3
}

/// <summary>
/// Modos de acesso a volume
/// </summary>
public enum VolumeMode
{
    /// <summary>
    /// Somente leitura
    /// </summary>
    ReadOnly = 1,

    /// <summary>
    /// Leitura e escrita
    /// </summary>
    ReadWrite = 2
}

/// <summary>
/// Protocolos de rede
/// </summary>
public enum NetworkProtocol
{
    /// <summary>
    /// Protocolo TCP
    /// </summary>
    Tcp = 1,

    /// <summary>
    /// Protocolo UDP
    /// </summary>
    Udp = 2
}

/// <summary>
/// Tipos de porta de container
/// </summary>
public enum PortType
{
    /// <summary>
    /// Porta publicada (mapeada para o host)
    /// </summary>
    Published = 1,

    /// <summary>
    /// Porta apenas exposta
    /// </summary>
    Exposed = 2
}

/// <summary>
/// Ações de container
/// </summary>
public enum ContainerAction
{
    /// <summary>
    /// Iniciar container
    /// </summary>
    Start = 1,

    /// <summary>
    /// Parar container
    /// </summary>
    Stop = 2,

    /// <summary>
    /// Reiniciar container
    /// </summary>
    Restart = 3,

    /// <summary>
    /// Pausar container
    /// </summary>
    Pause = 4,

    /// <summary>
    /// Despausar container
    /// </summary>
    Unpause = 5,

    /// <summary>
    /// Remover container
    /// </summary>
    Remove = 6,

    /// <summary>
    /// Matar container
    /// </summary>
    Kill = 7
}

/// <summary>
/// Streams de log de container
/// </summary>
public enum LogStream
{
    /// <summary>
    /// Saída padrão
    /// </summary>
    Stdout = 1,

    /// <summary>
    /// Saída de erro
    /// </summary>
    Stderr = 2
}

/// <summary>
/// Plataformas de sistema operacional
/// </summary>
public enum Platform
{
    /// <summary>
    /// Windows
    /// </summary>
    Windows = 1,

    /// <summary>
    /// Linux
    /// </summary>
    Linux = 2,

    /// <summary>
    /// macOS
    /// </summary>
    MacOS = 3
}

/// <summary>
/// Gerenciadores de pacotes
/// </summary>
public enum PackageManager
{
    /// <summary>
    /// Windows Package Manager (winget)
    /// </summary>
    Winget = 1,

    /// <summary>
    /// Chocolatey
    /// </summary>
    Chocolatey = 2,

    /// <summary>
    /// Homebrew (macOS/Linux)
    /// </summary>
    Homebrew = 3,

    /// <summary>
    /// APT (Debian/Ubuntu)
    /// </summary>
    Apt = 4,

    /// <summary>
    /// DNF (Fedora/RHEL)
    /// </summary>
    Dnf = 5,

    /// <summary>
    /// YUM (CentOS/RHEL)
    /// </summary>
    Yum = 6,

    /// <summary>
    /// Pacman (Arch Linux)
    /// </summary>
    Pacman = 7,

    /// <summary>
    /// Zypper (openSUSE)
    /// </summary>
    Zypper = 8
}

/// <summary>
/// Status de instalação de engine
/// </summary>
public enum EngineInstallStatus
{
    /// <summary>
    /// Não instalado
    /// </summary>
    NotInstalled = 1,

    /// <summary>
    /// Instalado mas não em execução
    /// </summary>
    Installed = 2,

    /// <summary>
    /// Instalado e em execução
    /// </summary>
    Running = 3,

    /// <summary>
    /// Erro na instalação
    /// </summary>
    Error = 4,

    /// <summary>
    /// Instalação em progresso
    /// </summary>
    Installing = 5
}
