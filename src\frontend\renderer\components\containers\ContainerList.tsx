/**
 * Container List - Lista detalhada de containers
 * Auto-Instalador V3 Lite
 * 
 * @description Página de lista de containers com visualização em tabela
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { useContainers, useContainerAction } from '../../services/container-service';
import { ContainerToolbar } from './ContainerToolbar';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { ErrorMessage } from '../common/ErrorMessage';
import { formatDateTime, formatBytes, formatUptime } from '../../utils/format';
import { toast } from 'react-hot-toast';
import type { Container, ContainerEngine, ContainerStatus } from '../../../../shared/types/api.types';

interface ContainerListProps {
  className?: string;
}

export const ContainerList: React.FC<ContainerListProps> = ({ className = '' }) => {
  // Estados locais
  const [selectedEngine, setSelectedEngine] = useState<ContainerEngine>('docker');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ContainerStatus | 'all'>('all');
  const [selectedContainers, setSelectedContainers] = useState<Set<string>>(new Set());
  const [sortField, setSortField] = useState<keyof Container>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Hooks de dados
  const { 
    data: containers, 
    isLoading: containersLoading, 
    error: containersError,
    refetch: refetchContainers
  } = useContainers({
    all: true,
    engine: selectedEngine,
    filters: {
      status: statusFilter !== 'all' ? [statusFilter] : undefined,
      name: searchTerm || undefined
    }
  });

  const containerAction = useContainerAction();

  // Filtrar e ordenar containers
  const processedContainers = React.useMemo(() => {
    if (!containers) return [];
    
    let filtered = containers.filter(container => {
      const matchesSearch = !searchTerm || 
        container.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        container.image.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });

    // Ordenar
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === 'asc' ? comparison : -comparison;
      }
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [containers, searchTerm, statusFilter, sortField, sortDirection]);

  // Handlers
  const handleSort = (field: keyof Container) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSelectContainer = (containerId: string) => {
    const newSelected = new Set(selectedContainers);
    if (newSelected.has(containerId)) {
      newSelected.delete(containerId);
    } else {
      newSelected.add(containerId);
    }
    setSelectedContainers(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedContainers.size === processedContainers.length) {
      setSelectedContainers(new Set());
    } else {
      setSelectedContainers(new Set(processedContainers.map(c => c.id)));
    }
  };

  const handleBulkAction = async (action: 'start' | 'stop' | 'restart' | 'remove') => {
    if (selectedContainers.size === 0) {
      toast.error('Selecione pelo menos um container');
      return;
    }

    const confirmMessage = `Tem certeza que deseja ${
      action === 'start' ? 'iniciar' :
      action === 'stop' ? 'parar' :
      action === 'restart' ? 'reiniciar' : 'remover'
    } ${selectedContainers.size} container(s)?`;

    if (!confirm(confirmMessage)) return;

    try {
      const promises = Array.from(selectedContainers).map(containerId =>
        containerAction.mutateAsync({
          containerId,
          action,
          engine: selectedEngine
        })
      );

      await Promise.all(promises);
      
      toast.success(`${selectedContainers.size} container(s) ${
        action === 'start' ? 'iniciado(s)' :
        action === 'stop' ? 'parado(s)' :
        action === 'restart' ? 'reiniciado(s)' : 'removido(s)'
      } com sucesso`);
      
      setSelectedContainers(new Set());
      refetchContainers();
    } catch (error) {
      toast.error(`Erro na ação em lote: ${error}`);
    }
  };

  // Determinar cor do status
  const getStatusColor = (status: ContainerStatus): string => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-400/10';
      case 'exited': return 'text-red-400 bg-red-400/10';
      case 'paused': return 'text-yellow-400 bg-yellow-400/10';
      case 'restarting': return 'text-blue-400 bg-blue-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  // Renderizar ícone de ordenação
  const renderSortIcon = (field: keyof Container) => {
    if (sortField !== field) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className={`flex flex-col h-full bg-gray-800 ${className}`}>
      {/* Toolbar */}
      <ContainerToolbar
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        selectedEngine={selectedEngine}
        onSearch={setSearchTerm}
        onStatusFilter={setStatusFilter}
        onEngineChange={setSelectedEngine}
      />

      {/* Ações em lote */}
      {selectedContainers.size > 0 && (
        <div className="bg-blue-900/20 border-b border-blue-500/50 px-6 py-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-400">
              {selectedContainers.size} container(s) selecionado(s)
            </span>
            <div className="flex gap-2">
              <button
                onClick={() => handleBulkAction('start')}
                className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
              >
                Iniciar
              </button>
              <button
                onClick={() => handleBulkAction('stop')}
                className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
              >
                Parar
              </button>
              <button
                onClick={() => handleBulkAction('restart')}
                className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Reiniciar
              </button>
              <button
                onClick={() => handleBulkAction('remove')}
                className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
              >
                Remover
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Conteúdo principal */}
      <div className="flex-1 overflow-hidden">
        {containersLoading && (
          <div className="flex items-center justify-center h-full">
            <LoadingSpinner size="lg" text="Carregando containers..." />
          </div>
        )}

        {containersError && (
          <div className="p-6">
            <ErrorMessage 
              message="Erro ao carregar containers"
              details={containersError.message}
              onRetry={refetchContainers}
            />
          </div>
        )}

        {!containersLoading && !containersError && (
          <div className="h-full overflow-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-700 border-b border-gray-600 sticky top-0">
                <tr>
                  <th className="w-12 px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedContainers.size === processedContainers.length && processedContainers.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-gray-600"
                    onClick={() => handleSort('name')}
                  >
                    Nome {renderSortIcon('name')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-gray-600"
                    onClick={() => handleSort('image')}
                  >
                    Imagem {renderSortIcon('image')}
                  </th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-gray-600"
                    onClick={() => handleSort('status')}
                  >
                    Status {renderSortIcon('status')}
                  </th>
                  <th className="px-4 py-3 text-left">Portas</th>
                  <th 
                    className="px-4 py-3 text-left cursor-pointer hover:bg-gray-600"
                    onClick={() => handleSort('createdAt')}
                  >
                    Criado {renderSortIcon('createdAt')}
                  </th>
                  <th className="px-4 py-3 text-left">Ações</th>
                </tr>
              </thead>
              <tbody>
                {processedContainers.map((container) => (
                  <tr 
                    key={container.id}
                    className="border-b border-gray-700 hover:bg-gray-700/50"
                  >
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedContainers.has(container.id)}
                        onChange={() => handleSelectContainer(container.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-4 py-3 font-medium text-white">
                      {container.name}
                    </td>
                    <td className="px-4 py-3 font-mono text-gray-300 text-xs">
                      {container.image}
                    </td>
                    <td className="px-4 py-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(container.status)}`}>
                        {container.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-gray-300 text-xs">
                      {container.ports.length > 0 ? (
                        container.ports.slice(0, 2).map((port, i) => (
                          <div key={i} className="font-mono">
                            {port.hostPort ? `${port.hostPort}:` : ''}{port.containerPort}/{port.protocol}
                          </div>
                        ))
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                      {container.ports.length > 2 && (
                        <div className="text-gray-500">+{container.ports.length - 2} mais</div>
                      )}
                    </td>
                    <td className="px-4 py-3 text-gray-300 text-xs">
                      {formatDateTime(container.createdAt)}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex gap-1">
                        {container.status === 'exited' && (
                          <button
                            type="button"
                            onClick={() => containerAction.mutate({
                              containerId: container.id,
                              action: 'start',
                              engine: container.engine
                            })}
                            className="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                          >
                            ▶️
                          </button>
                        )}
                        {container.status === 'running' && (
                          <button
                            type="button"
                            onClick={() => containerAction.mutate({
                              containerId: container.id,
                              action: 'stop',
                              engine: container.engine
                            })}
                            className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                          >
                            ⏹️
                          </button>
                        )}
                        <button
                          type="button"
                          onClick={() => {
                            // TODO: Abrir logs
                            toast.info('Logs em desenvolvimento');
                          }}
                          className="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                        >
                          📄
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {processedContainers.length === 0 && (
              <div className="flex flex-col items-center justify-center py-16 text-gray-400">
                <div className="text-6xl mb-4">📦</div>
                <h3 className="text-lg font-medium mb-2">Nenhum container encontrado</h3>
                <p className="text-sm text-center max-w-md">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Tente ajustar os filtros de busca ou status.'
                    : 'Crie seu primeiro container para começar.'
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
