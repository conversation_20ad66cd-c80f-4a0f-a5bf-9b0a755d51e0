/**
 * Format Utilities - Utilitários de formatação
 * Auto-Instalador V3 Lite
 * 
 * @description Funções utilitárias para formatação de dados
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

/**
 * Formata bytes em formato legível
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Formata porcentagem de CPU
 */
export const formatCpuPercent = (percent: number, decimals: number = 1): string => {
  return `${percent.toFixed(decimals)}%`;
};

/**
 * Formata tempo de uptime
 */
export const formatUptime = (startTime: string): string => {
  try {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  } catch {
    return 'N/A';
  }
};

/**
 * Formata data/hora em formato brasileiro
 */
export const formatDateTime = (dateTime: string): string => {
  try {
    const date = new Date(dateTime);
    return date.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch {
    return dateTime;
  }
};

/**
 * Formata apenas a data em formato brasileiro
 */
export const formatDate = (dateTime: string): string => {
  try {
    const date = new Date(dateTime);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return dateTime;
  }
};

/**
 * Formata apenas o horário
 */
export const formatTime = (dateTime: string): string => {
  try {
    const date = new Date(dateTime);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch {
    return dateTime;
  }
};

/**
 * Formata duração em milissegundos para formato legível
 */
export const formatDuration = (durationMs: number): string => {
  const seconds = Math.floor(durationMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * Formata número com separadores de milhares
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString('pt-BR');
};

/**
 * Trunca texto com reticências
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Formata status de container para exibição
 */
export const formatContainerStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    'running': 'Executando',
    'exited': 'Parado',
    'paused': 'Pausado',
    'restarting': 'Reiniciando',
    'removing': 'Removendo',
    'dead': 'Morto',
    'created': 'Criado'
  };
  
  return statusMap[status] || status;
};

/**
 * Formata nome de imagem Docker/Podman
 */
export const formatImageName = (imageName: string): { registry?: string; name: string; tag: string } => {
  // Exemplo: registry.com/namespace/image:tag
  const parts = imageName.split('/');
  const lastPart = parts[parts.length - 1];
  const [name, tag = 'latest'] = lastPart.split(':');
  
  if (parts.length > 1) {
    const registry = parts.length > 2 ? parts[0] : undefined;
    const namespace = parts.length > 2 ? parts.slice(1, -1).join('/') : parts[0];
    return {
      registry,
      name: `${namespace}/${name}`,
      tag
    };
  }
  
  return { name, tag };
};

/**
 * Formata porta de container
 */
export const formatPort = (port: { hostPort?: number; containerPort: number; protocol: string }): string => {
  if (port.hostPort) {
    return `${port.hostPort}:${port.containerPort}/${port.protocol}`;
  }
  return `${port.containerPort}/${port.protocol}`;
};

/**
 * Formata tamanho de arquivo para download/upload
 */
export const formatFileSize = (bytes: number): string => {
  return formatBytes(bytes, 1);
};

/**
 * Formata velocidade de rede
 */
export const formatNetworkSpeed = (bytesPerSecond: number): string => {
  const speed = formatBytes(bytesPerSecond, 1);
  return `${speed}/s`;
};
