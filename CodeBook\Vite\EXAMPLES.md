# Vite 5.4.2 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.4.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://vitejs.dev/
- **GitHub:** https://github.com/vitejs/vite
- **Documentação:** https://vitejs.dev/guide/
- **NPM/Package:** https://www.npmjs.com/package/vite
- **Fórum/Community:** https://github.com/vitejs/vite/discussions
- **Stack Overflow Tag:** `vite`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Configuração Completa para Electron + React**

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { rmSync } from 'fs';

export default defineConfig(({ command, mode }) => {
  const isProduction = mode === 'production';
  const isDevelopment = command === 'serve';

  // Limpar dist em builds
  if (command === 'build') {
    rmSync('dist/renderer', { recursive: true, force: true });
  }

  return {
    plugins: [
      react({
        jsxRuntime: 'automatic',
        jsxImportSource: 'react',
        fastRefresh: isDevelopment,
        babel: {
          plugins: [
            // Suporte para React 19.2 features
            ['babel-plugin-react-compiler', { target: '19' }]
          ]
        }
      }),

      // Plugin customizado para Electron
      {
        name: 'electron-dev',
        configureServer(server) {
          server.middlewares.use('/electron-api', (req, res, next) => {
            res.setHeader('Access-Control-Allow-Origin', '*');
            next();
          });
        }
      }
    ],

    base: './',
    root: 'src/renderer',
    publicDir: resolve(__dirname, 'assets'),

    build: {
      outDir: resolve(__dirname, 'dist/renderer'),
      emptyOutDir: true,
      target: 'esnext',
      minify: isProduction ? 'terser' : false,
      sourcemap: !isProduction,

      rollupOptions: {
        input: resolve(__dirname, 'src/renderer/index.html'),
        external: ['electron'],
        
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'ui-vendor': ['framer-motion', 'lucide-react'],
            'utils-vendor': ['date-fns', 'clsx', 'tailwind-merge']
          }
        }
      },

      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction
        }
      }
    },

    server: {
      port: 3000,
      host: '0.0.0.0',
      strictPort: true,
      
      hmr: {
        port: 3001
      },

      proxy: {
        '/api': {
          target: 'http://localhost:5000',
          changeOrigin: true
        }
      }
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/renderer'),
        '@components': resolve(__dirname, 'src/renderer/components'),
        '@pages': resolve(__dirname, 'src/renderer/pages'),
        '@services': resolve(__dirname, 'src/renderer/services'),
        '@hooks': resolve(__dirname, 'src/renderer/hooks'),
        '@types': resolve(__dirname, 'src/types'),
        '@utils': resolve(__dirname, 'src/renderer/utils'),
        '@store': resolve(__dirname, 'src/renderer/store')
      }
    },

    css: {
      postcss: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer')
        ]
      }
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        'framer-motion'
      ],
      exclude: ['electron']
    },

    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __DEV__: !isProduction,
      global: 'globalThis'
    }
  };
});
```

### **2. Plugin Customizado para Container Management**

```typescript
// plugins/container-dev-plugin.ts
import type { Plugin } from 'vite';
import { spawn, ChildProcess } from 'child_process';

interface ContainerDevOptions {
  containerEngine: 'docker' | 'podman';
  autoStart: boolean;
  containers: string[];
}

export function containerDevPlugin(options: ContainerDevOptions): Plugin {
  let runningContainers: ChildProcess[] = [];

  return {
    name: 'container-dev',
    
    configureServer(server) {
      // Endpoint para gerenciar containers em desenvolvimento
      server.middlewares.use('/dev-api/containers', (req, res) => {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Access-Control-Allow-Origin', '*');
        
        if (req.method === 'GET') {
          // Listar containers
          const containerList = runningContainers.map((proc, index) => ({
            id: index,
            name: options.containers[index],
            status: proc.killed ? 'stopped' : 'running'
          }));
          
          res.end(JSON.stringify(containerList));
        }
      });

      // Auto-start containers se configurado
      if (options.autoStart) {
        server.ws.on('connection', () => {
          startDevelopmentContainers();
        });
      }
    },

    buildStart() {
      console.log('🐳 Container Dev Plugin iniciado');
    },

    buildEnd() {
      // Cleanup containers ao finalizar
      runningContainers.forEach(proc => {
        if (!proc.killed) {
          proc.kill();
        }
      });
    }
  };

  function startDevelopmentContainers() {
    options.containers.forEach((containerName, index) => {
      const proc = spawn(options.containerEngine, ['start', containerName], {
        stdio: 'pipe'
      });

      proc.stdout?.on('data', (data) => {
        console.log(`📦 ${containerName}: ${data}`);
      });

      proc.stderr?.on('data', (data) => {
        console.error(`❌ ${containerName}: ${data}`);
      });

      runningContainers[index] = proc;
    });
  }
}

// Uso no vite.config.ts
import { containerDevPlugin } from './plugins/container-dev-plugin';

export default defineConfig({
  plugins: [
    react(),
    containerDevPlugin({
      containerEngine: 'podman',
      autoStart: true,
      containers: ['redis-dev', 'postgres-dev']
    })
  ]
});
```

### **3. Asset Optimization Plugin**

```typescript
// plugins/asset-optimizer.ts
import type { Plugin } from 'vite';
import { optimize } from 'svgo';
import sharp from 'sharp';

export function assetOptimizerPlugin(): Plugin {
  return {
    name: 'asset-optimizer',
    
    async generateBundle(options, bundle) {
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'asset') {
          // Otimizar SVGs
          if (fileName.endsWith('.svg')) {
            const optimized = optimize(chunk.source as string, {
              plugins: [
                'preset-default',
                'removeDimensions',
                'removeViewBox'
              ]
            });
            
            if (!optimized.error) {
              chunk.source = optimized.data;
            }
          }
          
          // Otimizar imagens PNG/JPG
          if (fileName.match(/\.(png|jpg|jpeg)$/)) {
            try {
              const optimized = await sharp(chunk.source as Buffer)
                .resize(1920, 1080, { 
                  fit: 'inside',
                  withoutEnlargement: true 
                })
                .jpeg({ quality: 85 })
                .toBuffer();
              
              chunk.source = optimized;
            } catch (error) {
              console.warn(`Failed to optimize ${fileName}:`, error);
            }
          }
        }
      }
    }
  };
}
```

### **4. Environment-Specific Configuration**

```typescript
// vite.config.development.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [
    react({
      fastRefresh: true,
      babel: {
        plugins: [
          // Plugins apenas para desenvolvimento
          ['babel-plugin-react-refresh', {}]
        ]
      }
    })
  ],

  server: {
    port: 3000,
    open: false, // Electron abrirá
    
    hmr: {
      overlay: true,
      port: 3001
    },

    // Proxy para backend local
    proxy: {
      '/api': 'http://localhost:5000',
      '/socket.io': {
        target: 'ws://localhost:5000',
        ws: true
      }
    }
  },

  // Otimizações para desenvolvimento
  optimizeDeps: {
    force: true, // Re-otimizar dependências
    include: [
      'react',
      'react-dom',
      'react-router-dom'
    ]
  },

  define: {
    __DEV__: true,
    __API_URL__: '"http://localhost:5000"'
  }
});

// vite.config.production.ts
export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [
          // Otimizações para produção
          ['babel-plugin-transform-remove-console', {}]
        ]
      }
    })
  ],

  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info']
      }
    },

    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            if (id.includes('react')) return 'react-vendor';
            if (id.includes('framer-motion')) return 'animation-vendor';
            return 'vendor';
          }
        }
      }
    }
  },

  define: {
    __DEV__: false,
    __API_URL__: '"https://api.auto-instalador.com"'
  }
});
```

### **5. Performance Monitoring Plugin**

```typescript
// plugins/performance-monitor.ts
import type { Plugin } from 'vite';

interface PerformanceMetrics {
  buildStart: number;
  buildEnd: number;
  bundleSize: number;
  chunkCount: number;
}

export function performanceMonitorPlugin(): Plugin {
  const metrics: PerformanceMetrics = {
    buildStart: 0,
    buildEnd: 0,
    bundleSize: 0,
    chunkCount: 0
  };

  return {
    name: 'performance-monitor',
    
    buildStart() {
      metrics.buildStart = Date.now();
      console.log('🚀 Build iniciado...');
    },

    generateBundle(options, bundle) {
      metrics.chunkCount = Object.keys(bundle).length;
      
      // Calcular tamanho total do bundle
      metrics.bundleSize = Object.values(bundle).reduce((total, chunk) => {
        if (chunk.type === 'chunk') {
          return total + chunk.code.length;
        } else if (chunk.type === 'asset') {
          return total + (chunk.source as string).length;
        }
        return total;
      }, 0);
    },

    buildEnd() {
      metrics.buildEnd = Date.now();
      const buildTime = metrics.buildEnd - metrics.buildStart;
      
      console.log('\n📊 Performance Metrics:');
      console.log(`⏱️  Build Time: ${buildTime}ms`);
      console.log(`📦 Bundle Size: ${(metrics.bundleSize / 1024).toFixed(2)}KB`);
      console.log(`🧩 Chunk Count: ${metrics.chunkCount}`);
      console.log(`⚡ Speed: ${(metrics.bundleSize / buildTime).toFixed(2)} bytes/ms`);
      
      // Alertas de performance
      if (buildTime > 30000) {
        console.warn('⚠️  Build time is over 30 seconds');
      }
      
      if (metrics.bundleSize > 5 * 1024 * 1024) {
        console.warn('⚠️  Bundle size is over 5MB');
      }
    }
  };
}
```

### **6. Hot Reload para Electron Main Process**

```typescript
// plugins/electron-hot-reload.ts
import type { Plugin } from 'vite';
import { spawn, ChildProcess } from 'child_process';
import { watch } from 'chokidar';

export function electronHotReloadPlugin(): Plugin {
  let electronProcess: ChildProcess | null = null;
  let electronWatcher: any = null;

  return {
    name: 'electron-hot-reload',
    
    configureServer(server) {
      // Iniciar Electron quando o servidor estiver pronto
      server.httpServer?.once('listening', () => {
        startElectron();
        watchElectronFiles();
      });

      // Cleanup ao fechar servidor
      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);
    }
  };

  function startElectron() {
    if (electronProcess) {
      electronProcess.kill();
    }

    electronProcess = spawn('electron', ['.'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development',
        ELECTRON_IS_DEV: '1'
      }
    });

    electronProcess.on('close', (code) => {
      if (code !== null && code !== 0) {
        console.error(`Electron exited with code ${code}`);
      }
    });
  }

  function watchElectronFiles() {
    if (electronWatcher) {
      electronWatcher.close();
    }

    electronWatcher = watch(['src/electron/**/*', 'dist/electron/**/*'], {
      ignored: /node_modules/
    });

    electronWatcher.on('change', (path: string) => {
      console.log(`🔄 Electron file changed: ${path}`);
      startElectron();
    });
  }

  function cleanup() {
    if (electronProcess) {
      electronProcess.kill();
    }
    if (electronWatcher) {
      electronWatcher.close();
    }
  }
}
```

### **7. Bundle Analysis Integration**

```typescript
// vite.config.analyze.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';

export default defineConfig({
  plugins: [
    react(),
    
    // Visualizador de bundle
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
      template: 'treemap' // 'treemap' | 'sunburst' | 'network'
    }),

    // Plugin customizado para análise
    {
      name: 'bundle-analyzer',
      generateBundle(options, bundle) {
        const analysis = {
          totalSize: 0,
          chunks: [] as any[],
          assets: [] as any[]
        };

        Object.entries(bundle).forEach(([fileName, chunk]) => {
          const size = chunk.type === 'chunk' 
            ? chunk.code.length 
            : (chunk.source as string).length;
          
          analysis.totalSize += size;
          
          if (chunk.type === 'chunk') {
            analysis.chunks.push({
              name: fileName,
              size,
              modules: Object.keys(chunk.modules || {}).length
            });
          } else {
            analysis.assets.push({
              name: fileName,
              size
            });
          }
        });

        // Salvar análise
        this.emitFile({
          type: 'asset',
          fileName: 'bundle-analysis.json',
          source: JSON.stringify(analysis, null, 2)
        });
      }
    }
  ],

  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Estratégia de chunking para análise
          if (id.includes('node_modules')) {
            const module = id.split('node_modules/')[1].split('/')[0];
            return `vendor-${module}`;
          }
          
          if (id.includes('src/components')) {
            return 'components';
          }
          
          if (id.includes('src/services')) {
            return 'services';
          }
          
          return 'main';
        }
      }
    }
  }
});

// Script para executar análise
// package.json
{
  "scripts": {
    "analyze": "vite build --config vite.config.analyze.ts && open dist/stats.html"
  }
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Vite 5.4.2 Examples
