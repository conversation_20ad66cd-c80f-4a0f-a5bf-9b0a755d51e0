# TypeScript 5.6.2 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.6.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.typescriptlang.org/
- **GitHub:** https://github.com/microsoft/TypeScript
- **Documentação:** https://www.typescriptlang.org/docs/
- **NPM/Package:** https://www.npmjs.com/package/typescript
- **Fórum/Community:** https://github.com/microsoft/TypeScript/discussions
- **Stack Overflow Tag:** `typescript`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Container Service Completo**

```typescript
// src/services/containerService.ts
import type { 
  Container, 
  ContainerCreateConfig, 
  ContainerStats,
  ContainerOperationResult,
  LogOptions 
} from '@types/container';

export class ContainerService {
  private electronAPI: ElectronAPI;

  constructor() {
    if (!window.electronAPI) {
      throw new Error('Electron API não disponível');
    }
    this.electronAPI = window.electronAPI;
  }

  /**
   * Lista todos os containers
   */
  async listContainers(): Promise<ContainerOperationResult<Container[]>> {
    try {
      const containers = await this.electronAPI.containers.list();
      return {
        success: true,
        data: containers
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        code: 'CONTAINER_LIST_FAILED'
      };
    }
  }

  /**
   * Obtém container por ID
   */
  async getContainer(id: string): Promise<ContainerOperationResult<Container>> {
    if (!id || typeof id !== 'string') {
      return {
        success: false,
        error: 'ID do container é obrigatório',
        code: 'INVALID_CONTAINER_ID'
      };
    }

    try {
      const container = await this.electronAPI.containers.getById(id);
      return {
        success: true,
        data: container
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Container não encontrado',
        code: 'CONTAINER_NOT_FOUND'
      };
    }
  }

  /**
   * Cria novo container
   */
  async createContainer(
    config: ContainerCreateConfig
  ): Promise<ContainerOperationResult<Container>> {
    // Validação de entrada
    const validation = this.validateContainerConfig(config);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join(', '),
        code: 'INVALID_CONFIG'
      };
    }

    try {
      const container = await this.electronAPI.containers.create(config);
      
      // Log da operação
      console.log(`Container criado: ${container.name} (${container.id})`);
      
      return {
        success: true,
        data: container
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao criar container',
        code: 'CONTAINER_CREATE_FAILED'
      };
    }
  }

  /**
   * Inicia container
   */
  async startContainer(id: string): Promise<ContainerOperationResult<void>> {
    try {
      await this.electronAPI.containers.start(id);
      
      // Notificação de sucesso
      await this.electronAPI.notifications.show(
        'Container Iniciado',
        `Container ${id} foi iniciado com sucesso`
      );

      return { success: true, data: undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao iniciar container',
        code: 'CONTAINER_START_FAILED'
      };
    }
  }

  /**
   * Para container
   */
  async stopContainer(id: string, timeout = 10): Promise<ContainerOperationResult<void>> {
    try {
      await this.electronAPI.containers.stop(id);
      
      return { success: true, data: undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao parar container',
        code: 'CONTAINER_STOP_FAILED'
      };
    }
  }

  /**
   * Remove container
   */
  async removeContainer(
    id: string, 
    force = false
  ): Promise<ContainerOperationResult<void>> {
    try {
      await this.electronAPI.containers.remove(id);
      
      return { success: true, data: undefined };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao remover container',
        code: 'CONTAINER_REMOVE_FAILED'
      };
    }
  }

  /**
   * Obtém logs do container
   */
  async getContainerLogs(
    id: string, 
    options: LogOptions = {}
  ): Promise<ContainerOperationResult<string[]>> {
    try {
      const logs = await this.electronAPI.containers.logs(id, options);
      return {
        success: true,
        data: logs
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao obter logs',
        code: 'CONTAINER_LOGS_FAILED'
      };
    }
  }

  /**
   * Obtém estatísticas do container
   */
  async getContainerStats(id: string): Promise<ContainerOperationResult<ContainerStats>> {
    try {
      const stats = await this.electronAPI.containers.stats(id);
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Falha ao obter estatísticas',
        code: 'CONTAINER_STATS_FAILED'
      };
    }
  }

  /**
   * Valida configuração do container
   */
  private validateContainerConfig(config: ContainerCreateConfig): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!config.name || config.name.trim().length === 0) {
      errors.push('Nome do container é obrigatório');
    }

    if (!config.image || config.image.trim().length === 0) {
      errors.push('Imagem do container é obrigatória');
    }

    // Validar nome do container (apenas caracteres alfanuméricos, hífens e underscores)
    if (config.name && !/^[a-zA-Z0-9_-]+$/.test(config.name)) {
      errors.push('Nome do container deve conter apenas letras, números, hífens e underscores');
    }

    // Validar portas
    if (config.ports) {
      for (const port of config.ports) {
        if (port.hostPort < 1 || port.hostPort > 65535) {
          errors.push(`Porta do host inválida: ${port.hostPort}`);
        }
        if (port.containerPort < 1 || port.containerPort > 65535) {
          errors.push(`Porta do container inválida: ${port.containerPort}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Singleton instance
export const containerService = new ContainerService();
```

### **2. React Hook Tipado com useQuery**

```typescript
// src/hooks/useContainers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { containerService } from '@services/containerService';
import type { Container, ContainerCreateConfig } from '@types/container';

interface UseContainersOptions {
  refetchInterval?: number;
  enabled?: boolean;
}

interface UseContainersReturn {
  containers: Container[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  createContainer: (config: ContainerCreateConfig) => Promise<void>;
  startContainer: (id: string) => Promise<void>;
  stopContainer: (id: string) => Promise<void>;
  removeContainer: (id: string) => Promise<void>;
  isCreating: boolean;
  isStarting: boolean;
  isStopping: boolean;
  isRemoving: boolean;
}

export function useContainers(options: UseContainersOptions = {}): UseContainersReturn {
  const queryClient = useQueryClient();
  
  const {
    refetchInterval = 30000, // 30 segundos
    enabled = true
  } = options;

  // Query para listar containers
  const {
    data: containersResult,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['containers'],
    queryFn: async () => {
      const result = await containerService.listContainers();
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    enabled,
    refetchInterval,
    staleTime: 10000, // 10 segundos
    gcTime: 300000, // 5 minutos
    retry: (failureCount, error) => {
      // Retry até 3 vezes, mas não para erros de autenticação
      if (error.message.includes('authentication')) {
        return false;
      }
      return failureCount < 3;
    }
  });

  // Mutation para criar container
  const createMutation = useMutation({
    mutationFn: async (config: ContainerCreateConfig) => {
      const result = await containerService.createContainer(config);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    },
    onError: (error: Error) => {
      console.error('Erro ao criar container:', error.message);
    }
  });

  // Mutation para iniciar container
  const startMutation = useMutation({
    mutationFn: async (id: string) => {
      const result = await containerService.startContainer(id);
      if (!result.success) {
        throw new Error(result.error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    }
  });

  // Mutation para parar container
  const stopMutation = useMutation({
    mutationFn: async (id: string) => {
      const result = await containerService.stopContainer(id);
      if (!result.success) {
        throw new Error(result.error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    }
  });

  // Mutation para remover container
  const removeMutation = useMutation({
    mutationFn: async (id: string) => {
      const result = await containerService.removeContainer(id);
      if (!result.success) {
        throw new Error(result.error);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    }
  });

  return {
    containers: containersResult || [],
    isLoading,
    error: queryError?.message || null,
    refetch,
    createContainer: createMutation.mutateAsync,
    startContainer: startMutation.mutateAsync,
    stopContainer: stopMutation.mutateAsync,
    removeContainer: removeMutation.mutateAsync,
    isCreating: createMutation.isPending,
    isStarting: startMutation.isPending,
    isStopping: stopMutation.isPending,
    isRemoving: removeMutation.isPending
  };
}

// Hook específico para um container
export function useContainer(id: string) {
  return useQuery({
    queryKey: ['container', id],
    queryFn: async () => {
      const result = await containerService.getContainer(id);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    enabled: !!id,
    staleTime: 5000,
    gcTime: 60000
  });
}

// Hook para estatísticas do container
export function useContainerStats(id: string, enabled = true) {
  return useQuery({
    queryKey: ['container-stats', id],
    queryFn: async () => {
      const result = await containerService.getContainerStats(id);
      if (!result.success) {
        throw new Error(result.error);
      }
      return result.data;
    },
    enabled: enabled && !!id,
    refetchInterval: 5000, // Atualizar a cada 5 segundos
    staleTime: 1000
  });
}
```

### **3. Componente React com TypeScript Avançado**

```typescript
// src/components/ContainerCard.tsx
import React, { memo, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Play, Square, Trash2, MoreVertical, Activity } from 'lucide-react';
import type { Container, ContainerStats } from '@types/container';
import { useContainerStats } from '@hooks/useContainers';
import { formatBytes, formatUptime } from '@utils/formatters';

interface ContainerCardProps {
  container: Container;
  onStart: (id: string) => Promise<void>;
  onStop: (id: string) => Promise<void>;
  onRemove: (id: string) => Promise<void>;
  onViewLogs: (id: string) => void;
  isLoading?: boolean;
  className?: string;
}

export const ContainerCard = memo<ContainerCardProps>(function ContainerCard({
  container,
  onStart,
  onStop,
  onRemove,
  onViewLogs,
  isLoading = false,
  className = ''
}) {
  // Buscar estatísticas apenas se o container estiver rodando
  const { data: stats } = useContainerStats(
    container.id, 
    container.status === 'running'
  );

  // Memoizar cálculos pesados
  const statusConfig = useMemo(() => {
    const configs = {
      running: {
        color: 'bg-green-100 text-green-800 border-green-200',
        icon: '🟢',
        label: 'Executando'
      },
      exited: {
        color: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: '⚫',
        label: 'Parado'
      },
      paused: {
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        icon: '⏸️',
        label: 'Pausado'
      },
      restarting: {
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: '🔄',
        label: 'Reiniciando'
      },
      removing: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: '🗑️',
        label: 'Removendo'
      },
      dead: {
        color: 'bg-red-100 text-red-800 border-red-200',
        icon: '💀',
        label: 'Morto'
      }
    } as const;

    return configs[container.status] || configs.exited;
  }, [container.status]);

  const formattedUptime = useMemo(() => {
    if (container.status !== 'running' || !container.startedAt) {
      return null;
    }
    return formatUptime(container.startedAt);
  }, [container.status, container.startedAt]);

  const formattedStats = useMemo(() => {
    if (!stats) return null;

    return {
      cpu: `${stats.cpuUsage.toFixed(1)}%`,
      memory: `${formatBytes(stats.memoryUsage)} / ${formatBytes(stats.memoryLimit)}`,
      memoryPercent: ((stats.memoryUsage / stats.memoryLimit) * 100).toFixed(1),
      network: `↓ ${formatBytes(stats.networkRx)} ↑ ${formatBytes(stats.networkTx)}`
    };
  }, [stats]);

  // Handlers com useCallback para evitar re-renders
  const handleStart = useCallback(async () => {
    try {
      await onStart(container.id);
    } catch (error) {
      console.error('Erro ao iniciar container:', error);
    }
  }, [container.id, onStart]);

  const handleStop = useCallback(async () => {
    try {
      await onStop(container.id);
    } catch (error) {
      console.error('Erro ao parar container:', error);
    }
  }, [container.id, onStop]);

  const handleRemove = useCallback(async () => {
    if (window.confirm(`Tem certeza que deseja remover o container "${container.name}"?`)) {
      try {
        await onRemove(container.id);
      } catch (error) {
        console.error('Erro ao remover container:', error);
      }
    }
  }, [container.id, container.name, onRemove]);

  const handleViewLogs = useCallback(() => {
    onViewLogs(container.id);
  }, [container.id, onViewLogs]);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.02 }}
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 p-4 
        hover:shadow-md transition-all duration-200
        ${isLoading ? 'opacity-50 pointer-events-none' : ''}
        ${className}
      `}
    >
      {/* Header */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-lg text-gray-900 truncate">
            {container.name}
          </h3>
          <p className="text-sm text-gray-600 truncate">
            {container.image}
          </p>
          {container.id && (
            <p className="text-xs text-gray-500 font-mono">
              ID: {container.id.substring(0, 12)}
            </p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`
            px-2 py-1 rounded-full text-xs font-medium border
            ${statusConfig.color}
          `}>
            {statusConfig.icon} {statusConfig.label}
          </span>
          
          <button className="p-1 hover:bg-gray-100 rounded">
            <MoreVertical className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Stats (apenas se rodando) */}
      {formattedStats && (
        <div className="mb-3 p-2 bg-gray-50 rounded text-xs space-y-1">
          <div className="flex justify-between">
            <span className="text-gray-600">CPU:</span>
            <span className="font-mono">{formattedStats.cpu}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Memória:</span>
            <span className="font-mono">{formattedStats.memory}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Rede:</span>
            <span className="font-mono">{formattedStats.network}</span>
          </div>
        </div>
      )}

      {/* Uptime */}
      {formattedUptime && (
        <div className="mb-3 text-xs text-gray-600">
          <Activity className="w-3 h-3 inline mr-1" />
          Uptime: {formattedUptime}
        </div>
      )}

      {/* Ports */}
      {container.ports && container.ports.length > 0 && (
        <div className="mb-3">
          <div className="text-xs text-gray-600 mb-1">Portas:</div>
          <div className="flex flex-wrap gap-1">
            {container.ports.map((port, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded font-mono"
              >
                {port.publicPort ? `${port.publicPort}:` : ''}{port.privatePort}/{port.type}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex space-x-2">
        {container.status === 'running' ? (
          <button
            onClick={handleStop}
            disabled={isLoading}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm disabled:opacity-50"
          >
            <Square className="w-4 h-4 mr-1" />
            Parar
          </button>
        ) : (
          <button
            onClick={handleStart}
            disabled={isLoading}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors text-sm disabled:opacity-50"
          >
            <Play className="w-4 h-4 mr-1" />
            Iniciar
          </button>
        )}
        
        <button
          onClick={handleViewLogs}
          className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
        >
          Logs
        </button>
        
        <button
          onClick={handleRemove}
          disabled={isLoading || container.status === 'running'}
          className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm disabled:opacity-50"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  );
});

export default ContainerCard;
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - TypeScript 5.6.2 Examples
