/**
 * About Page - Página sobre a aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Página com informações sobre o Auto-Instalador
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';

export const AboutPage: React.FC = () => {
  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          ℹ️ Sobre o Auto-Instalador V3 Lite
        </h1>
        
        <div className="space-y-6">
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Informações da Aplicação</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Versão:</span>
                <span className="ml-2 font-mono">3.0.0-lite</span>
              </div>
              <div>
                <span className="text-gray-400">Build:</span>
                <span className="ml-2 font-mono">2025.08.05</span>
              </div>
              <div>
                <span className="text-gray-400">Electron:</span>
                <span className="ml-2 font-mono">Latest</span>
              </div>
              <div>
                <span className="text-gray-400">React:</span>
                <span className="ml-2 font-mono">18.x</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Funcionalidades</h2>
            <ul className="space-y-2 text-sm text-gray-300">
              <li className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                Gerenciamento de containers Docker e Podman
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                Interface moderna inspirada no Docker Desktop
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                Detecção automática de engines de container
              </li>
              <li className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                Instalação automática multiplataforma
              </li>
              <li className="flex items-center gap-2">
                <span className="text-yellow-400">⚠</span>
                Gerenciamento de pacotes (em desenvolvimento)
              </li>
              <li className="flex items-center gap-2">
                <span className="text-yellow-400">⚠</span>
                Monitoramento do sistema (em desenvolvimento)
              </li>
            </ul>
          </div>

          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Tecnologias</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl mb-2">⚛️</div>
                <div>React</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📱</div>
                <div>Electron</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">🎨</div>
                <div>Tailwind CSS</div>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📝</div>
                <div>TypeScript</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
