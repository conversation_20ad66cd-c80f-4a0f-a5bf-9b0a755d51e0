/**
 * Container List Component
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para listagem e gerenciamento de containers
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PlayIcon, 
  StopIcon, 
  ArrowPathIcon,
  PauseIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { 
  useContainers, 
  useContainerAction, 
  formatContainerStatus, 
  getStatusColor,
  formatBytes,
  useContainerEvents
} from '../../../services/container-service';
import type { 
  Container, 
  ContainerEngine, 
  ContainerStatus,
  ContainerListRequest 
} from '../../../../../shared/types/api.types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContainerListProps {
  onCreateContainer?: () => void;
  onViewContainer?: (container: Container) => void;
  className?: string;
}

interface ContainerFilters {
  search: string;
  status: ContainerStatus | 'all';
  engine: ContainerEngine | 'all';
  showAll: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

export const ContainerList: React.FC<ContainerListProps> = ({
  onCreateContainer,
  onViewContainer,
  className = ''
}) => {
  // State
  const [filters, setFilters] = useState<ContainerFilters>({
    search: '',
    status: 'all',
    engine: 'all',
    showAll: false
  });

  // Setup real-time events
  useContainerEvents();

  // Build request
  const request: ContainerListRequest = useMemo(() => ({
    all: filters.showAll,
    filters: {
      name: filters.search || undefined,
      status: filters.status !== 'all' ? [filters.status] : undefined
    },
    engine: filters.engine !== 'all' ? filters.engine : undefined
  }), [filters]);

  // Queries
  const { 
    data: containersResponse, 
    isLoading, 
    error, 
    refetch 
  } = useContainers(request, {
    refetchInterval: 30000 // 30 segundos
  });

  // Mutations
  const containerAction = useContainerAction();

  // Filtered containers
  const containers = useMemo(() => {
    if (!containersResponse?.containers) return [];
    
    return containersResponse.containers.filter(container => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          container.name.toLowerCase().includes(searchLower) ||
          container.image.toLowerCase().includes(searchLower) ||
          container.id.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [containersResponse?.containers, filters.search]);

  // Handlers
  const handleAction = async (
    container: Container, 
    action: 'start' | 'stop' | 'restart' | 'pause' | 'unpause' | 'remove'
  ) => {
    await containerAction.mutateAsync({
      id: container.id,
      action,
      engine: container.engine,
      force: action === 'remove'
    });
  };

  const handleFilterChange = (key: keyof ContainerFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Carregando containers...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
          <span className="ml-2 text-red-800">Erro ao carregar containers</span>
        </div>
        <p className="mt-2 text-sm text-red-600">{error.message}</p>
        <button
          onClick={() => refetch()}
          className="mt-3 px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200 transition-colors"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Containers</h2>
          <p className="text-sm text-gray-600">
            {containers.length} container{containers.length !== 1 ? 's' : ''} encontrado{containers.length !== 1 ? 's' : ''}
          </p>
        </div>
        
        {onCreateContainer && (
          <button
            onClick={onCreateContainer}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Novo Container
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar containers..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Todos os status</option>
            <option value="running">Executando</option>
            <option value="exited">Parado</option>
            <option value="paused">Pausado</option>
            <option value="created">Criado</option>
          </select>

          {/* Engine Filter */}
          <select
            value={filters.engine}
            onChange={(e) => handleFilterChange('engine', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Todos os engines</option>
            <option value="docker">Docker</option>
            <option value="podman">Podman</option>
          </select>

          {/* Show All Toggle */}
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.showAll}
              onChange={(e) => handleFilterChange('showAll', e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">Mostrar parados</span>
          </label>
        </div>
      </div>

      {/* Container List */}
      <div className="space-y-3">
        <AnimatePresence>
          {containers.map((container) => (
            <motion.div
              key={container.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                {/* Container Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className={`w-3 h-3 rounded-full ${
                        container.status === 'running' ? 'bg-green-400' :
                        container.status === 'paused' ? 'bg-yellow-400' :
                        container.status === 'exited' ? 'bg-gray-400' :
                        'bg-red-400'
                      }`} />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 
                          className="text-lg font-medium text-gray-900 truncate cursor-pointer hover:text-blue-600"
                          onClick={() => onViewContainer?.(container)}
                        >
                          {container.name || container.id.substring(0, 12)}
                        </h3>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {container.engine}
                        </span>
                      </div>
                      
                      <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                        <span>{container.image}</span>
                        <span className={getStatusColor(container.status)}>
                          {formatContainerStatus(container.status)}
                        </span>
                        {container.ports.length > 0 && (
                          <span>
                            Portas: {container.ports.map(p => 
                              p.hostPort ? `${p.hostPort}:${p.containerPort}` : p.containerPort
                            ).join(', ')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  {container.status === 'running' && (
                    <>
                      <button
                        onClick={() => handleAction(container, 'pause')}
                        disabled={containerAction.isPending}
                        className="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                        title="Pausar"
                      >
                        <PauseIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleAction(container, 'restart')}
                        disabled={containerAction.isPending}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Reiniciar"
                      >
                        <ArrowPathIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleAction(container, 'stop')}
                        disabled={containerAction.isPending}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Parar"
                      >
                        <StopIcon className="h-4 w-4" />
                      </button>
                    </>
                  )}

                  {container.status === 'paused' && (
                    <button
                      onClick={() => handleAction(container, 'unpause')}
                      disabled={containerAction.isPending}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Despausar"
                    >
                      <PlayIcon className="h-4 w-4" />
                    </button>
                  )}

                  {(container.status === 'exited' || container.status === 'created') && (
                    <button
                      onClick={() => handleAction(container, 'start')}
                      disabled={containerAction.isPending}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Iniciar"
                    >
                      <PlayIcon className="h-4 w-4" />
                    </button>
                  )}

                  {container.status !== 'running' && (
                    <button
                      onClick={() => handleAction(container, 'remove')}
                      disabled={containerAction.isPending}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Remover"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Empty State */}
        {containers.length === 0 && (
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum container encontrado</h3>
            <p className="mt-1 text-sm text-gray-500">
              {filters.search || filters.status !== 'all' || filters.engine !== 'all'
                ? 'Tente ajustar os filtros de busca.'
                : 'Comece criando seu primeiro container.'}
            </p>
            {onCreateContainer && (
              <div className="mt-6">
                <button
                  onClick={onCreateContainer}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Criar Container
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
