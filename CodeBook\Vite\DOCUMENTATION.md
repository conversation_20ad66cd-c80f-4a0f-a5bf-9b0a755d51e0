# Vite 5.4.2 - Documentação Oficial Resumida

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.4.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://vitejs.dev/
- **GitHub:** https://github.com/vitejs/vite
- **Documentação:** https://vitejs.dev/guide/
- **NPM/Package:** https://www.npmjs.com/package/vite
- **Fórum/Community:** https://github.com/vitejs/vite/discussions
- **Stack Overflow Tag:** `vite`

---

## 📚 **DOCUMENTAÇÃO CORE VITE 5.4.2**

### **1. Configuração Básica**

#### **vite.config.ts Essencial**
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',
  
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      input: resolve(__dirname, 'index.html')
    }
  },
  
  server: {
    port: 3000,
    open: true
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
});
```

#### **Configuração Condicional**
```typescript
export default defineConfig(({ command, mode }) => {
  const isProduction = mode === 'production';
  const isDevelopment = command === 'serve';
  
  return {
    plugins: [
      react({
        fastRefresh: isDevelopment
      })
    ],
    
    build: {
      minify: isProduction ? 'terser' : false,
      sourcemap: !isProduction
    },
    
    define: {
      __DEV__: !isProduction,
      __PROD__: isProduction
    }
  };
});
```

---

### **2. Plugin System**

#### **Plugin Oficial React**
```typescript
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [
    react({
      // JSX runtime automático
      jsxRuntime: 'automatic',
      
      // Configurações Babel
      babel: {
        plugins: [
          ['@babel/plugin-proposal-decorators', { legacy: true }]
        ]
      },
      
      // Fast Refresh
      fastRefresh: true,
      
      // Include/exclude files
      include: /\.(jsx|tsx)$/,
      exclude: /node_modules/
    })
  ]
});
```

#### **Plugin Customizado**
```typescript
import type { Plugin } from 'vite';

function customPlugin(): Plugin {
  return {
    name: 'custom-plugin',
    
    // Hook de configuração
    config(config, { command }) {
      if (command === 'serve') {
        config.define = {
          ...config.define,
          __DEV_MODE__: true
        };
      }
    },
    
    // Hook de build
    generateBundle(options, bundle) {
      // Processar bundle
      console.log('Bundle gerado:', Object.keys(bundle));
    },
    
    // Hook de transformação
    transform(code, id) {
      if (id.endsWith('.special')) {
        return `export default ${JSON.stringify(code)}`;
      }
    }
  };
}
```

---

### **3. Development Server**

#### **Configuração do Servidor**
```typescript
export default defineConfig({
  server: {
    // Porta e host
    port: 3000,
    host: '0.0.0.0',
    strictPort: true,
    
    // HTTPS (opcional)
    https: false,
    
    // Abrir browser automaticamente
    open: '/dashboard',
    
    // Proxy para APIs
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      
      '/socket.io': {
        target: 'ws://localhost:5000',
        ws: true
      }
    },
    
    // CORS
    cors: {
      origin: ['http://localhost:3000'],
      credentials: true
    },
    
    // Headers customizados
    headers: {
      'X-Custom-Header': 'value'
    }
  }
});
```

#### **Hot Module Replacement (HMR)**
```typescript
// Configuração HMR
export default defineConfig({
  server: {
    hmr: {
      port: 3001,
      overlay: true, // Mostrar erros na tela
      clientPort: 3001
    }
  }
});

// API HMR no código
if (import.meta.hot) {
  // Aceitar atualizações do módulo atual
  import.meta.hot.accept();
  
  // Aceitar atualizações de dependências
  import.meta.hot.accept('./dependency.js', (newModule) => {
    // Atualizar com novo módulo
    updateDependency(newModule);
  });
  
  // Cleanup antes da atualização
  import.meta.hot.dispose((data) => {
    // Salvar estado
    data.state = getCurrentState();
  });
  
  // Invalidar módulo
  import.meta.hot.invalidate();
}
```

---

### **4. Build Configuration**

#### **Configuração de Build**
```typescript
export default defineConfig({
  build: {
    // Diretório de saída
    outDir: 'dist',
    emptyOutDir: true,
    
    // Target de build
    target: 'esnext',
    
    // Minificação
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // Source maps
    sourcemap: true,
    
    // Configurações Rollup
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        admin: resolve(__dirname, 'admin.html')
      },
      
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['lodash', 'date-fns']
        }
      },
      
      external: ['electron']
    },
    
    // Configurações de assets
    assetsInlineLimit: 4096,
    chunkSizeWarningLimit: 1000,
    
    // CSS
    cssCodeSplit: true,
    cssMinify: true
  }
});
```

#### **Chunking Strategy**
```typescript
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react')) return 'react-vendor';
            if (id.includes('lodash')) return 'utils-vendor';
            return 'vendor';
          }
          
          // Feature chunks
          if (id.includes('src/features/dashboard')) return 'dashboard';
          if (id.includes('src/features/containers')) return 'containers';
          
          // Component chunks
          if (id.includes('src/components/ui')) return 'ui-components';
          
          return 'main';
        }
      }
    }
  }
});
```

---

### **5. Asset Handling**

#### **Static Assets**
```typescript
// Importar assets
import logoUrl from './assets/logo.png';
import iconUrl from './assets/icon.svg?url';
import iconInline from './assets/icon.svg?inline';
import iconRaw from './assets/data.txt?raw';

// Usar em componentes
function Logo() {
  return <img src={logoUrl} alt="Logo" />;
}

// Assets dinâmicos
const getAssetUrl = (name: string) => {
  return new URL(`./assets/${name}`, import.meta.url).href;
};
```

#### **Public Directory**
```typescript
// Arquivos em public/ são servidos na raiz
// public/favicon.ico -> /favicon.ico
// public/images/hero.jpg -> /images/hero.jpg

// Referenciar no código
const heroImage = '/images/hero.jpg';

// Configurar public dir
export default defineConfig({
  publicDir: 'public', // padrão
  
  // Ou desabilitar
  publicDir: false
});
```

---

### **6. Environment Variables**

#### **Variáveis de Ambiente**
```typescript
// .env files
// .env                # carregado em todos os casos
// .env.local          # carregado em todos os casos, ignorado pelo git
// .env.[mode]         # carregado apenas no modo específico
// .env.[mode].local   # carregado apenas no modo específico, ignorado pelo git

// Usar no código (apenas VITE_ prefixadas)
const apiUrl = import.meta.env.VITE_API_URL;
const isDev = import.meta.env.DEV;
const isProd = import.meta.env.PROD;
const mode = import.meta.env.MODE;

// Tipos TypeScript
interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_APP_TITLE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

#### **Configuração de Environment**
```typescript
export default defineConfig({
  // Definir variáveis em build time
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_DATE__: JSON.stringify(new Date().toISOString()),
    
    // Substituir process.env para compatibilidade
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  },
  
  // Prefixo customizado para env vars
  envPrefix: ['VITE_', 'CUSTOM_'],
  
  // Diretório de env files
  envDir: '.'
});
```

---

### **7. CSS e Preprocessors**

#### **CSS Configuration**
```typescript
export default defineConfig({
  css: {
    // PostCSS
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    },
    
    // CSS Modules
    modules: {
      localsConvention: 'camelCaseOnly',
      generateScopedName: '[name]__[local]___[hash:base64:5]'
    },
    
    // Preprocessors
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      },
      
      less: {
        math: 'parens-division'
      },
      
      stylus: {
        define: {
          $specialColor: new stylus.nodes.RGBA(51, 197, 255, 1)
        }
      }
    },
    
    // DevSourcemap
    devSourcemap: true
  }
});
```

#### **CSS Import**
```typescript
// CSS normal
import './style.css';

// CSS Modules
import styles from './component.module.css';

// SCSS
import './styles.scss';

// CSS como string
import cssText from './style.css?inline';

// PostCSS
import './tailwind.css';
```

---

### **8. TypeScript Integration**

#### **TypeScript Configuration**
```typescript
// Vite tem suporte nativo ao TypeScript
// Apenas transpila, não faz type checking

// Para type checking, usar:
// - tsc --noEmit
// - vue-tsc (para Vue)
// - Plugin específico

export default defineConfig({
  // Configurações específicas para TS
  esbuild: {
    target: 'esnext',
    format: 'esm'
  },
  
  // Resolver extensões
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx']
  }
});
```

#### **Type Definitions**
```typescript
// vite-env.d.ts
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_APP_TITLE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Declarar tipos para assets
declare module '*.svg' {
  import React = require('react');
  export const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
  const src: string;
  export default src;
}

declare module '*.png' {
  const src: string;
  export default src;
}
```

---

### **9. Testing Integration**

#### **Vitest Configuration**
```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  
  test: {
    // Ambiente de teste
    environment: 'jsdom',
    
    // Setup files
    setupFiles: ['./src/test/setup.ts'],
    
    // Globals
    globals: true,
    
    // Coverage
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts'
      ]
    },
    
    // Pool configuration
    pool: 'threads',
    poolOptions: {
      threads: {
        maxThreads: 4,
        minThreads: 1
      }
    }
  }
});
```

---

### **10. Performance Optimization**

#### **Dependency Optimization**
```typescript
export default defineConfig({
  optimizeDeps: {
    // Incluir dependências para pré-bundling
    include: [
      'react',
      'react-dom',
      'lodash-es'
    ],
    
    // Excluir dependências
    exclude: [
      'electron'
    ],
    
    // Configurações esbuild
    esbuildOptions: {
      target: 'esnext'
    },
    
    // Forçar re-otimização
    force: false
  },
  
  // Worker configuration
  worker: {
    format: 'es',
    plugins: [react()]
  }
});
```

#### **Build Performance**
```typescript
export default defineConfig({
  build: {
    // Configurações de performance
    rollupOptions: {
      // Configurar workers para builds paralelos
      maxParallelFileOps: 4,
      
      // Cache configuration
      cache: true
    },
    
    // Reportar chunk sizes
    reportCompressedSize: false,
    
    // Configurações de minificação
    minify: 'terser',
    terserOptions: {
      parallel: true,
      maxWorkers: 4
    }
  }
});
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Vite 5.4.2 Documentation
