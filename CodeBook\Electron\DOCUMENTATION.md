# Electron 37.1.2 - Documentação Oficial Resumida

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 37.1.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.electronjs.org/
- **GitHub:** https://github.com/electron/electron
- **Documentação:** https://www.electronjs.org/docs/latest/
- **NPM/Package:** https://www.npmjs.com/package/electron
- **Fórum/Community:** https://github.com/electron/electron/discussions
- **Stack Overflow Tag:** `electron`

---

## 📚 **DOCUMENTAÇÃO CORE**

### **1. Process Model (Modelo de Processos)**

#### **Main Process**
- **Função:** Gerencia ciclo de vida da aplicação e cria renderer processes
- **Acesso:** APIs completas do Node.js e Electron
- **Responsabilidades:**
  - <PERSON><PERSON>r e gerenciar BrowserWindows
  - Gerenciar menu da aplicação
  - Lidar com eventos do sistema
  - Comunicação IPC

```typescript
// Exemplo Main Process
import { app, BrowserWindow } from 'electron';

app.whenReady().then(() => {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });
  
  mainWindow.loadFile('index.html');
});
```

#### **Renderer Process**
- **Função:** Renderiza a interface usando Chromium
- **Acesso:** APIs web padrão + APIs expostas via preload
- **Isolamento:** Sem acesso direto ao Node.js (segurança)

```typescript
// Renderer Process (React)
import React from 'react';

function App() {
  const handleAction = async () => {
    // Comunicação via API exposta
    const result = await window.electronAPI.invoke('some-action');
  };

  return <div>Auto-Instalador V3 Lite</div>;
}
```

#### **Preload Scripts**
- **Função:** Ponte segura entre Main e Renderer
- **Execução:** Antes do renderer carregar
- **Acesso:** APIs limitadas do Node.js + contextBridge

```typescript
// preload.js
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
  on: (channel: string, callback: Function) => ipcRenderer.on(channel, callback)
});
```

---

### **2. Security (Segurança)**

#### **Princípios Fundamentais v37**
1. **Context Isolation:** Obrigatório (default: true)
2. **Node Integration:** Desabilitado (default: false)
3. **Web Security:** Habilitado (default: true)
4. **Preload Scripts:** Única forma segura de expor APIs

#### **Configurações de Segurança**
```typescript
const secureWindow = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,           // ✅ Obrigatório
    contextIsolation: true,           // ✅ Obrigatório
    webSecurity: true,               // ✅ Default v37
    allowRunningInsecureContent: false,
    experimentalFeatures: false,
    enableRemoteModule: false,
    preload: path.join(__dirname, 'preload.js')
  }
});
```

#### **Content Security Policy (CSP)**
```typescript
// Configuração CSP para Auto-Instalador
session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
  callback({
    responseHeaders: {
      ...details.responseHeaders,
      'Content-Security-Policy': [
        "default-src 'self' 'unsafe-inline' data:; " +
        "script-src 'self' 'unsafe-eval' 'unsafe-inline'; " +
        "connect-src 'self' http://localhost:* ws://localhost:*; " +
        "img-src 'self' data: https:;"
      ]
    }
  });
});
```

---

### **3. IPC Communication**

#### **Padrões de Comunicação**

**Invoke/Handle Pattern (Recomendado)**
```typescript
// Main Process
ipcMain.handle('get-containers', async () => {
  return await dockerService.listContainers();
});

// Renderer Process (via preload)
const containers = await window.electronAPI.invoke('get-containers');
```

**Send/On Pattern (Para eventos)**
```typescript
// Main Process
mainWindow.webContents.send('container-status-changed', { id, status });

// Renderer Process
window.electronAPI.on('container-status-changed', (data) => {
  updateContainerStatus(data);
});
```

#### **Canais IPC Seguros**
```typescript
// Whitelist de canais permitidos
const ALLOWED_CHANNELS = [
  'containers:list',
  'containers:start',
  'containers:stop',
  'fs:select-folder',
  'notifications:show'
];

// Validação no preload
const electronAPI = {
  invoke: (channel: string, ...args: any[]) => {
    if (ALLOWED_CHANNELS.includes(channel)) {
      return ipcRenderer.invoke(channel, ...args);
    }
    throw new Error(`Channel ${channel} not allowed`);
  }
};
```

---

### **4. Native APIs**

#### **Dialog API**
```typescript
import { dialog } from 'electron';

// File selection
const selectFile = async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [
      { name: 'Docker Compose', extensions: ['yml', 'yaml'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  
  return result.canceled ? null : result.filePaths[0];
};

// Save dialog
const saveFile = async (content: string) => {
  const result = await dialog.showSaveDialog({
    defaultPath: 'docker-compose.yml',
    filters: [
      { name: 'YAML', extensions: ['yml', 'yaml'] }
    ]
  });
  
  if (!result.canceled && result.filePath) {
    await fs.writeFile(result.filePath, content);
  }
};
```

#### **Notification API**
```typescript
import { Notification } from 'electron';

const showNotification = (title: string, body: string) => {
  const notification = new Notification({
    title,
    body,
    icon: path.join(__dirname, 'assets/icon.png')
  });
  
  notification.show();
  
  notification.on('click', () => {
    // Bring app to front
    mainWindow?.show();
    mainWindow?.focus();
  });
};
```

#### **System Tray**
```typescript
import { Tray, Menu } from 'electron';

let tray: Tray;

const createTray = () => {
  tray = new Tray(path.join(__dirname, 'assets/tray-icon.png'));
  
  const contextMenu = Menu.buildFromTemplate([
    { label: 'Show App', click: () => mainWindow?.show() },
    { label: 'Quit', click: () => app.quit() }
  ]);
  
  tray.setContextMenu(contextMenu);
  tray.setToolTip('Auto-Instalador V3 Lite');
};
```

---

### **5. App Lifecycle**

#### **Eventos Principais**
```typescript
import { app } from 'electron';

// App ready
app.whenReady().then(() => {
  createMainWindow();
  createTray();
  registerIPCHandlers();
});

// All windows closed
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// App activate (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

// Before quit
app.on('before-quit', (event) => {
  // Cleanup operations
  cleanupResources();
});
```

#### **Window Management**
```typescript
class WindowManager {
  private mainWindow: BrowserWindow | null = null;

  createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      show: false, // Don't show until ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    // Show when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    // Handle close
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
  }
}
```

---

### **6. Auto Updater**

#### **Configuração Básica**
```typescript
import { autoUpdater } from 'electron-updater';

class UpdateManager {
  constructor() {
    this.setupAutoUpdater();
  }

  private setupAutoUpdater(): void {
    // Configure update server
    autoUpdater.setFeedURL({
      provider: 'github',
      owner: 'auto-instalador-v3',
      repo: 'desktop-app'
    });

    // Events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...');
    });

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version);
    });

    autoUpdater.on('update-downloaded', () => {
      // Notify user and restart
      autoUpdater.quitAndInstall();
    });
  }

  checkForUpdates(): void {
    autoUpdater.checkForUpdatesAndNotify();
  }
}
```

---

### **7. Performance Optimization**

#### **Memory Management**
```typescript
// Garbage collection hints
if (global.gc) {
  setInterval(() => {
    global.gc();
  }, 30000); // Every 30 seconds
}

// Memory monitoring
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB'
  });
}, 60000); // Every minute
```

#### **Process Optimization**
```typescript
// Limit renderer processes
app.commandLine.appendSwitch('--renderer-process-limit', '8');

// Memory limits
app.commandLine.appendSwitch('--max-old-space-size', '4096');

// GPU acceleration
app.commandLine.appendSwitch('--enable-gpu-rasterization');
```

---

### **8. Testing**

#### **Playwright Integration**
```typescript
// tests/electron.spec.ts
import { test, expect } from '@playwright/test';
import { ElectronApplication, _electron as electron } from 'playwright';

test.describe('Electron App', () => {
  let electronApp: ElectronApplication;

  test.beforeAll(async () => {
    electronApp = await electron.launch({ args: ['.'] });
  });

  test.afterAll(async () => {
    await electronApp.close();
  });

  test('should launch app', async () => {
    const window = await electronApp.firstWindow();
    expect(await window.title()).toBe('Auto-Instalador V3 Lite');
  });
});
```

---

## 📋 **BREAKING CHANGES v37**

### **Mudanças Obrigatórias**
1. **contextIsolation:** Agora obrigatório (true)
2. **nodeIntegration:** Deve ser false
3. **webSecurity:** Default mudou para true
4. **Algumas APIs deprecated** foram removidas

### **Migration Guide**
```typescript
// ANTES (v31 - Deprecated)
const window = new BrowserWindow({
  webPreferences: {
    nodeIntegration: true,
    contextIsolation: false
  }
});

// DEPOIS (v37 - Required)
const window = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    preload: path.join(__dirname, 'preload.js')
  }
});
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Electron 37.1.2 Documentation
