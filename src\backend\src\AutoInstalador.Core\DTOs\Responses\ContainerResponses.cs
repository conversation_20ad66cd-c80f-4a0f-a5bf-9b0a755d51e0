using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.DTOs.Responses;

/// <summary>
/// Response base para operações de container
/// </summary>
public class BaseContainerResponse
{
    /// <summary>
    /// Se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Mensagem da operação
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Lista de erros
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Timestamp da operação
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Response para listagem de containers
/// </summary>
public class ContainerListResponse : BaseContainerResponse
{
    /// <summary>
    /// Lista de containers
    /// </summary>
    public List<ContainerDto> Containers { get; set; } = new();

    /// <summary>
    /// Total de containers
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Engine utilizado
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Response para container específico
/// </summary>
public class ContainerResponse : BaseContainerResponse
{
    /// <summary>
    /// Dados do container
    /// </summary>
    public ContainerDto? Container { get; set; }
}

/// <summary>
/// Response para execução de container
/// </summary>
public class ContainerRunResponse : BaseContainerResponse
{
    /// <summary>
    /// ID do container criado
    /// </summary>
    public string? ContainerId { get; set; }

    /// <summary>
    /// Nome do container
    /// </summary>
    public string? ContainerName { get; set; }

    /// <summary>
    /// Engine utilizado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Tempo estimado para inicialização
    /// </summary>
    public TimeSpan? EstimatedStartTime { get; set; }
}

/// <summary>
/// Response para ações de container
/// </summary>
public class ContainerActionResponse : BaseContainerResponse
{
    /// <summary>
    /// ID do container
    /// </summary>
    public string? ContainerId { get; set; }

    /// <summary>
    /// Ação executada
    /// </summary>
    public ContainerAction Action { get; set; }

    /// <summary>
    /// Duração da operação
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// Response para logs de container
/// </summary>
public class ContainerLogsResponse : BaseContainerResponse
{
    /// <summary>
    /// Logs do container
    /// </summary>
    public List<ContainerLogDto> Logs { get; set; } = new();

    /// <summary>
    /// ID do container
    /// </summary>
    public string? ContainerId { get; set; }

    /// <summary>
    /// Total de linhas
    /// </summary>
    public int TotalLines { get; set; }
}

/// <summary>
/// Response para estatísticas de container
/// </summary>
public class ContainerStatsResponse : BaseContainerResponse
{
    /// <summary>
    /// Estatísticas do container
    /// </summary>
    public ContainerStatsDto? Stats { get; set; }
}

/// <summary>
/// Response para execução em container
/// </summary>
public class ContainerExecResponse : BaseContainerResponse
{
    /// <summary>
    /// Código de saída
    /// </summary>
    public int ExitCode { get; set; }

    /// <summary>
    /// Saída padrão
    /// </summary>
    public string StandardOutput { get; set; } = string.Empty;

    /// <summary>
    /// Saída de erro
    /// </summary>
    public string StandardError { get; set; } = string.Empty;

    /// <summary>
    /// Duração da execução
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// Response para listagem de imagens
/// </summary>
public class ContainerImageListResponse : BaseContainerResponse
{
    /// <summary>
    /// Lista de imagens
    /// </summary>
    public List<ContainerImageDto> Images { get; set; } = new();

    /// <summary>
    /// Total de imagens
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Engine utilizado
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Response para download de imagem
/// </summary>
public class ContainerImagePullResponse : BaseContainerResponse
{
    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string? ImageName { get; set; }

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string? Tag { get; set; }

    /// <summary>
    /// ID da imagem baixada
    /// </summary>
    public string? ImageId { get; set; }

    /// <summary>
    /// Tamanho da imagem em bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Duração do download
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// Response para remoção de imagem
/// </summary>
public class ContainerImageRemoveResponse : BaseContainerResponse
{
    /// <summary>
    /// ID da imagem removida
    /// </summary>
    public string? ImageId { get; set; }

    /// <summary>
    /// Espaço liberado em bytes
    /// </summary>
    public long FreedSpace { get; set; }
}

/// <summary>
/// Response para busca de imagens
/// </summary>
public class ContainerImageSearchResponse : BaseContainerResponse
{
    /// <summary>
    /// Resultados da busca
    /// </summary>
    public List<ContainerImageSearchResultDto> Results { get; set; } = new();

    /// <summary>
    /// Termo de busca
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Total de resultados
    /// </summary>
    public int TotalResults { get; set; }
}

/// <summary>
/// Response para listagem de engines
/// </summary>
public class ContainerEngineListResponse : BaseContainerResponse
{
    /// <summary>
    /// Lista de engines
    /// </summary>
    public List<ContainerEngineInfoDto> Engines { get; set; } = new();
}

/// <summary>
/// Response para status de engine
/// </summary>
public class ContainerEngineStatusResponse : BaseContainerResponse
{
    /// <summary>
    /// Engine consultado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Se está disponível
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Se está em execução
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// Versão do engine
    /// </summary>
    public string? Version { get; set; }
}

/// <summary>
/// Response para informações de engine
/// </summary>
public class ContainerEngineInfoResponse : BaseContainerResponse
{
    /// <summary>
    /// Informações do engine
    /// </summary>
    public ContainerEngineInfoDto? EngineInfo { get; set; }
}

/// <summary>
/// Response para instalação de engine
/// </summary>
public class ContainerEngineInstallResponse : BaseContainerResponse
{
    /// <summary>
    /// Engine instalado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Versão instalada
    /// </summary>
    public string? InstalledVersion { get; set; }

    /// <summary>
    /// Duração da instalação
    /// </summary>
    public TimeSpan InstallDuration { get; set; }

    /// <summary>
    /// Se requer reinicialização
    /// </summary>
    public bool RequiresRestart { get; set; }

    /// <summary>
    /// Comandos executados
    /// </summary>
    public List<string> ExecutedCommands { get; set; } = new();
}

/// <summary>
/// Response para detecção de engines
/// </summary>
public class ContainerEngineDetectionResponse : BaseContainerResponse
{
    /// <summary>
    /// Engines detectados
    /// </summary>
    public List<EngineDetectionResultDto> DetectedEngines { get; set; } = new();

    /// <summary>
    /// Engine recomendado
    /// </summary>
    public ContainerEngine? RecommendedEngine { get; set; }
}

// ============================================================================
// DTOs AUXILIARES
// ============================================================================

/// <summary>
/// DTO para container
/// </summary>
public class ContainerDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Image { get; set; } = string.Empty;
    public string ImageId { get; set; } = string.Empty;
    public ContainerStatus Status { get; set; }
    public ContainerState State { get; set; }
    public ContainerEngine Engine { get; set; }
    public string Platform { get; set; } = string.Empty;
    public string Command { get; set; } = string.Empty;
    public string[] Args { get; set; } = Array.Empty<string>();
    public string? WorkingDir { get; set; }
    public string? User { get; set; }
    public string NetworkMode { get; set; } = string.Empty;
    public RestartPolicy RestartPolicy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? FinishedAt { get; set; }
    public int? ExitCode { get; set; }
    public List<ContainerPortDto> Ports { get; set; } = new();
    public List<ContainerVolumeDto> Volumes { get; set; } = new();
    public Dictionary<string, string> Environment { get; set; } = new();
    public Dictionary<string, string> Labels { get; set; } = new();
    public ContainerResourcesDto? Resources { get; set; }
}

/// <summary>
/// DTO para log de container
/// </summary>
public class ContainerLogDto
{
    public DateTime Timestamp { get; set; }
    public LogStream Stream { get; set; }
    public string Message { get; set; } = string.Empty;
    public string ContainerId { get; set; } = string.Empty;
}

/// <summary>
/// DTO para estatísticas de container
/// </summary>
public class ContainerStatsDto
{
    public string ContainerId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public CpuStatsDto Cpu { get; set; } = new();
    public MemoryStatsDto Memory { get; set; } = new();
    public NetworkStatsDto Network { get; set; } = new();
    public BlockIOStatsDto BlockIO { get; set; } = new();
    public int Pids { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// DTO para estatísticas de CPU
/// </summary>
public class CpuStatsDto
{
    public double Usage { get; set; }
    public long SystemUsage { get; set; }
    public int OnlineCpus { get; set; }
    public long ThrottledTime { get; set; }
}

/// <summary>
/// DTO para estatísticas de memória
/// </summary>
public class MemoryStatsDto
{
    public long Usage { get; set; }
    public long Limit { get; set; }
    public double Percentage { get; set; }
    public long Cache { get; set; }
    public long? Swap { get; set; }
}

/// <summary>
/// DTO para estatísticas de rede
/// </summary>
public class NetworkStatsDto
{
    public long RxBytes { get; set; }
    public long TxBytes { get; set; }
    public long RxPackets { get; set; }
    public long TxPackets { get; set; }
    public long RxErrors { get; set; }
    public long TxErrors { get; set; }
}

/// <summary>
/// DTO para estatísticas de I/O
/// </summary>
public class BlockIOStatsDto
{
    public long ReadBytes { get; set; }
    public long WriteBytes { get; set; }
    public long ReadOps { get; set; }
    public long WriteOps { get; set; }
}

/// <summary>
/// DTO para imagem de container
/// </summary>
public class ContainerImageDto
{
    public string Id { get; set; } = string.Empty;
    public string Repository { get; set; } = string.Empty;
    public string Tag { get; set; } = string.Empty;
    public string? Digest { get; set; }
    public long Size { get; set; }
    public DateTime Created { get; set; }
    public Dictionary<string, string> Labels { get; set; } = new();
    public string Architecture { get; set; } = string.Empty;
    public string Os { get; set; } = string.Empty;
    public ContainerEngine Engine { get; set; }
}

/// <summary>
/// DTO para resultado de busca de imagem
/// </summary>
public class ContainerImageSearchResultDto
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Stars { get; set; }
    public bool IsOfficial { get; set; }
    public bool IsAutomated { get; set; }
}

/// <summary>
/// DTO para informações de engine
/// </summary>
public class ContainerEngineInfoDto
{
    public ContainerEngine Engine { get; set; }
    public string Version { get; set; } = string.Empty;
    public string ApiVersion { get; set; } = string.Empty;
    public bool IsAvailable { get; set; }
    public bool IsRunning { get; set; }
    public bool Rootless { get; set; }
    public string StorageDriver { get; set; } = string.Empty;
    public string CgroupVersion { get; set; } = string.Empty;
    public PlatformInfoDto Platform { get; set; } = new();
    public RuntimeInfoDto Runtime { get; set; } = new();
}

/// <summary>
/// DTO para informações de plataforma
/// </summary>
public class PlatformInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string Architecture { get; set; } = string.Empty;
    public string Os { get; set; } = string.Empty;
}

/// <summary>
/// DTO para informações de runtime
/// </summary>
public class RuntimeInfoDto
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
}

/// <summary>
/// DTO para resultado de detecção de engine
/// </summary>
public class EngineDetectionResultDto
{
    public ContainerEngine Engine { get; set; }
    public bool IsDetected { get; set; }
    public string? Version { get; set; }
    public string? ExecutablePath { get; set; }
    public bool IsRunning { get; set; }
    public string? ErrorMessage { get; set; }
}
