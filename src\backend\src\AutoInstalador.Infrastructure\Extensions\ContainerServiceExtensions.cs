using AutoInstalador.Core.Interfaces.Services;
using AutoInstalador.Infrastructure.External.Containers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AutoInstalador.Infrastructure.Extensions;

/// <summary>
/// Extensões para configuração de serviços de container
/// </summary>
public static class ContainerServiceExtensions
{
    /// <summary>
    /// Adiciona serviços de container ao container de DI
    /// </summary>
    /// <param name="services">Collection de serviços</param>
    /// <returns>Collection de serviços</returns>
    public static IServiceCollection AddContainerServices(this IServiceCollection services)
    {
        // Register container engines
        services.AddSingleton<DockerService>();
        services.AddSingleton<PodmanService>();
        services.AddSingleton<ContainerEngineDetector>();

        // Register container services
        services.AddScoped<IContainerService, ContainerManagerService>();
        services.AddScoped<IContainerImageService, ContainerImageManagerService>();
        services.AddScoped<IContainerEngineService, ContainerEngineManagerService>();

        // Register container engine detector
        services.AddScoped<IContainerEngineDetector, ContainerEngineDetector>();

        return services;
    }

    /// <summary>
    /// Adiciona health checks para container engines
    /// </summary>
    /// <param name="services">Collection de serviços</param>
    /// <returns>Health checks builder</returns>
    public static IServiceCollection AddContainerHealthChecks(this IServiceCollection services)
    {
        services.AddHealthChecks()
            .AddCheck<ContainerEngineHealthCheck>("container-engines", tags: new[] { "ready" });

        return services;
    }
}

/// <summary>
/// Health check para verificar status dos engines de container
/// </summary>
public class ContainerEngineHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IContainerEngineDetector _engineDetector;
    private readonly ILogger<ContainerEngineHealthCheck> _logger;

    public ContainerEngineHealthCheck(
        IContainerEngineDetector engineDetector,
        ILogger<ContainerEngineHealthCheck> logger)
    {
        _engineDetector = engineDetector;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var engines = await _engineDetector.DetectEnginesAsync(cancellationToken);
            var availableEngines = engines.Where(e => e.IsAvailable).ToList();

            if (availableEngines.Any())
            {
                var engineNames = string.Join(", ", availableEngines.Select(e => e.Engine.ToString()));
                var runningEngines = availableEngines.Where(e => e.IsRunning).ToList();
                
                if (runningEngines.Any())
                {
                    var runningNames = string.Join(", ", runningEngines.Select(e => e.Engine.ToString()));
                    return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                        $"Container engines disponíveis: {engineNames}. Em execução: {runningNames}");
                }
                else
                {
                    return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded(
                        $"Container engines disponíveis: {engineNames}, mas nenhum está em execução");
                }
            }

            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded(
                "Nenhum engine de container disponível");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar status dos engines de container");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Erro ao verificar engines de container", ex);
        }
    }
}
