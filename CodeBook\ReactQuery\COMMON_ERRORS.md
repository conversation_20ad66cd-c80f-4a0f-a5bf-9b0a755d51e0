# React Query 5.56.2 - <PERSON><PERSON><PERSON> Comuns e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.56.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tanstack.com/query/latest
- **GitHub:** https://github.com/TanStack/query
- **Documentação:** https://tanstack.com/query/latest/docs/framework/react/overview
- **NPM/Package:** https://www.npmjs.com/package/@tanstack/react-query
- **Fórum/Community:** https://github.com/TanStack/query/discussions
- **Stack Overflow Tag:** `react-query`

---

## 🚨 **ERROS CRÍTICOS REACT QUERY 5.56**

### **1. Query Key Inconsistencies**

#### **Erro:**
```
Query data not updating
Stale data being returned
Cache not invalidating properly
```

#### **Causa:**
Query keys inconsistentes ou mal estruturadas.

#### **Solução:**
```typescript
// ❌ ERRADO - Query keys inconsistentes
function BadQueryKeys() {
  // Diferentes estruturas para mesmos dados
  const query1 = useQuery({
    queryKey: ['containers', id], // Array
    queryFn: fetchContainer
  });
  
  const query2 = useQuery({
    queryKey: `containers-${id}`, // String
    queryFn: fetchContainer
  });
  
  // Invalidação não funciona
  queryClient.invalidateQueries({ queryKey: ['containers'] }); // Não afeta query2
}

// ✅ CORRETO - Query keys consistentes
const containerKeys = {
  all: ['containers'] as const,
  lists: () => [...containerKeys.all, 'list'] as const,
  list: (filters: string) => [...containerKeys.lists(), { filters }] as const,
  details: () => [...containerKeys.all, 'detail'] as const,
  detail: (id: string) => [...containerKeys.details(), id] as const,
  stats: (id: string) => [...containerKeys.detail(id), 'stats'] as const
};

function GoodQueryKeys() {
  const containerQuery = useQuery({
    queryKey: containerKeys.detail(id),
    queryFn: () => fetchContainer(id)
  });
  
  const statsQuery = useQuery({
    queryKey: containerKeys.stats(id),
    queryFn: () => fetchContainerStats(id)
  });
  
  // Invalidação funciona corretamente
  const invalidateContainer = () => {
    queryClient.invalidateQueries({ queryKey: containerKeys.detail(id) });
    queryClient.invalidateQueries({ queryKey: containerKeys.stats(id) });
  };
}
```

---

### **2. Memory Leaks com Queries**

#### **Erro:**
```
Memory usage increasing over time
Queries not being garbage collected
Application becoming slow
```

#### **Causa:**
Queries não sendo limpas adequadamente ou configurações de cache inadequadas.

#### **Solução:**
```typescript
// ❌ ERRADO - Sem limpeza adequada
function MemoryLeakExample() {
  const [containerId, setContainerId] = useState('');
  
  // Query sempre ativa, mesmo quando não necessária
  const { data } = useQuery({
    queryKey: ['containers', containerId, 'stats'],
    queryFn: () => fetchContainerStats(containerId),
    refetchInterval: 1000, // Muito frequente
    gcTime: Infinity, // Nunca limpa o cache
    enabled: true // Sempre habilitada
  });
  
  return <div>{data?.cpuUsage}</div>;
}

// ✅ CORRETO - Com limpeza adequada
function MemoryOptimizedExample() {
  const [containerId, setContainerId] = useState('');
  const [isVisible, setIsVisible] = useState(true);
  
  const { data } = useQuery({
    queryKey: ['containers', containerId, 'stats'],
    queryFn: () => fetchContainerStats(containerId),
    enabled: !!containerId && isVisible, // Só quando necessário
    refetchInterval: isVisible ? 3000 : false, // Parar quando não visível
    gcTime: 5 * 60 * 1000, // 5 minutos
    staleTime: 2 * 1000, // 2 segundos
    retry: false // Não retry para dados em tempo real
  });
  
  // Cleanup quando componente desmonta
  useEffect(() => {
    return () => {
      queryClient.removeQueries({ 
        queryKey: ['containers', containerId, 'stats'] 
      });
    };
  }, [containerId]);
  
  return <div>{data?.cpuUsage}</div>;
}

// Hook para limpeza automática
function useQueryCleanup() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const cleanup = setInterval(() => {
      // Remover queries antigas
      const queries = queryClient.getQueryCache().getAll();
      const now = Date.now();
      
      queries.forEach(query => {
        const lastUpdated = query.state.dataUpdatedAt;
        if (now - lastUpdated > 10 * 60 * 1000) { // 10 minutos
          queryClient.removeQueries({ queryKey: query.queryKey });
        }
      });
    }, 5 * 60 * 1000); // A cada 5 minutos
    
    return () => clearInterval(cleanup);
  }, [queryClient]);
}
```

---

### **3. Optimistic Updates Failing**

#### **Erro:**
```
Optimistic updates not working
UI not updating immediately
Rollback not happening on error
```

#### **Causa:**
Implementação incorreta de optimistic updates ou problemas com rollback.

#### **Solução:**
```typescript
// ❌ ERRADO - Optimistic update incompleto
function BadOptimisticUpdate() {
  const mutation = useMutation({
    mutationFn: updateContainer,
    onMutate: async (updatedContainer) => {
      // Sem cancelamento de queries
      // Sem snapshot
      // Update direto sem verificações
      queryClient.setQueryData(['containers', updatedContainer.id], updatedContainer);
    },
    // Sem onError para rollback
    // Sem onSettled para refetch
  });
}

// ✅ CORRETO - Optimistic update completo
function GoodOptimisticUpdate() {
  const queryClient = useQueryClient();
  
  const mutation = useMutation({
    mutationFn: updateContainer,
    onMutate: async (updatedContainer) => {
      // 1. Cancelar queries em andamento
      await queryClient.cancelQueries({ 
        queryKey: ['containers', updatedContainer.id] 
      });
      
      // 2. Snapshot do estado anterior
      const previousContainer = queryClient.getQueryData<Container>([
        'containers', 
        updatedContainer.id
      ]);
      
      // 3. Update otimista
      queryClient.setQueryData<Container>(
        ['containers', updatedContainer.id],
        (old) => old ? { ...old, ...updatedContainer } : updatedContainer
      );
      
      // 4. Update na lista também
      queryClient.setQueryData<Container[]>(['containers'], (old) => {
        return old?.map(container => 
          container.id === updatedContainer.id 
            ? { ...container, ...updatedContainer }
            : container
        );
      });
      
      // 5. Retornar contexto para rollback
      return { previousContainer };
    },
    onError: (err, updatedContainer, context) => {
      // 6. Rollback em caso de erro
      if (context?.previousContainer) {
        queryClient.setQueryData(
          ['containers', updatedContainer.id],
          context.previousContainer
        );
        
        // Rollback na lista também
        queryClient.setQueryData<Container[]>(['containers'], (old) => {
          return old?.map(container => 
            container.id === updatedContainer.id 
              ? context.previousContainer!
              : container
          );
        });
      }
      
      // 7. Notificar erro ao usuário
      showErrorNotification('Falha ao atualizar container');
    },
    onSuccess: (data, updatedContainer) => {
      // 8. Update com dados reais do servidor
      queryClient.setQueryData(['containers', updatedContainer.id], data);
    },
    onSettled: (data, error, updatedContainer) => {
      // 9. Sempre refetch para garantir consistência
      queryClient.invalidateQueries({ 
        queryKey: ['containers', updatedContainer.id] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['containers'] 
      });
    }
  });
  
  return mutation;
}
```

---

### **4. Infinite Query Issues**

#### **Erro:**
```
Infinite query not loading more pages
Duplicate data in infinite query
getNextPageParam not working
```

#### **Causa:**
Configuração incorreta de infinite queries ou problemas com paginação.

#### **Solução:**
```typescript
// ❌ ERRADO - Infinite query mal configurada
function BadInfiniteQuery() {
  const query = useInfiniteQuery({
    queryKey: ['logs'],
    queryFn: fetchLogs, // Sem pageParam
    getNextPageParam: (lastPage) => lastPage.nextPage, // Propriedade errada
    // Sem initialPageParam
  });
}

// ✅ CORRETO - Infinite query bem configurada
interface LogsResponse {
  logs: LogEntry[];
  nextCursor?: string;
  hasMore: boolean;
  totalCount: number;
}

function GoodInfiniteQuery(containerId: string) {
  const query = useInfiniteQuery({
    queryKey: ['containers', containerId, 'logs'],
    queryFn: ({ pageParam }) => fetchContainerLogs(containerId, pageParam),
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage: LogsResponse) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    getPreviousPageParam: (firstPage: LogsResponse) => {
      return firstPage.previousCursor;
    },
    maxPages: 20, // Limitar para performance
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
    enabled: !!containerId,
    retry: (failureCount, error) => {
      // Não retry para dados em tempo real
      return false;
    }
  });
  
  // Função helper para carregar mais
  const loadMore = useCallback(() => {
    if (query.hasNextPage && !query.isFetchingNextPage) {
      query.fetchNextPage();
    }
  }, [query.hasNextPage, query.isFetchingNextPage, query.fetchNextPage]);
  
  // Dados flattened
  const allLogs = useMemo(() => {
    return query.data?.pages.flatMap(page => page.logs) || [];
  }, [query.data]);
  
  return {
    ...query,
    allLogs,
    loadMore,
    totalLogs: query.data?.pages[0]?.totalCount || 0
  };
}

// API function correta
async function fetchContainerLogs(
  containerId: string, 
  cursor?: string
): Promise<LogsResponse> {
  const response = await window.electronAPI.invoke('containers:logs', {
    containerId,
    cursor,
    limit: 50
  });
  
  return {
    logs: response.logs,
    nextCursor: response.nextCursor,
    previousCursor: response.previousCursor,
    hasMore: response.hasMore,
    totalCount: response.totalCount
  };
}
```

---

### **5. React 19.2 Compatibility Issues**

#### **Erro:**
```
useSuspenseQuery not working with React 19.2
useActionState conflicts with mutations
Concurrent features causing issues
```

#### **Causa:**
Incompatibilidade entre React Query e novos recursos do React 19.2.

#### **Solução:**
```typescript
// ❌ ERRADO - Conflito entre hooks
import { useActionState } from 'react';
import { useMutation } from '@tanstack/react-query';

function ConflictingHooks() {
  const [state, formAction] = useActionState(async () => {}, {});
  const mutation = useMutation({ mutationFn: createContainer });
  
  // Ambos tentando controlar o mesmo estado
  const handleSubmit = () => {
    mutation.mutate(data); // Conflito com formAction
  };
}

// ✅ CORRETO - Integração adequada
import { useActionState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';

function ProperIntegration() {
  const queryClient = useQueryClient();
  const createMutation = useMutation({
    mutationFn: createContainer,
    onSuccess: (data) => {
      queryClient.setQueryData(['containers'], (old: Container[]) => 
        old ? [...old, data] : [data]
      );
    }
  });
  
  const [state, formAction, isPending] = useActionState(
    async (previousState: any, formData: FormData) => {
      try {
        const containerData = Object.fromEntries(formData);
        const result = await createMutation.mutateAsync(containerData);
        return { success: true, data: result };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    },
    { success: false }
  );
  
  return (
    <form action={formAction}>
      <input name="name" required />
      <button 
        type="submit" 
        disabled={isPending || createMutation.isPending}
      >
        {isPending ? 'Criando...' : 'Criar'}
      </button>
      
      {state.error && <div className="error">{state.error}</div>}
      {state.success && <div className="success">Criado com sucesso!</div>}
    </form>
  );
}

// Suspense Query com React 19.2
import { useSuspenseQuery } from '@tanstack/react-query';
import { Suspense, use } from 'react';

function SuspenseIntegration({ containerPromise }: { containerPromise: Promise<Container> }) {
  // Usar use() para promises externas
  const container = use(containerPromise);
  
  // useSuspenseQuery para queries internas
  const { data: stats } = useSuspenseQuery({
    queryKey: ['containers', container.id, 'stats'],
    queryFn: () => fetchContainerStats(container.id),
    staleTime: 2 * 1000
  });
  
  return (
    <div>
      <h3>{container.name}</h3>
      <p>CPU: {stats.cpuUsage}%</p>
    </div>
  );
}

// Wrapper com Suspense
function App() {
  const containerPromise = fetchContainer('container-id');
  
  return (
    <Suspense fallback={<div>Carregando container...</div>}>
      <SuspenseIntegration containerPromise={containerPromise} />
    </Suspense>
  );
}
```

---

### **6. TypeScript Errors**

#### **Erro:**
```
Type errors with query data
Generic types not inferred correctly
QueryKey types not working
```

#### **Causa:**
Configuração incorreta de tipos TypeScript.

#### **Solução:**
```typescript
// ❌ ERRADO - Tipos não definidos
function BadTypes() {
  const query = useQuery({
    queryKey: ['containers'],
    queryFn: fetchContainers // Tipo não inferido
  });
  
  // data pode ser undefined, mas TypeScript não sabe
  const firstContainer = query.data[0]; // Erro potencial
}

// ✅ CORRETO - Tipos explícitos
interface Container {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'error';
  image: string;
  createdAt: string;
}

// Query keys tipadas
const containerKeys = {
  all: ['containers'] as const,
  lists: () => [...containerKeys.all, 'list'] as const,
  list: (filters: ContainerFilters) => [...containerKeys.lists(), filters] as const,
  details: () => [...containerKeys.all, 'detail'] as const,
  detail: (id: string) => [...containerKeys.details(), id] as const
} satisfies Record<string, readonly unknown[]>;

// Hook tipado
function useContainers(filters?: ContainerFilters) {
  return useQuery<Container[], Error>({
    queryKey: containerKeys.list(filters || {}),
    queryFn: async (): Promise<Container[]> => {
      const response = await window.electronAPI.invoke('containers:list', filters);
      return response;
    },
    staleTime: 30 * 1000,
    select: (data: Container[]) => {
      return data.sort((a, b) => a.name.localeCompare(b.name));
    }
  });
}

// Mutation tipada
interface CreateContainerData {
  name: string;
  image: string;
  ports?: string[];
  environment?: Record<string, string>;
}

function useCreateContainer() {
  const queryClient = useQueryClient();
  
  return useMutation<Container, Error, CreateContainerData>({
    mutationFn: async (data: CreateContainerData): Promise<Container> => {
      return await window.electronAPI.invoke('containers:create', data);
    },
    onSuccess: (newContainer: Container) => {
      queryClient.setQueryData<Container[]>(
        containerKeys.lists(),
        (old) => old ? [...old, newContainer] : [newContainer]
      );
    }
  });
}

// Uso com tipos seguros
function TypeSafeComponent() {
  const { data: containers, isLoading, error } = useContainers();
  const createMutation = useCreateContainer();
  
  if (isLoading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error.message}</div>;
  
  // containers é Container[] | undefined, mas TypeScript sabe disso
  return (
    <div>
      {containers?.map(container => (
        <div key={container.id}>
          {container.name} - {container.status}
        </div>
      ))}
    </div>
  );
}
```

---

### **7. Network and Offline Issues**

#### **Erro:**
```
Queries failing when offline
Network errors not handled properly
Retry logic not working
```

#### **Causa:**
Configuração inadequada para cenários offline ou problemas de rede.

#### **Solução:**
```typescript
// ❌ ERRADO - Sem tratamento offline
function BadNetworkHandling() {
  const query = useQuery({
    queryKey: ['containers'],
    queryFn: fetchContainers,
    // Sem configuração de rede
    // Sem tratamento de offline
  });
}

// ✅ CORRETO - Com tratamento offline
function GoodNetworkHandling() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  const query = useQuery({
    queryKey: ['containers'],
    queryFn: fetchContainers,
    networkMode: isOnline ? 'online' : 'offlineFirst',
    retry: (failureCount, error) => {
      // Não retry se offline
      if (!isOnline) return false;
      
      // Retry baseado no tipo de erro
      if (error.name === 'NetworkError') {
        return failureCount < 5;
      }
      
      if (error.status >= 500) {
        return failureCount < 3;
      }
      
      return false;
    },
    retryDelay: (attemptIndex) => {
      // Delay exponencial com jitter
      const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);
      const jitter = Math.random() * 1000;
      return baseDelay + jitter;
    },
    staleTime: isOnline ? 30 * 1000 : Infinity,
    gcTime: isOnline ? 5 * 60 * 1000 : 24 * 60 * 60 * 1000 // 24h offline
  });
  
  return {
    ...query,
    isOnline
  };
}

// QueryClient com configuração offline
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      networkMode: 'online',
      retry: (failureCount, error) => {
        if (!navigator.onLine) return false;
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    mutations: {
      networkMode: 'online',
      retry: (failureCount, error) => {
        if (!navigator.onLine) return false;
        if (error.status >= 500) return failureCount < 2;
        return false;
      }
    }
  }
});
```

---

## 🔧 **DEBUGGING TOOLS**

### **Query Inspector**
```typescript
// Debug helper para queries
function QueryInspector() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const cache = queryClient.getQueryCache();
    
    const unsubscribe = cache.subscribe((event) => {
      console.log('Query cache event:', event);
    });
    
    return unsubscribe;
  }, [queryClient]);
  
  const logCacheState = () => {
    const queries = queryClient.getQueryCache().getAll();
    console.table(queries.map(query => ({
      key: JSON.stringify(query.queryKey),
      state: query.state.status,
      dataUpdatedAt: new Date(query.state.dataUpdatedAt).toLocaleTimeString(),
      observers: query.getObserversCount()
    })));
  };
  
  return (
    <button onClick={logCacheState}>
      Log Cache State
    </button>
  );
}
```

### **Performance Monitor**
```typescript
// Monitor de performance
function useQueryPerformance() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('react-query')) {
          console.log('React Query performance:', entry);
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    return () => observer.disconnect();
  }, []);
  
  const getMemoryUsage = () => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      estimatedSize: JSON.stringify(queries).length
    };
  };
  
  return { getMemoryUsage };
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - React Query 5.56.2 Common Errors
