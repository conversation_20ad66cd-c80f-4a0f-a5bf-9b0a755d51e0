# Auto-Instalador V3 Lite - Setup Completo
# Otimizado para Intel i5 12ª Gen, 32GB RAM, SSD 512GB
# PowerShell Script para Windows

param(
    [switch]$SkipNodeCheck,
    [switch]$SkipDotNetCheck,
    [switch]$Force
)

Write-Host "🚀 Configurando Auto-Instalador V3 Lite Desktop..." -ForegroundColor Green
Write-Host "Hardware Target: Intel i5 12ª Gen, 32GB RAM, SSD 512GB" -ForegroundColor Cyan
Write-Host ""

# Função para verificar se comando existe
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# Função para executar comando com verificação
function Invoke-SafeCommand {
    param($Command, $Description)
    
    Write-Host "📦 $Description..." -ForegroundColor Yellow
    
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description concluído" -ForegroundColor Green
        } else {
            Write-Host "❌ Erro em: $Description" -ForegroundColor Red
            exit 1
        }
    }
    catch {
        Write-Host "❌ Erro executando: $Command" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        exit 1
    }
}

# 1. Verificar Node.js
if (-not $SkipNodeCheck) {
    Write-Host "🔍 Verificando Node.js..." -ForegroundColor Cyan
    
    if (Test-Command "node") {
        $nodeVersion = node --version
        Write-Host "Node.js encontrado: $nodeVersion" -ForegroundColor Green
        
        if ($nodeVersion -notmatch "v22\.7\.0") {
            Write-Host "⚠️  Versão recomendada: v22.7.0" -ForegroundColor Yellow
            Write-Host "Instale via: winget install OpenJS.NodeJS" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Node.js não encontrado!" -ForegroundColor Red
        Write-Host "Instale via: winget install OpenJS.NodeJS" -ForegroundColor Yellow
        exit 1
    }
}

# 2. Verificar .NET SDK
if (-not $SkipDotNetCheck) {
    Write-Host "🔍 Verificando .NET SDK..." -ForegroundColor Cyan
    
    if (Test-Command "dotnet") {
        $dotnetVersion = dotnet --version
        Write-Host ".NET SDK encontrado: $dotnetVersion" -ForegroundColor Green
        
        if ($dotnetVersion -notmatch "9\.0\.") {
            Write-Host "⚠️  Versão recomendada: 9.0.x" -ForegroundColor Yellow
            Write-Host "Instale via: winget install Microsoft.DotNet.SDK.9" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ .NET SDK não encontrado!" -ForegroundColor Red
        Write-Host "Instale via: winget install Microsoft.DotNet.SDK.9" -ForegroundColor Yellow
        exit 1
    }
}

# 3. Criar estrutura do projeto
Write-Host "📁 Criando estrutura do projeto..." -ForegroundColor Cyan

$projectStructure = @(
    # Frontend structure
    "src/frontend/electron/main",
    "src/frontend/electron/preload",
    "src/frontend/electron/renderer",
    "src/frontend/renderer/components/ui",
    "src/frontend/renderer/components/layout",
    "src/frontend/renderer/components/features",
    "src/frontend/renderer/components/forms",
    "src/frontend/renderer/pages",
    "src/frontend/renderer/hooks",
    "src/frontend/renderer/services",
    "src/frontend/renderer/store",
    "src/frontend/renderer/types",
    "src/frontend/renderer/utils",
    "src/frontend/renderer/styles",
    "src/frontend/renderer/assets",

    # Backend structure
    "src/backend/src/AutoInstalador.API/Controllers",
    "src/backend/src/AutoInstalador.API/Middleware",
    "src/backend/src/AutoInstalador.API/Configuration",
    "src/backend/src/AutoInstalador.Core/Entities",
    "src/backend/src/AutoInstalador.Core/Interfaces/Repositories",
    "src/backend/src/AutoInstalador.Core/Interfaces/Services",
    "src/backend/src/AutoInstalador.Core/DTOs/Requests",
    "src/backend/src/AutoInstalador.Core/DTOs/Responses",
    "src/backend/src/AutoInstalador.Core/Enums",
    "src/backend/src/AutoInstalador.Core/Exceptions",
    "src/backend/src/AutoInstalador.Application/Services",
    "src/backend/src/AutoInstalador.Application/Validators",
    "src/backend/src/AutoInstalador.Application/Mappers",
    "src/backend/src/AutoInstalador.Infrastructure/Data/Repositories",
    "src/backend/src/AutoInstalador.Infrastructure/External",
    "src/backend/src/AutoInstalador.Infrastructure/Logging",
    "src/backend/src/AutoInstalador.Shared/Extensions",
    "src/backend/src/AutoInstalador.Shared/Helpers",
    "src/backend/tests/AutoInstalador.UnitTests/Services",
    "src/backend/tests/AutoInstalador.UnitTests/Controllers",
    "src/backend/tests/AutoInstalador.IntegrationTests/API",
    "src/backend/tests/AutoInstalador.E2ETests/Scenarios",

    # Shared resources
    "shared/types",
    "shared/contracts",
    "shared/schemas",

    # Frontend tests
    "tests/unit/components",
    "tests/unit/hooks",
    "tests/unit/services",
    "tests/integration/electron",
    "tests/integration/api",
    "tests/e2e/scenarios",
    "tests/contracts",

    # Build and assets
    "build/frontend",
    "build/backend",
    "build/release",
    "assets/images",
    "assets/icons"
)

foreach ($dir in $projectStructure) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Criado: $dir" -ForegroundColor Green
    }
}

# 3.1. Criar estrutura backend .NET
Write-Host "🏗️ Criando estrutura backend .NET..." -ForegroundColor Cyan

# Navegar para diretório backend
Set-Location "src/backend"

# Criar solution
if (-not (Test-Path "AutoInstalador.sln")) {
    Invoke-SafeCommand "dotnet new sln -n AutoInstalador" "Criação da solution .NET"
}

# Criar projetos .NET
$dotnetProjects = @(
    @{ Name = "AutoInstalador.API"; Template = "webapi"; Path = "src/AutoInstalador.API" },
    @{ Name = "AutoInstalador.Core"; Template = "classlib"; Path = "src/AutoInstalador.Core" },
    @{ Name = "AutoInstalador.Application"; Template = "classlib"; Path = "src/AutoInstalador.Application" },
    @{ Name = "AutoInstalador.Infrastructure"; Template = "classlib"; Path = "src/AutoInstalador.Infrastructure" },
    @{ Name = "AutoInstalador.Shared"; Template = "classlib"; Path = "src/AutoInstalador.Shared" },
    @{ Name = "AutoInstalador.UnitTests"; Template = "xunit"; Path = "tests/AutoInstalador.UnitTests" },
    @{ Name = "AutoInstalador.IntegrationTests"; Template = "xunit"; Path = "tests/AutoInstalador.IntegrationTests" },
    @{ Name = "AutoInstalador.E2ETests"; Template = "xunit"; Path = "tests/AutoInstalador.E2ETests" }
)

foreach ($project in $dotnetProjects) {
    if (-not (Test-Path "$($project.Path)/$($project.Name).csproj")) {
        Invoke-SafeCommand "dotnet new $($project.Template) -n $($project.Name) -o $($project.Path)" "Criação do projeto $($project.Name)"
        Invoke-SafeCommand "dotnet sln add $($project.Path)/$($project.Name).csproj" "Adição do projeto $($project.Name) à solution"
    }
}

# Adicionar referências entre projetos
$projectReferences = @(
    @{ Project = "src/AutoInstalador.API"; Reference = "src/AutoInstalador.Application" },
    @{ Project = "src/AutoInstalador.API"; Reference = "src/AutoInstalador.Infrastructure" },
    @{ Project = "src/AutoInstalador.Application"; Reference = "src/AutoInstalador.Core" },
    @{ Project = "src/AutoInstalador.Infrastructure"; Reference = "src/AutoInstalador.Core" },
    @{ Project = "src/AutoInstalador.Infrastructure"; Reference = "src/AutoInstalador.Shared" },
    @{ Project = "tests/AutoInstalador.UnitTests"; Reference = "src/AutoInstalador.Application" },
    @{ Project = "tests/AutoInstalador.IntegrationTests"; Reference = "src/AutoInstalador.API" },
    @{ Project = "tests/AutoInstalador.E2ETests"; Reference = "src/AutoInstalador.API" }
)

foreach ($ref in $projectReferences) {
    Invoke-SafeCommand "dotnet add $($ref.Project) reference $($ref.Reference)" "Adição de referência $($ref.Reference) ao projeto $($ref.Project)"
}

# Voltar ao diretório raiz
Set-Location "../.."

Write-Host "✅ Estrutura backend .NET criada" -ForegroundColor Green

# 4. Inicializar package.json se não existir
if (-not (Test-Path "package.json") -or $Force) {
    Write-Host "📦 Inicializando package.json..." -ForegroundColor Cyan
    
    $packageJson = @{
        name = "auto-instalador-v3-lite-desktop"
        version = "1.0.0"
        description = "Auto-Instalador V3 Lite - Desktop Application (Versões Mais Recentes)"
        main = "dist/electron/main/main.js"
        homepage = "./"
        engines = @{
            node = "22.7.0"
            npm = "10.8.2"
        }
        config = @{
            target_platform = "i5-12th-gen"
            max_memory = "32gb"
            storage = "512gb-ssd"
        }
        scripts = @{
            dev = "concurrently `"npm run dev:react`" `"npm run dev:electron`""
            "dev:react" = "vite --port 3000 --host"
            "dev:electron" = "wait-on http://localhost:3000 && electron ."
            build = "npm run build:clean && npm run build:react && npm run build:electron"
            "build:clean" = "rimraf dist release"
            "build:react" = "vite build"
            "build:electron" = "tsc -p tsconfig.electron.json"
            dist = "npm run build && electron-builder"
            "dist:win" = "npm run build && electron-builder --win"
            "dist:linux" = "npm run build && electron-builder --linux"
            "dist:mac" = "npm run build && electron-builder --mac"
            test = "vitest run"
            "test:coverage" = "vitest run --coverage"
            "test:e2e" = "playwright test"
            lint = "eslint . --ext .ts,.tsx"
            "lint:fix" = "eslint . --ext .ts,.tsx --fix"
            format = "prettier --write `"src/**/*.{ts,tsx,js,jsx,json,css,md}`""
            "format:check" = "prettier --check `"src/**/*.{ts,tsx,js,jsx,json,css,md}`""
        }
    }
    
    $packageJson | ConvertTo-Json -Depth 10 | Out-File -FilePath "package.json" -Encoding UTF8
    Write-Host "✅ package.json criado" -ForegroundColor Green
}

# 5. Instalar dependências de produção
Write-Host "📦 Instalando dependências de produção..." -ForegroundColor Cyan

$prodDependencies = @(
    "react@19.2.0",
    "react-dom@19.2.0",
    "react-router-dom@6.26.0",
    "@tanstack/react-query@5.56.2",
    "framer-motion@11.5.4",
    "tailwindcss@4.0.0-beta.1",
    "lucide-react@0.445.0",
    "better-sqlite3@11.3.0",
    "ioredis@5.4.1",
    "zustand@5.0.0",
    "react-hook-form@7.52.2",
    "zod@3.23.8",
    "electron-updater@6.2.1",
    "electron-store@10.0.0",
    "electron-log@5.1.7",
    "axios@1.7.4",
    "date-fns@3.6.0",
    "uuid@10.0.0",
    "clsx@2.1.1",
    "tailwind-merge@2.5.2"
)

$prodCommand = "npm install " + ($prodDependencies -join " ")
Invoke-SafeCommand $prodCommand "Instalação de dependências de produção"

# 6. Instalar dependências de desenvolvimento
Write-Host "🔧 Instalando dependências de desenvolvimento..." -ForegroundColor Cyan

$devDependencies = @(
    "electron@37.1.2",
    "typescript@5.6.2",
    "@types/react@19.2.0",
    "@types/react-dom@19.2.0",
    "@types/node@22.5.0",
    "@types/uuid@10.0.0",
    "@types/better-sqlite3@7.6.11",
    "vite@5.4.2",
    "@vitejs/plugin-react@4.3.1",
    "electron-builder@25.0.5",
    "@playwright/test@1.47.0",
    "eslint@9.9.1",
    "@typescript-eslint/parser@8.2.0",
    "@typescript-eslint/eslint-plugin@8.2.0",
    "prettier@3.3.3",
    "vitest@2.0.5",
    "@vitest/ui@2.0.5",
    "@vitest/coverage-v8@2.0.5",
    "@testing-library/react@16.0.0",
    "@testing-library/jest-dom@6.4.8",
    "concurrently@8.2.2",
    "wait-on@7.2.0",
    "rimraf@5.0.10",
    "husky@9.1.4",
    "lint-staged@15.2.8"
)

$devCommand = "npm install --save-dev " + ($devDependencies -join " ")
Invoke-SafeCommand $devCommand "Instalação de dependências de desenvolvimento"

# 7. Configurar otimizações para i5 12ª Gen
Write-Host "🔧 Aplicando otimizações para i5 12ª Gen..." -ForegroundColor Cyan

# Configurar variáveis de ambiente
$env:NODE_OPTIONS = "--max-old-space-size=4096 --max-semi-space-size=512"
$env:UV_THREADPOOL_SIZE = "12"
$env:ELECTRON_DISABLE_SECURITY_WARNINGS = "true"

# Salvar configurações no .env
$envContent = @"
# Otimizações para Intel i5 12ª Gen + 32GB RAM
NODE_OPTIONS=--max-old-space-size=4096 --max-semi-space-size=512
UV_THREADPOOL_SIZE=12
ELECTRON_DISABLE_SECURITY_WARNINGS=true

# .NET Optimizations
DOTNET_GCHeapCount=6
DOTNET_GCConcurrent=1
DOTNET_GCServer=0
DOTNET_EnableDiagnostics=0

# Development
NODE_ENV=development
VITE_PORT=3000
ELECTRON_IS_DEV=true
"@

$envContent | Out-File -FilePath ".env" -Encoding UTF8
Write-Host "✅ Configurações de ambiente criadas" -ForegroundColor Green

# 8. Criar arquivos de configuração TypeScript
Write-Host "📝 Criando configurações TypeScript..." -ForegroundColor Cyan

# tsconfig.json para React
$tsConfigReact = @{
    compilerOptions = @{
        target = "ES2020"
        lib = @("ES2020", "DOM", "DOM.Iterable")
        allowJs = $false
        skipLibCheck = $true
        esModuleInterop = $true
        allowSyntheticDefaultImports = $true
        strict = $true
        forceConsistentCasingInFileNames = $true
        noFallthroughCasesInSwitch = $true
        module = "ESNext"
        moduleResolution = "bundler"
        resolveJsonModule = $true
        isolatedModules = $true
        noEmit = $true
        jsx = "react-jsx"
        baseUrl = "."
        paths = @{
            "@/*" = @("src/frontend/*")
            "@components/*" = @("src/frontend/renderer/components/*")
            "@pages/*" = @("src/frontend/renderer/pages/*")
            "@services/*" = @("src/frontend/renderer/services/*")
            "@hooks/*" = @("src/frontend/renderer/hooks/*")
            "@types/*" = @("src/frontend/renderer/types/*")
            "@utils/*" = @("src/frontend/renderer/utils/*")
            "@shared/*" = @("shared/*")
            "@contracts/*" = @("shared/contracts/*")
            "@schemas/*" = @("shared/schemas/*")
        }
    }
    include = @("src/frontend/renderer/**/*", "src/frontend/renderer/**/*.tsx", "src/frontend/renderer/**/*.ts", "shared/**/*.ts")
    exclude = @("node_modules", "dist", "src/frontend/electron", "src/backend")
}

$tsConfigReact | ConvertTo-Json -Depth 10 | Out-File -FilePath "tsconfig.json" -Encoding UTF8

# tsconfig.electron.json para Electron
$tsConfigElectron = @{
    compilerOptions = @{
        target = "ES2020"
        lib = @("ES2020")
        module = "CommonJS"
        moduleResolution = "node"
        esModuleInterop = $true
        allowSyntheticDefaultImports = $true
        strict = $true
        skipLibCheck = $true
        forceConsistentCasingInFileNames = $true
        outDir = "dist/electron"
        rootDir = "src/frontend/electron"
        declaration = $true
        sourceMap = $false
    }
    include = @("src/frontend/electron/**/*", "shared/**/*.ts")
    exclude = @("node_modules", "dist", "src/frontend/renderer", "src/backend")
}

$tsConfigElectron | ConvertTo-Json -Depth 10 | Out-File -FilePath "tsconfig.electron.json" -Encoding UTF8

Write-Host "✅ Configurações TypeScript criadas" -ForegroundColor Green

# 9. Instalar browsers do Playwright
Write-Host "🎭 Instalando browsers do Playwright..." -ForegroundColor Cyan
Invoke-SafeCommand "npx playwright install" "Instalação de browsers Playwright"

# 10. Configurar Git hooks
if (Test-Path ".git") {
    Write-Host "🔧 Configurando Git hooks..." -ForegroundColor Cyan
    Invoke-SafeCommand "npx husky init" "Configuração Husky"
    
    # Criar pre-commit hook
    $preCommitHook = @"
#!/usr/bin/env sh
. "`$(dirname -- "`$0")/_/husky.sh"

npx lint-staged
"@
    
    $preCommitHook | Out-File -FilePath ".husky/pre-commit" -Encoding UTF8
    Write-Host "✅ Git hooks configurados" -ForegroundColor Green
}

# 11. Verificar instalação
Write-Host "✅ Verificando instalação..." -ForegroundColor Cyan

Write-Host ""
Write-Host "📋 Verificação final:" -ForegroundColor Yellow
Write-Host "  - Node.js: $(node --version)" -ForegroundColor Green
Write-Host "  - npm: $(npm --version)" -ForegroundColor Green
Write-Host "  - .NET: $(dotnet --version)" -ForegroundColor Green

# Verificar se dependências foram instaladas
if (Test-Path "node_modules") {
    $packageCount = (Get-ChildItem "node_modules" -Directory).Count
    Write-Host "  - Packages instalados: $packageCount" -ForegroundColor Green
} else {
    Write-Host "  - ❌ node_modules não encontrado" -ForegroundColor Red
}

# 12. Mostrar próximos passos
Write-Host ""
Write-Host "🎉 Setup concluído com sucesso!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Próximos passos:" -ForegroundColor Yellow
Write-Host "1. Configurar arquivos de código fonte (main.ts, preload.ts, App.tsx)" -ForegroundColor Cyan
Write-Host "2. Configurar Vite (vite.config.ts)" -ForegroundColor Cyan
Write-Host "3. Configurar Electron Builder (electron-builder.json)" -ForegroundColor Cyan
Write-Host "4. Executar: npm run dev" -ForegroundColor Cyan
Write-Host ""
Write-Host "💾 Consumo estimado no seu hardware:" -ForegroundColor Yellow
Write-Host "   - RAM: ~12GB (37.5% dos 32GB disponíveis)" -ForegroundColor Green
Write-Host "   - CPU: ~4-6 cores (33-50% dos 12 cores)" -ForegroundColor Green
Write-Host "   - Storage: ~15GB (3% dos 512GB)" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Hardware otimizado para:" -ForegroundColor Green
Write-Host "   - Intel i5 12ª Gen (6P + 6E cores)" -ForegroundColor Cyan
Write-Host "   - 32GB RAM (configurações otimizadas)" -ForegroundColor Cyan
Write-Host "   - SSD 512GB (cache expandido)" -ForegroundColor Cyan

Write-Host ""
Write-Host "✨ Auto-Instalador V3 Lite Desktop pronto para desenvolvimento!" -ForegroundColor Green
