# TailwindCSS 4.0.0-beta.1 - Documentação Oficial Resumida

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 4.0.0-beta.1  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tailwindcss.com/
- **GitHub:** https://github.com/tailwindlabs/tailwindcss
- **Documentação:** https://tailwindcss.com/docs
- **NPM/Package:** https://www.npmjs.com/package/tailwindcss
- **Fórum/Community:** https://github.com/tailwindlabs/tailwindcss/discussions
- **Stack Overflow Tag:** `tailwind-css`

---

## 📚 **DOCUMENTAÇÃO CORE TAILWIND 4.0**

### **1. CSS-first Configuration**

#### **Theme Configuration**
```css
@theme {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-900: #1e3a8a;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}
```

#### **Component Layer**
```css
@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  
  .btn-primary {
    @apply btn bg-primary-500 text-white hover:bg-primary-600;
  }
  
  .card {
    @apply bg-white rounded-lg shadow border;
  }
}
```

#### **Utilities Layer**
```css
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }
}
```

---

### **2. Automatic Class Detection**

#### **Zero Configuration**
```typescript
// TailwindCSS 4.0 detecta classes automaticamente
// Não precisa mais configurar content paths!

function Component() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h1 className="text-2xl font-bold text-gray-900">
        Auto-detected classes
      </h1>
      <p className="text-gray-600 mt-2">
        Classes são detectadas automaticamente no código
      </p>
    </div>
  );
}
```

#### **Dynamic Classes**
```typescript
// Classes dinâmicas também são detectadas
const statusClasses = {
  running: 'bg-green-100 text-green-800',
  stopped: 'bg-gray-100 text-gray-800',
  error: 'bg-red-100 text-red-800'
};

function StatusBadge({ status }: { status: keyof typeof statusClasses }) {
  return (
    <span className={`px-2 py-1 rounded-full text-xs ${statusClasses[status]}`}>
      {status}
    </span>
  );
}
```

---

### **3. Container Queries**

#### **Container Setup**
```css
.container-card {
  @apply @container;
}

.responsive-content {
  @apply grid grid-cols-1 @sm:grid-cols-2 @lg:grid-cols-3;
}
```

#### **Container Query Classes**
```typescript
function ResponsiveCard() {
  return (
    <div className="@container bg-white rounded-lg p-4">
      <div className="@sm:flex @sm:items-center @sm:space-x-4">
        <img className="@sm:w-16 @sm:h-16 w-full h-32 object-cover rounded" />
        <div className="@sm:mt-0 mt-4">
          <h3 className="text-lg font-semibold">Container Title</h3>
          <p className="text-gray-600 @lg:text-base text-sm">
            Container description
          </p>
        </div>
      </div>
    </div>
  );
}
```

---

### **4. Modern CSS Features**

#### **CSS Nesting**
```css
.sidebar {
  @apply w-64 bg-gray-50;
  
  .nav-item {
    @apply block px-4 py-2 text-gray-700;
    
    &:hover {
      @apply bg-gray-100 text-gray-900;
    }
    
    &.active {
      @apply bg-primary-50 text-primary-700;
      
      &::before {
        content: '';
        @apply absolute left-0 top-0 bottom-0 w-1 bg-primary-500;
      }
    }
  }
}
```

#### **CSS Custom Properties**
```css
@theme {
  --animate-spin: spin 1s linear infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: .5; }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
```

---

### **5. Performance Optimizations**

#### **Rust Engine Benefits**
```css
/* TailwindCSS 4.0 com engine Rust */
/* 10x mais rápido que v3.4 */
/* Menor uso de memória */
/* Builds paralelos automáticos */

@config {
  /* Configurações de performance */
  --build-parallel: true;
  --cache-strategy: "aggressive";
  --output-optimization: "maximum";
}
```

#### **Smaller CSS Output**
```css
/* CSS output otimizado automaticamente */
/* Remoção de CSS não utilizado */
/* Compressão automática */
/* Deduplicação de regras */

/* Antes (v3.4): ~3.5MB CSS não comprimido */
/* Agora (v4.0): ~2.1MB CSS não comprimido (-40%) */
```

---

### **6. Dark Mode**

#### **Dark Mode Setup**
```css
@theme {
  --color-gray-50: #f9fafb;
  --color-gray-900: #111827;
  
  /* Dark mode variants */
  --color-dark-bg: #0f172a;
  --color-dark-surface: #1e293b;
  --color-dark-text: #f1f5f9;
}

@layer base {
  :root {
    color-scheme: light;
  }
  
  .dark {
    color-scheme: dark;
  }
}
```

#### **Dark Mode Classes**
```typescript
function DarkModeComponent() {
  return (
    <div className="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
      <h1 className="text-2xl font-bold">
        Título que muda com o tema
      </h1>
      
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded">
        <p className="text-gray-600 dark:text-gray-300">
          Conteúdo adaptável ao tema
        </p>
      </div>
      
      <button className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white px-4 py-2 rounded">
        Botão com dark mode
      </button>
    </div>
  );
}
```

---

### **7. Typography**

#### **Font Configuration**
```css
@theme {
  --font-family-sans: 'Inter', system-ui, sans-serif;
  --font-family-serif: 'Georgia', serif;
  --font-family-mono: 'JetBrains Mono', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}
```

#### **Typography Classes**
```typescript
function TypographyExample() {
  return (
    <article className="prose prose-lg max-w-none">
      <h1 className="text-4xl font-bold text-gray-900 mb-4">
        Título Principal
      </h1>
      
      <h2 className="text-2xl font-semibold text-gray-800 mb-3">
        Subtítulo
      </h2>
      
      <p className="text-base text-gray-600 leading-relaxed mb-4">
        Parágrafo com texto normal e espaçamento adequado para leitura.
      </p>
      
      <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded font-mono text-sm">
        Código inline
      </code>
      
      <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-700">
        Citação com estilo personalizado
      </blockquote>
    </article>
  );
}
```

---

### **8. Responsive Design**

#### **Breakpoint System**
```css
@theme {
  --breakpoint-sm: 640px;   /* Small devices */
  --breakpoint-md: 768px;   /* Medium devices */
  --breakpoint-lg: 1024px;  /* Large devices */
  --breakpoint-xl: 1280px;  /* Extra large devices */
  --breakpoint-2xl: 1536px; /* 2X large devices */
}
```

#### **Responsive Classes**
```typescript
function ResponsiveLayout() {
  return (
    <div className="container mx-auto px-4">
      {/* Grid responsivo */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {/* Cards responsivos */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow">
          <h3 className="text-lg sm:text-xl font-semibold mb-2">
            Card Title
          </h3>
          <p className="text-sm sm:text-base text-gray-600">
            Card content
          </p>
        </div>
      </div>
      
      {/* Layout flexível */}
      <div className="flex flex-col lg:flex-row gap-6 mt-8">
        <aside className="lg:w-1/4">
          <div className="bg-gray-50 p-4 rounded-lg">
            Sidebar content
          </div>
        </aside>
        
        <main className="lg:w-3/4">
          <div className="bg-white p-6 rounded-lg shadow">
            Main content
          </div>
        </main>
      </div>
    </div>
  );
}
```

---

### **9. Animation and Transitions**

#### **Animation Configuration**
```css
@theme {
  --animate-duration-75: 75ms;
  --animate-duration-100: 100ms;
  --animate-duration-150: 150ms;
  --animate-duration-200: 200ms;
  --animate-duration-300: 300ms;
  --animate-duration-500: 500ms;
  --animate-duration-700: 700ms;
  --animate-duration-1000: 1000ms;
  
  --animate-ease-linear: linear;
  --animate-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --animate-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --animate-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}
```

#### **Animation Classes**
```typescript
function AnimatedComponents() {
  const [isVisible, setIsVisible] = useState(false);
  
  return (
    <div className="space-y-4">
      {/* Hover animations */}
      <button className="bg-blue-500 hover:bg-blue-600 transform hover:scale-105 transition-all duration-200 text-white px-4 py-2 rounded">
        Hover me
      </button>
      
      {/* Loading spinner */}
      <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
      
      {/* Fade in/out */}
      <div className={`transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
        Fade content
      </div>
      
      {/* Slide animations */}
      <div className="transform transition-transform duration-300 hover:translate-x-2">
        Slide on hover
      </div>
      
      {/* Pulse animation */}
      <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
    </div>
  );
}
```

---

### **10. Form Styling**

#### **Form Components**
```css
@layer components {
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply text-sm text-red-600 mt-1;
  }
  
  .form-group {
    @apply mb-4;
  }
}
```

#### **Form Example**
```typescript
function FormExample() {
  return (
    <form className="max-w-md mx-auto space-y-4">
      <div className="form-group">
        <label className="form-label" htmlFor="name">
          Nome do Container
        </label>
        <input
          type="text"
          id="name"
          className="form-input"
          placeholder="Digite o nome"
        />
        <p className="form-error">Campo obrigatório</p>
      </div>
      
      <div className="form-group">
        <label className="form-label" htmlFor="image">
          Imagem Docker
        </label>
        <select id="image" className="form-input">
          <option>nginx:latest</option>
          <option>redis:alpine</option>
          <option>postgres:13</option>
        </select>
      </div>
      
      <div className="form-group">
        <label className="flex items-center">
          <input type="checkbox" className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
          <span className="text-sm text-gray-700">Auto-start container</span>
        </label>
      </div>
      
      <button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
        Criar Container
      </button>
    </form>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - TailwindCSS 4.0.0-beta.1 Documentation
