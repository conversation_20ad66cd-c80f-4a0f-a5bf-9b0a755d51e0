/**
 * Container Controls Component
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para controles de ações de container
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  PlayIcon, 
  StopIcon, 
  ArrowPathIcon,
  PauseIcon,
  TrashIcon,
  CommandLineIcon,
  ExclamationTriangleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { 
  useContainerAction,
  formatContainerStatus,
  getStatusColor 
} from '../../../services/container-service';
import type { 
  Container, 
  ContainerEngine,
  ContainerStatus 
} from '../../../../../shared/types/api.types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContainerControlsProps {
  container: Container;
  onExecCommand?: (container: Container) => void;
  onViewLogs?: (container: Container) => void;
  onViewStats?: (container: Container) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  layout?: 'horizontal' | 'vertical';
}

interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant: 'primary' | 'secondary' | 'danger' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  tooltip?: string;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmLabel: string;
  cancelLabel: string;
  onConfirm: () => void;
  onCancel: () => void;
  variant: 'danger' | 'warning';
}

// ============================================================================
// ACTION BUTTON COMPONENT
// ============================================================================

const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  label,
  onClick,
  disabled = false,
  loading = false,
  variant,
  size = 'md',
  tooltip
}) => {
  const sizeClasses = {
    sm: 'p-1.5 text-xs',
    md: 'p-2 text-sm',
    lg: 'p-3 text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  const variantClasses = {
    primary: 'text-blue-600 hover:bg-blue-50 border-blue-200',
    secondary: 'text-gray-600 hover:bg-gray-50 border-gray-200',
    danger: 'text-red-600 hover:bg-red-50 border-red-200',
    warning: 'text-yellow-600 hover:bg-yellow-50 border-yellow-200'
  };

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      disabled={disabled || loading}
      title={tooltip || label}
      className={`
        relative border rounded-lg transition-all duration-200
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
      `}
    >
      {loading ? (
        <div className={`animate-spin rounded-full border-b-2 border-current ${iconSizes[size]}`} />
      ) : (
        <Icon className={iconSizes[size]} />
      )}
      <span className="sr-only">{label}</span>
    </motion.button>
  );
};

// ============================================================================
// CONFIRM DIALOG COMPONENT
// ============================================================================

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  confirmLabel,
  cancelLabel,
  onConfirm,
  onCancel,
  variant
}) => {
  if (!isOpen) return null;

  const variantClasses = {
    danger: 'text-red-600 bg-red-50 border-red-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200'
  };

  const buttonClasses = {
    danger: 'bg-red-600 hover:bg-red-700 text-white',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
      >
        <div className="p-6">
          <div className="flex items-center">
            <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${variantClasses[variant]}`}>
              <ExclamationTriangleIcon className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <p className="mt-1 text-sm text-gray-600">{message}</p>
            </div>
          </div>
          
          <div className="mt-6 flex space-x-3 justify-end">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              {cancelLabel}
            </button>
            <button
              onClick={onConfirm}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${buttonClasses[variant]}`}
            >
              {confirmLabel}
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export const ContainerControls: React.FC<ContainerControlsProps> = ({
  container,
  onExecCommand,
  onViewLogs,
  onViewStats,
  className = '',
  size = 'md',
  layout = 'horizontal'
}) => {
  // State
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    action: string;
    title: string;
    message: string;
    variant: 'danger' | 'warning';
  }>({
    isOpen: false,
    action: '',
    title: '',
    message: '',
    variant: 'danger'
  });

  // Mutations
  const containerAction = useContainerAction();

  // Handlers
  const handleAction = async (action: 'start' | 'stop' | 'restart' | 'pause' | 'unpause' | 'remove') => {
    // Actions that require confirmation
    const confirmActions = ['stop', 'restart', 'remove'];
    
    if (confirmActions.includes(action)) {
      const confirmMessages = {
        stop: {
          title: 'Parar Container',
          message: `Tem certeza que deseja parar o container "${container.name}"?`,
          variant: 'warning' as const
        },
        restart: {
          title: 'Reiniciar Container',
          message: `Tem certeza que deseja reiniciar o container "${container.name}"?`,
          variant: 'warning' as const
        },
        remove: {
          title: 'Remover Container',
          message: `Tem certeza que deseja remover o container "${container.name}"? Esta ação não pode ser desfeita.`,
          variant: 'danger' as const
        }
      };

      setConfirmDialog({
        isOpen: true,
        action,
        ...confirmMessages[action as keyof typeof confirmMessages]
      });
      return;
    }

    // Execute action directly
    await containerAction.mutateAsync({
      id: container.id,
      action,
      engine: container.engine
    });
  };

  const handleConfirmAction = async () => {
    await containerAction.mutateAsync({
      id: container.id,
      action: confirmDialog.action as any,
      engine: container.engine,
      force: confirmDialog.action === 'remove'
    });
    
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  };

  const handleCancelAction = () => {
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  };

  // Get available actions based on container status
  const getAvailableActions = (status: ContainerStatus) => {
    const actions = [];

    switch (status) {
      case 'running':
        actions.push(
          { key: 'pause', icon: PauseIcon, label: 'Pausar', variant: 'warning' as const },
          { key: 'restart', icon: ArrowPathIcon, label: 'Reiniciar', variant: 'secondary' as const },
          { key: 'stop', icon: StopIcon, label: 'Parar', variant: 'danger' as const }
        );
        break;
      
      case 'paused':
        actions.push(
          { key: 'unpause', icon: PlayIcon, label: 'Despausar', variant: 'primary' as const }
        );
        break;
      
      case 'exited':
      case 'created':
        actions.push(
          { key: 'start', icon: PlayIcon, label: 'Iniciar', variant: 'primary' as const },
          { key: 'remove', icon: TrashIcon, label: 'Remover', variant: 'danger' as const }
        );
        break;
      
      default:
        break;
    }

    return actions;
  };

  const availableActions = getAvailableActions(container.status);
  const isLoading = containerAction.isPending;

  const containerClasses = layout === 'horizontal' 
    ? 'flex items-center space-x-2' 
    : 'flex flex-col space-y-2';

  return (
    <>
      <div className={`${containerClasses} ${className}`}>
        {/* Status Indicator */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            container.status === 'running' ? 'bg-green-400' :
            container.status === 'paused' ? 'bg-yellow-400' :
            container.status === 'exited' ? 'bg-gray-400' :
            'bg-red-400'
          }`} />
          <span className={`text-sm font-medium ${getStatusColor(container.status)}`}>
            {formatContainerStatus(container.status)}
          </span>
        </div>

        {/* Action Buttons */}
        <div className={layout === 'horizontal' ? 'flex space-x-1' : 'flex flex-col space-y-1'}>
          {availableActions.map(action => (
            <ActionButton
              key={action.key}
              icon={action.icon}
              label={action.label}
              onClick={() => handleAction(action.key as any)}
              disabled={isLoading}
              loading={isLoading}
              variant={action.variant}
              size={size}
            />
          ))}
        </div>

        {/* Additional Actions */}
        {container.status === 'running' && (
          <div className={layout === 'horizontal' ? 'flex space-x-1 border-l pl-2' : 'flex flex-col space-y-1 border-t pt-2'}>
            {onExecCommand && (
              <ActionButton
                icon={CommandLineIcon}
                label="Terminal"
                onClick={() => onExecCommand(container)}
                variant="secondary"
                size={size}
                tooltip="Abrir terminal"
              />
            )}
            
            {onViewLogs && (
              <ActionButton
                icon={ClockIcon}
                label="Logs"
                onClick={() => onViewLogs(container)}
                variant="secondary"
                size={size}
                tooltip="Ver logs"
              />
            )}
            
            {onViewStats && (
              <ActionButton
                icon={ClockIcon}
                label="Stats"
                onClick={() => onViewStats(container)}
                variant="secondary"
                size={size}
                tooltip="Ver estatísticas"
              />
            )}
          </div>
        )}
      </div>

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmLabel="Confirmar"
        cancelLabel="Cancelar"
        onConfirm={handleConfirmAction}
        onCancel={handleCancelAction}
        variant={confirmDialog.variant}
      />
    </>
  );
};
