/**
 * Container Dashboard Component
 * Auto-Instalador V3 Lite
 * 
 * @description Dashboard principal para gerenciamento de containers
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Squares2X2Icon,
  CubeIcon,
  CogIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CommandLineIcon,
  PlusIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { ContainerList } from './ContainerList';
import { ContainerStats } from './ContainerStats';
import { ContainerLogs } from './ContainerLogs';
import { ContainerControls } from './ContainerControls';
import { 
  useContainerEngines, 
  useDetectEngines,
  useContainerEvents 
} from '../../../services/container-service';
import type { 
  Container, 
  ContainerEngine 
} from '../../../../../shared/types/api.types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContainerDashboardProps {
  className?: string;
}

type DashboardView = 'overview' | 'containers' | 'images' | 'engines' | 'stats' | 'logs';

interface ViewConfig {
  id: DashboardView;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

const DASHBOARD_VIEWS: ViewConfig[] = [
  {
    id: 'overview',
    label: 'Visão Geral',
    icon: Squares2X2Icon,
    description: 'Status geral dos containers e engines'
  },
  {
    id: 'containers',
    label: 'Containers',
    icon: CubeIcon,
    description: 'Gerenciar containers existentes'
  },
  {
    id: 'engines',
    label: 'Engines',
    icon: CogIcon,
    description: 'Configurar Docker e Podman'
  },
  {
    id: 'stats',
    label: 'Estatísticas',
    icon: ChartBarIcon,
    description: 'Monitoramento em tempo real'
  },
  {
    id: 'logs',
    label: 'Logs',
    icon: DocumentTextIcon,
    description: 'Visualizar logs dos containers'
  }
];

// ============================================================================
// COMPONENT
// ============================================================================

export const ContainerDashboard: React.FC<ContainerDashboardProps> = ({
  className = ''
}) => {
  // State
  const [currentView, setCurrentView] = useState<DashboardView>('overview');
  const [selectedContainer, setSelectedContainer] = useState<Container | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Setup real-time events
  useContainerEvents();

  // Queries
  const { data: enginesResponse, isLoading: enginesLoading } = useContainerEngines();
  const { data: detectionResponse, isLoading: detectionLoading } = useDetectEngines();

  // Handlers
  const handleViewChange = (view: DashboardView) => {
    setCurrentView(view);
    if (view !== 'stats' && view !== 'logs') {
      setSelectedContainer(null);
    }
  };

  const handleContainerSelect = (container: Container) => {
    setSelectedContainer(container);
    setCurrentView('stats');
  };

  const handleViewLogs = (container: Container) => {
    setSelectedContainer(container);
    setCurrentView('logs');
  };

  const handleCreateContainer = () => {
    setShowCreateModal(true);
  };

  // Check if any engine is available
  const hasAvailableEngine = detectionResponse?.detectedEngines?.some(e => e.isDetected) ?? false;

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Containers</h1>
            <p className="text-sm text-gray-600">
              Gerencie containers Docker e Podman
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {hasAvailableEngine && (
              <button
                onClick={handleCreateContainer}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Novo Container
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Engine Status Warning */}
      {!detectionLoading && !hasAvailableEngine && (
        <div className="flex-shrink-0 bg-yellow-50 border-b border-yellow-200 px-6 py-3">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-yellow-800">
                Nenhum engine de container detectado
              </p>
              <p className="text-xs text-yellow-700">
                Instale Docker ou Podman para começar a usar containers
              </p>
            </div>
            <button
              onClick={() => setCurrentView('engines')}
              className="ml-auto px-3 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors text-sm"
            >
              Configurar Engines
            </button>
          </div>
        </div>
      )}

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="flex-shrink-0 w-64 bg-gray-50 border-r border-gray-200">
          <nav className="p-4 space-y-2">
            {DASHBOARD_VIEWS.map((view) => {
              const Icon = view.icon;
              const isActive = currentView === view.id;
              
              return (
                <button
                  key={view.id}
                  onClick={() => handleViewChange(view.id)}
                  className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors ${
                    isActive
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-3 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                  <div>
                    <div className="font-medium">{view.label}</div>
                    <div className="text-xs text-gray-500">{view.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>

          {/* Selected Container Info */}
          {selectedContainer && (currentView === 'stats' || currentView === 'logs') && (
            <div className="border-t border-gray-200 p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Container Selecionado</h3>
              <div className="bg-white rounded-lg border border-gray-200 p-3">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    selectedContainer.status === 'running' ? 'bg-green-400' :
                    selectedContainer.status === 'paused' ? 'bg-yellow-400' :
                    'bg-gray-400'
                  }`} />
                  <span className="text-sm font-medium truncate">
                    {selectedContainer.name || selectedContainer.id.substring(0, 12)}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1 truncate">
                  {selectedContainer.image}
                </p>
                <div className="mt-2">
                  <ContainerControls
                    container={selectedContainer}
                    size="sm"
                    layout="vertical"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentView}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="h-full overflow-auto p-6"
            >
              {currentView === 'overview' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">Visão Geral</h2>
                  {/* Overview content will be implemented */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <p className="text-gray-600">Dashboard de visão geral em desenvolvimento...</p>
                  </div>
                </div>
              )}

              {currentView === 'containers' && (
                <ContainerList
                  onCreateContainer={handleCreateContainer}
                  onViewContainer={handleContainerSelect}
                />
              )}

              {currentView === 'engines' && (
                <div className="space-y-6">
                  <h2 className="text-xl font-semibold text-gray-900">Engines de Container</h2>
                  {/* Engine management content will be implemented */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <p className="text-gray-600">Gerenciamento de engines em desenvolvimento...</p>
                  </div>
                </div>
              )}

              {currentView === 'stats' && selectedContainer && (
                <ContainerStats
                  containerId={selectedContainer.id}
                  containerName={selectedContainer.name}
                  engine={selectedContainer.engine}
                />
              )}

              {currentView === 'logs' && selectedContainer && (
                <ContainerLogs
                  containerId={selectedContainer.id}
                  containerName={selectedContainer.name}
                  engine={selectedContainer.engine}
                />
              )}

              {(currentView === 'stats' || currentView === 'logs') && !selectedContainer && (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      Nenhum container selecionado
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Selecione um container para ver {currentView === 'stats' ? 'estatísticas' : 'logs'}
                    </p>
                    <button
                      onClick={() => setCurrentView('containers')}
                      className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Ver Containers
                    </button>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};
