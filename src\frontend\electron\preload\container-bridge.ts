/**
 * Container Bridge - Electron Preload
 * Auto-Instalador V3 Lite
 * 
 * @description Bridge seguro para comunicação entre renderer e main process para containers
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import { contextBridge, ipcRenderer } from 'electron';
import type {
  Container,
  ContainerEngine,
  ContainerStatus,
  ContainerStats,
  ContainerLog,
  ContainerImage,
  ContainerEngineInfo,
  ContainerRunRequest,
  ContainerListRequest,
  ContainerLogsRequest,
  ContainerActionRequest,
  ContainerEngineInstallRequest,
  BaseResponse,
  ContainerListResponse,
  ContainerResponse,
  ContainerRunResponse,
  ContainerActionResponse,
  ContainerLogsResponse,
  ContainerStatsResponse,
  ContainerImageListResponse,
  ContainerEngineListResponse,
  ContainerEngineStatusResponse,
  ContainerEngineInstallResponse,
  ContainerEngineDetectionResponse
} from '../../../shared/types/api.types';

// ============================================================================
// CONTAINER API BRIDGE
// ============================================================================

export interface ContainerAPI {
  // Container Management
  listContainers(request: ContainerListRequest): Promise<ContainerListResponse>;
  getContainer(id: string, engine?: ContainerEngine): Promise<ContainerResponse>;
  runContainer(request: ContainerRunRequest): Promise<ContainerRunResponse>;
  startContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  stopContainer(id: string, timeout?: number, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  restartContainer(id: string, timeout?: number, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  pauseContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  unpauseContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  removeContainer(id: string, force?: boolean, engine?: ContainerEngine): Promise<ContainerActionResponse>;
  
  // Container Logs & Stats
  getContainerLogs(request: ContainerLogsRequest): Promise<ContainerLogsResponse>;
  getContainerStats(id: string, engine?: ContainerEngine): Promise<ContainerStatsResponse>;
  execContainer(id: string, command: string, args?: string[], interactive?: boolean, tty?: boolean, engine?: ContainerEngine): Promise<BaseResponse<any>>;
  
  // Container Images
  listImages(engine?: ContainerEngine): Promise<ContainerImageListResponse>;
  pullImage(imageName: string, tag?: string, engine?: ContainerEngine): Promise<BaseResponse<any>>;
  removeImage(imageId: string, force?: boolean, engine?: ContainerEngine): Promise<BaseResponse<any>>;
  searchImages(searchTerm: string, limit?: number, engine?: ContainerEngine): Promise<BaseResponse<any>>;
  
  // Container Engines
  listEngines(): Promise<ContainerEngineListResponse>;
  getEngineStatus(engine: ContainerEngine): Promise<ContainerEngineStatusResponse>;
  getEngineInfo(engine: ContainerEngine): Promise<BaseResponse<ContainerEngineInfo>>;
  installEngine(request: ContainerEngineInstallRequest): Promise<ContainerEngineInstallResponse>;
  detectEngines(): Promise<ContainerEngineDetectionResponse>;
  
  // Real-time Events
  onContainerStatusChanged(callback: (data: { containerId: string; status: ContainerStatus; engine: ContainerEngine }) => void): () => void;
  onContainerStatsUpdate(callback: (stats: ContainerStats) => void): () => void;
  onEngineStatusChanged(callback: (data: { engine: ContainerEngine; isAvailable: boolean; isRunning: boolean }) => void): () => void;
  
  // Utility
  isEngineAvailable(engine: ContainerEngine): Promise<boolean>;
  getRecommendedEngine(): Promise<ContainerEngine | null>;
}

// ============================================================================
// IPC CHANNEL CONSTANTS
// ============================================================================

const CONTAINER_CHANNELS = {
  // Container Management
  LIST_CONTAINERS: 'container:list',
  GET_CONTAINER: 'container:get',
  RUN_CONTAINER: 'container:run',
  START_CONTAINER: 'container:start',
  STOP_CONTAINER: 'container:stop',
  RESTART_CONTAINER: 'container:restart',
  PAUSE_CONTAINER: 'container:pause',
  UNPAUSE_CONTAINER: 'container:unpause',
  REMOVE_CONTAINER: 'container:remove',
  
  // Container Logs & Stats
  GET_CONTAINER_LOGS: 'container:logs',
  GET_CONTAINER_STATS: 'container:stats',
  EXEC_CONTAINER: 'container:exec',
  
  // Container Images
  LIST_IMAGES: 'container:images:list',
  PULL_IMAGE: 'container:images:pull',
  REMOVE_IMAGE: 'container:images:remove',
  SEARCH_IMAGES: 'container:images:search',
  
  // Container Engines
  LIST_ENGINES: 'container:engines:list',
  GET_ENGINE_STATUS: 'container:engines:status',
  GET_ENGINE_INFO: 'container:engines:info',
  INSTALL_ENGINE: 'container:engines:install',
  DETECT_ENGINES: 'container:engines:detect',
  
  // Real-time Events
  CONTAINER_STATUS_CHANGED: 'container:status-changed',
  CONTAINER_STATS_UPDATE: 'container:stats-update',
  ENGINE_STATUS_CHANGED: 'container:engine-status-changed',
  
  // Utility
  IS_ENGINE_AVAILABLE: 'container:engine:available',
  GET_RECOMMENDED_ENGINE: 'container:engine:recommended'
} as const;

// ============================================================================
// CONTAINER API IMPLEMENTATION
// ============================================================================

const containerAPI: ContainerAPI = {
  // Container Management
  async listContainers(request: ContainerListRequest): Promise<ContainerListResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.LIST_CONTAINERS, request);
  },

  async getContainer(id: string, engine?: ContainerEngine): Promise<ContainerResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_CONTAINER, { id, engine });
  },

  async runContainer(request: ContainerRunRequest): Promise<ContainerRunResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.RUN_CONTAINER, request);
  },

  async startContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.START_CONTAINER, { id, engine });
  },

  async stopContainer(id: string, timeout?: number, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.STOP_CONTAINER, { id, timeout, engine });
  },

  async restartContainer(id: string, timeout?: number, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.RESTART_CONTAINER, { id, timeout, engine });
  },

  async pauseContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.PAUSE_CONTAINER, { id, engine });
  },

  async unpauseContainer(id: string, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.UNPAUSE_CONTAINER, { id, engine });
  },

  async removeContainer(id: string, force?: boolean, engine?: ContainerEngine): Promise<ContainerActionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.REMOVE_CONTAINER, { id, force, engine });
  },

  // Container Logs & Stats
  async getContainerLogs(request: ContainerLogsRequest): Promise<ContainerLogsResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_CONTAINER_LOGS, request);
  },

  async getContainerStats(id: string, engine?: ContainerEngine): Promise<ContainerStatsResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_CONTAINER_STATS, { id, engine });
  },

  async execContainer(id: string, command: string, args?: string[], interactive?: boolean, tty?: boolean, engine?: ContainerEngine): Promise<BaseResponse<any>> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.EXEC_CONTAINER, {
      id,
      command,
      args,
      interactive,
      tty,
      engine
    });
  },

  // Container Images
  async listImages(engine?: ContainerEngine): Promise<ContainerImageListResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.LIST_IMAGES, { engine });
  },

  async pullImage(imageName: string, tag?: string, engine?: ContainerEngine): Promise<BaseResponse<any>> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.PULL_IMAGE, { imageName, tag, engine });
  },

  async removeImage(imageId: string, force?: boolean, engine?: ContainerEngine): Promise<BaseResponse<any>> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.REMOVE_IMAGE, { imageId, force, engine });
  },

  async searchImages(searchTerm: string, limit?: number, engine?: ContainerEngine): Promise<BaseResponse<any>> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.SEARCH_IMAGES, { searchTerm, limit, engine });
  },

  // Container Engines
  async listEngines(): Promise<ContainerEngineListResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.LIST_ENGINES);
  },

  async getEngineStatus(engine: ContainerEngine): Promise<ContainerEngineStatusResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_ENGINE_STATUS, { engine });
  },

  async getEngineInfo(engine: ContainerEngine): Promise<BaseResponse<ContainerEngineInfo>> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_ENGINE_INFO, { engine });
  },

  async installEngine(request: ContainerEngineInstallRequest): Promise<ContainerEngineInstallResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.INSTALL_ENGINE, request);
  },

  async detectEngines(): Promise<ContainerEngineDetectionResponse> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.DETECT_ENGINES);
  },

  // Real-time Events
  onContainerStatusChanged(callback: (data: { containerId: string; status: ContainerStatus; engine: ContainerEngine }) => void): () => void {
    const listener = (_event: any, data: any) => callback(data);
    ipcRenderer.on(CONTAINER_CHANNELS.CONTAINER_STATUS_CHANGED, listener);
    
    return () => {
      ipcRenderer.removeListener(CONTAINER_CHANNELS.CONTAINER_STATUS_CHANGED, listener);
    };
  },

  onContainerStatsUpdate(callback: (stats: ContainerStats) => void): () => void {
    const listener = (_event: any, stats: ContainerStats) => callback(stats);
    ipcRenderer.on(CONTAINER_CHANNELS.CONTAINER_STATS_UPDATE, listener);
    
    return () => {
      ipcRenderer.removeListener(CONTAINER_CHANNELS.CONTAINER_STATS_UPDATE, listener);
    };
  },

  onEngineStatusChanged(callback: (data: { engine: ContainerEngine; isAvailable: boolean; isRunning: boolean }) => void): () => void {
    const listener = (_event: any, data: any) => callback(data);
    ipcRenderer.on(CONTAINER_CHANNELS.ENGINE_STATUS_CHANGED, listener);
    
    return () => {
      ipcRenderer.removeListener(CONTAINER_CHANNELS.ENGINE_STATUS_CHANGED, listener);
    };
  },

  // Utility
  async isEngineAvailable(engine: ContainerEngine): Promise<boolean> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.IS_ENGINE_AVAILABLE, { engine });
  },

  async getRecommendedEngine(): Promise<ContainerEngine | null> {
    return await ipcRenderer.invoke(CONTAINER_CHANNELS.GET_RECOMMENDED_ENGINE);
  }
};

// ============================================================================
// CONTEXT BRIDGE EXPOSURE
// ============================================================================

// Expor API de containers para o renderer process
contextBridge.exposeInMainWorld('containerAPI', containerAPI);

// Expor constantes úteis
contextBridge.exposeInMainWorld('containerConstants', {
  engines: {
    DOCKER: 'docker' as ContainerEngine,
    PODMAN: 'podman' as ContainerEngine
  },
  status: {
    CREATED: 'created' as ContainerStatus,
    RUNNING: 'running' as ContainerStatus,
    PAUSED: 'paused' as ContainerStatus,
    RESTARTING: 'restarting' as ContainerStatus,
    REMOVING: 'removing' as ContainerStatus,
    DEAD: 'dead' as ContainerStatus,
    EXITED: 'exited' as ContainerStatus
  }
});

// ============================================================================
// TYPE DECLARATIONS FOR RENDERER
// ============================================================================

declare global {
  interface Window {
    containerAPI: ContainerAPI;
    containerConstants: {
      engines: {
        DOCKER: ContainerEngine;
        PODMAN: ContainerEngine;
      };
      status: {
        CREATED: ContainerStatus;
        RUNNING: ContainerStatus;
        PAUSED: ContainerStatus;
        RESTARTING: ContainerStatus;
        REMOVING: ContainerStatus;
        DEAD: ContainerStatus;
        EXITED: ContainerStatus;
      };
    };
  }
}

export { CONTAINER_CHANNELS };
