using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Interfaces.Services;

/// <summary>
/// Interface base para engines de container (<PERSON><PERSON>, <PERSON>dman)
/// </summary>
public interface IContainerEngine
{
    /// <summary>
    /// Nome do engine (docker, podman)
    /// </summary>
    string EngineName { get; }

    /// <summary>
    /// Tipo do engine
    /// </summary>
    ContainerEngine EngineType { get; }

    /// <summary>
    /// Verifica se o engine está disponível no sistema
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se disponível</returns>
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se o engine está em execução
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se em execução</returns>
    Task<bool> IsRunningAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações do engine
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do engine</returns>
    Task<ContainerEngineInfo> GetEngineInfoAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista containers
    /// </summary>
    /// <param name="all">Incluir containers parados</param>
    /// <param name="filters">Filtros de busca</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    Task<IEnumerable<Container>> ListContainersAsync(bool all = false, Dictionary<string, string>? filters = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações de um container específico
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do container</returns>
    Task<Container?> GetContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa um novo container
    /// </summary>
    /// <param name="options">Opções de execução</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>ID do container criado</returns>
    Task<string> RunContainerAsync(ContainerRunOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Inicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se iniciado com sucesso</returns>
    Task<bool> StartContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Para um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se parado com sucesso</returns>
    Task<bool> StopContainerAsync(string containerId, int? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reinicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se reiniciado com sucesso</returns>
    Task<bool> RestartContainerAsync(string containerId, int? timeout = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se pausado com sucesso</returns>
    Task<bool> PauseContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Despausa um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se despausado com sucesso</returns>
    Task<bool> UnpauseContainerAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removido com sucesso</returns>
    Task<bool> RemoveContainerAsync(string containerId, bool force = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém logs de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="tail">Número de linhas do final</param>
    /// <param name="follow">Seguir logs em tempo real</param>
    /// <param name="timestamps">Incluir timestamps</param>
    /// <param name="since">Data/hora de início</param>
    /// <param name="until">Data/hora de fim</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Logs do container</returns>
    Task<IEnumerable<ContainerLog>> GetContainerLogsAsync(string containerId, int? tail = null, bool follow = false, bool timestamps = true, DateTime? since = null, DateTime? until = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do container</returns>
    Task<ContainerStats?> GetContainerStatsAsync(string containerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa um comando em um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="command">Comando a executar</param>
    /// <param name="args">Argumentos do comando</param>
    /// <param name="interactive">Modo interativo</param>
    /// <param name="tty">Alocar TTY</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da execução</returns>
    Task<ContainerExecResult> ExecContainerAsync(string containerId, string command, string[]? args = null, bool interactive = false, bool tty = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista imagens
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de imagens</returns>
    Task<IEnumerable<ContainerImage>> ListImagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Faz download de uma imagem
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="tag">Tag da imagem</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se baixada com sucesso</returns>
    Task<bool> PullImageAsync(string imageName, string? tag = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove uma imagem
    /// </summary>
    /// <param name="imageId">ID da imagem</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removida com sucesso</returns>
    Task<bool> RemoveImageAsync(string imageId, bool force = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Busca imagens em registries
    /// </summary>
    /// <param name="searchTerm">Termo de busca</param>
    /// <param name="limit">Limite de resultados</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultados da busca</returns>
    Task<IEnumerable<ContainerImageSearchResult>> SearchImagesAsync(string searchTerm, int limit = 25, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa um comando CLI do engine
    /// </summary>
    /// <param name="command">Comando a executar</param>
    /// <param name="args">Argumentos do comando</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da execução</returns>
    Task<CommandResult> ExecuteCommandAsync(string command, string[]? args = null, int timeout = 30, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface para detecção e instalação de engines
/// </summary>
public interface IContainerEngineDetector
{
    /// <summary>
    /// Detecta engines instalados no sistema
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de engines detectados</returns>
    Task<IEnumerable<ContainerEngineInfo>> DetectEnginesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se um engine específico está instalado
    /// </summary>
    /// <param name="engine">Engine a verificar</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se instalado</returns>
    Task<bool> IsEngineInstalledAsync(ContainerEngine engine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Instala um engine de container
    /// </summary>
    /// <param name="engine">Engine a instalar</param>
    /// <param name="platform">Plataforma do sistema</param>
    /// <param name="packageManager">Gerenciador de pacotes</param>
    /// <param name="force">Forçar instalação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se instalado com sucesso</returns>
    Task<bool> InstallEngineAsync(ContainerEngine engine, string platform, string? packageManager = null, bool force = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém comandos de instalação para uma plataforma
    /// </summary>
    /// <param name="engine">Engine a instalar</param>
    /// <param name="platform">Plataforma do sistema</param>
    /// <param name="packageManager">Gerenciador de pacotes</param>
    /// <returns>Comandos de instalação</returns>
    string[] GetInstallCommands(ContainerEngine engine, string platform, string? packageManager = null);
}
