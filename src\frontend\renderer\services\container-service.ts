/**
 * Container Service - React Frontend
 * Auto-Instalador V3 Lite
 * 
 * @description Serviço para gerenciamento de containers com React Query
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import { useMutation, useQuery, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import type {
  Container,
  ContainerEngine,
  ContainerStatus,
  ContainerStats,
  ContainerLog,
  ContainerImage,
  ContainerEngineInfo,
  ContainerRunRequest,
  ContainerListRequest,
  ContainerLogsRequest,
  ContainerActionRequest,
  ContainerEngineInstallRequest,
  BaseResponse,
  ContainerListResponse,
  ContainerResponse,
  ContainerRunResponse,
  ContainerActionResponse,
  ContainerLogsResponse,
  ContainerStatsResponse,
  ContainerImageListResponse,
  ContainerEngineListResponse,
  ContainerEngineStatusResponse,
  ContainerEngineInstallResponse,
  ContainerEngineDetectionResponse
} from '../../../shared/types/api.types';

// ============================================================================
// QUERY KEYS
// ============================================================================

export const containerQueryKeys = {
  all: ['containers'] as const,
  lists: () => [...containerQueryKeys.all, 'list'] as const,
  list: (filters: ContainerListRequest) => [...containerQueryKeys.lists(), filters] as const,
  details: () => [...containerQueryKeys.all, 'detail'] as const,
  detail: (id: string, engine?: ContainerEngine) => [...containerQueryKeys.details(), id, engine] as const,
  stats: (id: string, engine?: ContainerEngine) => [...containerQueryKeys.all, 'stats', id, engine] as const,
  logs: (request: ContainerLogsRequest) => [...containerQueryKeys.all, 'logs', request] as const,
  
  images: {
    all: ['container-images'] as const,
    lists: () => [...containerQueryKeys.images.all, 'list'] as const,
    list: (engine?: ContainerEngine) => [...containerQueryKeys.images.lists(), engine] as const,
  },
  
  engines: {
    all: ['container-engines'] as const,
    lists: () => [...containerQueryKeys.engines.all, 'list'] as const,
    status: (engine: ContainerEngine) => [...containerQueryKeys.engines.all, 'status', engine] as const,
    info: (engine: ContainerEngine) => [...containerQueryKeys.engines.all, 'info', engine] as const,
    detection: () => [...containerQueryKeys.engines.all, 'detection'] as const,
  }
} as const;

// ============================================================================
// CONTAINER HOOKS
// ============================================================================

/**
 * Hook para listar containers
 */
export function useContainers(
  request: ContainerListRequest = { all: false },
  options?: Omit<UseQueryOptions<ContainerListResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.list(request),
    queryFn: () => window.containerAPI.listContainers(request),
    staleTime: 30000, // 30 segundos
    refetchInterval: 60000, // 1 minuto
    ...options
  });
}

/**
 * Hook para obter container específico
 */
export function useContainer(
  id: string,
  engine?: ContainerEngine,
  options?: Omit<UseQueryOptions<ContainerResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.detail(id, engine),
    queryFn: () => window.containerAPI.getContainer(id, engine),
    enabled: !!id,
    staleTime: 30000,
    ...options
  });
}

/**
 * Hook para estatísticas de container
 */
export function useContainerStats(
  id: string,
  engine?: ContainerEngine,
  options?: Omit<UseQueryOptions<ContainerStatsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.stats(id, engine),
    queryFn: () => window.containerAPI.getContainerStats(id, engine),
    enabled: !!id,
    refetchInterval: 5000, // 5 segundos para stats em tempo real
    ...options
  });
}

/**
 * Hook para logs de container
 */
export function useContainerLogs(
  request: ContainerLogsRequest,
  options?: Omit<UseQueryOptions<ContainerLogsResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.logs(request),
    queryFn: () => window.containerAPI.getContainerLogs(request),
    enabled: !!request.containerId,
    staleTime: 10000, // 10 segundos
    ...options
  });
}

// ============================================================================
// CONTAINER MUTATIONS
// ============================================================================

/**
 * Hook para executar container
 */
export function useRunContainer(
  options?: UseMutationOptions<ContainerRunResponse, Error, ContainerRunRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: ContainerRunRequest) => window.containerAPI.runContainer(request),
    onSuccess: (data) => {
      // Invalidar lista de containers
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.lists() });
      
      toast.success(`Container ${data.containerName || data.containerId} criado com sucesso!`);
    },
    onError: (error) => {
      toast.error(`Erro ao criar container: ${error.message}`);
    },
    ...options
  });
}

/**
 * Hook para ações de container (start, stop, restart, etc.)
 */
export function useContainerAction(
  options?: UseMutationOptions<ContainerActionResponse, Error, {
    id: string;
    action: 'start' | 'stop' | 'restart' | 'pause' | 'unpause' | 'remove';
    timeout?: number;
    force?: boolean;
    engine?: ContainerEngine;
  }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, action, timeout, force, engine }) => {
      switch (action) {
        case 'start':
          return window.containerAPI.startContainer(id, engine);
        case 'stop':
          return window.containerAPI.stopContainer(id, timeout, engine);
        case 'restart':
          return window.containerAPI.restartContainer(id, timeout, engine);
        case 'pause':
          return window.containerAPI.pauseContainer(id, engine);
        case 'unpause':
          return window.containerAPI.unpauseContainer(id, engine);
        case 'remove':
          return window.containerAPI.removeContainer(id, force, engine);
        default:
          throw new Error(`Ação não suportada: ${action}`);
      }
    },
    onSuccess: (data, variables) => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.detail(variables.id, variables.engine) });
      
      const actionNames = {
        start: 'iniciado',
        stop: 'parado',
        restart: 'reiniciado',
        pause: 'pausado',
        unpause: 'despausado',
        remove: 'removido'
      };
      
      toast.success(`Container ${actionNames[variables.action]} com sucesso!`);
    },
    onError: (error, variables) => {
      toast.error(`Erro ao ${variables.action} container: ${error.message}`);
    },
    ...options
  });
}

// ============================================================================
// IMAGE HOOKS
// ============================================================================

/**
 * Hook para listar imagens
 */
export function useContainerImages(
  engine?: ContainerEngine,
  options?: Omit<UseQueryOptions<ContainerImageListResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.images.list(engine),
    queryFn: () => window.containerAPI.listImages(engine),
    staleTime: 60000, // 1 minuto
    ...options
  });
}

/**
 * Hook para baixar imagem
 */
export function usePullImage(
  options?: UseMutationOptions<BaseResponse<any>, Error, {
    imageName: string;
    tag?: string;
    engine?: ContainerEngine;
  }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ imageName, tag, engine }) => window.containerAPI.pullImage(imageName, tag, engine),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.images.lists() });
      toast.success(`Imagem ${variables.imageName}${variables.tag ? `:${variables.tag}` : ''} baixada com sucesso!`);
    },
    onError: (error) => {
      toast.error(`Erro ao baixar imagem: ${error.message}`);
    },
    ...options
  });
}

/**
 * Hook para remover imagem
 */
export function useRemoveImage(
  options?: UseMutationOptions<BaseResponse<any>, Error, {
    imageId: string;
    force?: boolean;
    engine?: ContainerEngine;
  }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ imageId, force, engine }) => window.containerAPI.removeImage(imageId, force, engine),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.images.lists() });
      toast.success('Imagem removida com sucesso!');
    },
    onError: (error) => {
      toast.error(`Erro ao remover imagem: ${error.message}`);
    },
    ...options
  });
}

// ============================================================================
// ENGINE HOOKS
// ============================================================================

/**
 * Hook para listar engines
 */
export function useContainerEngines(
  options?: Omit<UseQueryOptions<ContainerEngineListResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.engines.lists(),
    queryFn: () => window.containerAPI.listEngines(),
    staleTime: 300000, // 5 minutos
    ...options
  });
}

/**
 * Hook para status de engine
 */
export function useEngineStatus(
  engine: ContainerEngine,
  options?: Omit<UseQueryOptions<ContainerEngineStatusResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.engines.status(engine),
    queryFn: () => window.containerAPI.getEngineStatus(engine),
    staleTime: 60000, // 1 minuto
    refetchInterval: 120000, // 2 minutos
    ...options
  });
}

/**
 * Hook para detectar engines
 */
export function useDetectEngines(
  options?: Omit<UseQueryOptions<ContainerEngineDetectionResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: containerQueryKeys.engines.detection(),
    queryFn: () => window.containerAPI.detectEngines(),
    staleTime: 300000, // 5 minutos
    ...options
  });
}

/**
 * Hook para instalar engine
 */
export function useInstallEngine(
  options?: UseMutationOptions<ContainerEngineInstallResponse, Error, ContainerEngineInstallRequest>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: ContainerEngineInstallRequest) => window.containerAPI.installEngine(request),
    onSuccess: (data, variables) => {
      // Invalidar queries de engines
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.engines.all });
      
      toast.success(`${variables.engine} instalado com sucesso!`);
      
      if (data.requiresRestart) {
        toast('Reinicialização pode ser necessária', { icon: '⚠️' });
      }
    },
    onError: (error, variables) => {
      toast.error(`Erro ao instalar ${variables.engine}: ${error.message}`);
    },
    ...options
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook para verificar se engine está disponível
 */
export function useIsEngineAvailable(engine: ContainerEngine) {
  return useQuery({
    queryKey: ['engine-available', engine],
    queryFn: () => window.containerAPI.isEngineAvailable(engine),
    staleTime: 60000,
    refetchInterval: 120000
  });
}

/**
 * Hook para obter engine recomendado
 */
export function useRecommendedEngine() {
  return useQuery({
    queryKey: ['recommended-engine'],
    queryFn: () => window.containerAPI.getRecommendedEngine(),
    staleTime: 300000
  });
}

// ============================================================================
// REAL-TIME HOOKS
// ============================================================================

/**
 * Hook para eventos em tempo real de containers
 */
export function useContainerEvents() {
  const queryClient = useQueryClient();

  React.useEffect(() => {
    // Status changes
    const unsubscribeStatus = window.containerAPI.onContainerStatusChanged((data) => {
      // Invalidar queries relacionadas ao container
      queryClient.invalidateQueries({ 
        queryKey: containerQueryKeys.detail(data.containerId, data.engine) 
      });
      queryClient.invalidateQueries({ queryKey: containerQueryKeys.lists() });
      
      // Mostrar notificação
      const statusMessages = {
        running: 'iniciado',
        exited: 'parado',
        dead: 'falhou',
        paused: 'pausado'
      };
      
      const message = statusMessages[data.status as keyof typeof statusMessages];
      if (message) {
        toast(`Container ${message}`, { icon: '📦' });
      }
    });

    // Stats updates
    const unsubscribeStats = window.containerAPI.onContainerStatsUpdate((stats) => {
      // Atualizar cache de stats
      queryClient.setQueryData(
        containerQueryKeys.stats(stats.containerId),
        { success: true, stats, timestamp: new Date().toISOString() }
      );
    });

    // Engine status changes
    const unsubscribeEngine = window.containerAPI.onEngineStatusChanged((data) => {
      queryClient.invalidateQueries({ 
        queryKey: containerQueryKeys.engines.status(data.engine) 
      });
      
      if (!data.isAvailable) {
        toast.error(`${data.engine} não está mais disponível`);
      }
    });

    return () => {
      unsubscribeStatus();
      unsubscribeStats();
      unsubscribeEngine();
    };
  }, [queryClient]);
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Formata status de container para exibição
 */
export function formatContainerStatus(status: ContainerStatus): string {
  const statusMap = {
    created: 'Criado',
    running: 'Executando',
    paused: 'Pausado',
    restarting: 'Reiniciando',
    removing: 'Removendo',
    dead: 'Morto',
    exited: 'Parado'
  };
  
  return statusMap[status] || status;
}

/**
 * Formata tamanho em bytes para exibição
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

/**
 * Formata porcentagem de CPU/Memória
 */
export function formatPercentage(value: number): string {
  return `${value.toFixed(1)}%`;
}

/**
 * Obtém cor do status do container
 */
export function getStatusColor(status: ContainerStatus): string {
  const colorMap = {
    created: 'text-blue-600',
    running: 'text-green-600',
    paused: 'text-yellow-600',
    restarting: 'text-orange-600',
    removing: 'text-red-600',
    dead: 'text-red-800',
    exited: 'text-gray-600'
  };
  
  return colorMap[status] || 'text-gray-600';
}
