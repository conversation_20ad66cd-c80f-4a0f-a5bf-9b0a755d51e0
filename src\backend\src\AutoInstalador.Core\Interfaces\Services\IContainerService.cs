using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Interfaces.Services;

/// <summary>
/// Interface para serviços de gerenciamento de containers
/// </summary>
public interface IContainerService
{
    /// <summary>
    /// Lista todos os containers disponíveis
    /// </summary>
    /// <param name="request">Parâmetros de filtro</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de containers</returns>
    Task<ContainerListResponse> ListContainersAsync(ContainerListRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações detalhadas de um container específico
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do container</returns>
    Task<ContainerResponse> GetContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa um novo container
    /// </summary>
    /// <param name="request">Configurações do container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do container criado</returns>
    Task<ContainerRunResponse> RunContainerAsync(ContainerRunRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Inicia um container parado
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> StartContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Para um container em execução
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> StopContainerAsync(string containerId, int? timeout = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reinicia um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> RestartContainerAsync(string containerId, int? timeout = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pausa um container em execução
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> PauseContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Despausa um container pausado
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> UnpauseContainerAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<ContainerActionResponse> RemoveContainerAsync(string containerId, bool force = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém logs de um container
    /// </summary>
    /// <param name="request">Parâmetros dos logs</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Logs do container</returns>
    Task<ContainerLogsResponse> GetContainerLogsAsync(ContainerLogsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas de um container
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do container</returns>
    Task<ContainerStatsResponse> GetContainerStatsAsync(string containerId, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa um comando em um container em execução
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="command">Comando a ser executado</param>
    /// <param name="args">Argumentos do comando</param>
    /// <param name="interactive">Modo interativo</param>
    /// <param name="tty">Alocar TTY</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da execução</returns>
    Task<ContainerExecResponse> ExecContainerAsync(string containerId, string command, string[]? args = null, bool interactive = false, bool tty = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface para serviços de gerenciamento de imagens de container
/// </summary>
public interface IContainerImageService
{
    /// <summary>
    /// Lista todas as imagens disponíveis
    /// </summary>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de imagens</returns>
    Task<ContainerImageListResponse> ListImagesAsync(ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Faz download de uma imagem
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="tag">Tag da imagem</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado do download</returns>
    Task<ContainerImagePullResponse> PullImageAsync(string imageName, string? tag = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove uma imagem
    /// </summary>
    /// <param name="imageId">ID da imagem</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da remoção</returns>
    Task<ContainerImageRemoveResponse> RemoveImageAsync(string imageId, bool force = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Busca imagens em registries
    /// </summary>
    /// <param name="searchTerm">Termo de busca</param>
    /// <param name="limit">Limite de resultados</param>
    /// <param name="engine">Engine de container (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultados da busca</returns>
    Task<ContainerImageSearchResponse> SearchImagesAsync(string searchTerm, int limit = 25, ContainerEngine? engine = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface para serviços de gerenciamento de engines de container
/// </summary>
public interface IContainerEngineService
{
    /// <summary>
    /// Lista engines de container disponíveis
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de engines</returns>
    Task<ContainerEngineListResponse> ListEnginesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica o status de um engine específico
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Status do engine</returns>
    Task<ContainerEngineStatusResponse> GetEngineStatusAsync(ContainerEngine engine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações detalhadas de um engine
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do engine</returns>
    Task<ContainerEngineInfoResponse> GetEngineInfoAsync(ContainerEngine engine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Instala um engine de container
    /// </summary>
    /// <param name="request">Parâmetros de instalação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da instalação</returns>
    Task<ContainerEngineInstallResponse> InstallEngineAsync(ContainerEngineInstallRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Detecta engines de container instalados
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Engines detectados</returns>
    Task<ContainerEngineDetectionResponse> DetectEnginesAsync(CancellationToken cancellationToken = default);
}
