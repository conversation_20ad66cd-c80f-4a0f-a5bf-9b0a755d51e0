# React Query 5.56.2 - Links e Recursos Adicionais

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.56.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tanstack.com/query/latest
- **GitHub:** https://github.com/TanStack/query
- **Documentação:** https://tanstack.com/query/latest/docs/framework/react/overview
- **NPM/Package:** https://www.npmjs.com/package/@tanstack/react-query
- **Fórum/Community:** https://github.com/TanStack/query/discussions
- **Stack Overflow Tag:** `react-query`

---

## 📚 **DOCUMENTAÇÃO OFICIAL**

### **Core Documentation**
- **Getting Started:** https://tanstack.com/query/latest/docs/framework/react/quick-start
- **Queries:** https://tanstack.com/query/latest/docs/framework/react/guides/queries
- **Mutations:** https://tanstack.com/query/latest/docs/framework/react/guides/mutations
- **Query Invalidation:** https://tanstack.com/query/latest/docs/framework/react/guides/query-invalidation
- **Optimistic Updates:** https://tanstack.com/query/latest/docs/framework/react/guides/optimistic-updates

### **Advanced Topics**
- **Infinite Queries:** https://tanstack.com/query/latest/docs/framework/react/guides/infinite-queries
- **Suspense:** https://tanstack.com/query/latest/docs/framework/react/guides/suspense
- **SSR:** https://tanstack.com/query/latest/docs/framework/react/guides/ssr
- **Offline Support:** https://tanstack.com/query/latest/docs/framework/react/guides/network-mode
- **Background Refetching:** https://tanstack.com/query/latest/docs/framework/react/guides/background-fetching

### **React 19.2 Integration**
- **useSuspenseQuery:** https://tanstack.com/query/latest/docs/framework/react/reference/useSuspenseQuery
- **Concurrent Features:** https://tanstack.com/query/latest/docs/framework/react/guides/concurrent-features
- **Server Components:** https://tanstack.com/query/latest/docs/framework/react/guides/server-components

---

## 🛠️ **FERRAMENTAS E UTILITÁRIOS**

### **Official Tools**
- **TanStack Query DevTools:** https://tanstack.com/query/latest/docs/framework/react/devtools
  - Browser extension para debugging
  - Query inspection em tempo real
  - Performance monitoring
  - Cache visualization

- **Query Key Factory:** Pattern para organizar query keys
  - Hierarquia consistente de keys
  - TypeScript support
  - Invalidation patterns

### **Community Tools**
- **React Query Kit:** https://github.com/liaoliao666/react-query-kit
- **Query Key Factory:** https://github.com/lukemorales/query-key-factory
- **React Query Auth:** https://github.com/ts-react/react-query-auth
- **React Query Firebase:** https://github.com/invertase/react-query-firebase

### **Development Tools**
- **ESLint Plugin:** @tanstack/eslint-plugin-query
- **VS Code Extension:** TanStack Query snippets
- **TypeScript Definitions:** Built-in type definitions

---

## 🎓 **TUTORIAIS E GUIAS**

### **Beginner Tutorials**
- **Official Tutorial:** https://tanstack.com/query/latest/docs/framework/react/quick-start
- **TanStack YouTube Channel:** https://www.youtube.com/c/tanstack
- **React Query Essentials:** Step-by-step learning path

### **Advanced Guides**
- **Performance Optimization:** Best practices for large applications
- **Complex State Management:** Advanced patterns with React Query
- **Real-time Data:** WebSocket integration patterns

### **React Integration**
- **React 19.2 Integration:** Modern React features with Query
- **Concurrent Features:** Using Query with Suspense and transitions
- **Server Components:** Query in server-rendered applications

### **Desktop App Specific**
- **Electron + React Query:** Desktop application patterns
- **Offline-First:** Building robust offline experiences
- **Real-time Updates:** Live data synchronization

---

## 🏗️ **BOILERPLATES E TEMPLATES**

### **Official Examples**
- **TanStack Query Examples:** https://github.com/TanStack/query/tree/main/examples
- **CodeSandbox Templates:** Ready-to-use Query examples
- **Next.js Templates:** SSR with React Query

### **Community Templates**
- **React Query Starter:** Complete application templates
- **Electron + Query:** Desktop app boilerplates
- **Real-time Dashboard:** Live data dashboard templates

### **Auto-Instalador Specific**
- **Container Management:** Query patterns for container data
- **Real-time Monitoring:** Live stats and metrics
- **Offline Support:** Robust offline-first patterns

---

## 📖 **LIVROS E RECURSOS EDUCACIONAIS**

### **Books**
- **"React Query in Action"**
  - Comprehensive guide to React Query
  - Real-world examples and patterns
  - Performance optimization techniques

- **"Modern React Data Fetching"**
  - Advanced data fetching patterns
  - Integration with modern React features
  - Production-ready techniques

### **Online Courses**
- **React Query Course (Udemy):** https://www.udemy.com/topic/react-query/
- **Advanced React Query (Pluralsight):** https://www.pluralsight.com/
- **TanStack Query Masterclass (Frontend Masters):** https://frontendmasters.com/

### **YouTube Channels**
- **TanStack:** Official channel with tutorials
- **Tanner Linsley:** Creator's personal channel
- **Kent C. Dodds:** React Query tutorials
- **Jack Herrington:** Advanced React Query patterns

---

## 🤝 **COMUNIDADE E SUPORTE**

### **Official Channels**
- **GitHub Discussions:** https://github.com/TanStack/query/discussions
- **Discord:** https://discord.gg/tanstack
- **Twitter:** @tanstack

### **Community Forums**
- **Reddit:** r/reactjs (React Query discussions)
- **Stack Overflow:** Tag `react-query` and `tanstack-query`
- **Dev.to:** React Query articles and tutorials

### **Regional Communities**
- **React Query Brasil:** Telegram groups and Discord channels
- **TanStack Developers:** LinkedIn groups
- **Local Meetups:** Check meetup.com for React/data fetching events

---

## 🔧 **DEBUGGING E PROFILING**

### **DevTools Usage**
```typescript
// Configuração do DevTools
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="app">
        {/* Sua aplicação */}
      </div>
      
      {/* DevTools apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom-right"
        />
      )}
    </QueryClientProvider>
  );
}
```

### **Performance Monitoring**
```typescript
// Monitor de performance customizado
function useQueryPerformance() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const cache = queryClient.getQueryCache();
    
    const unsubscribe = cache.subscribe((event) => {
      if (event?.type === 'updated') {
        console.log('Query updated:', {
          key: event.query.queryKey,
          status: event.query.state.status,
          dataUpdatedAt: event.query.state.dataUpdatedAt
        });
      }
    });
    
    return unsubscribe;
  }, [queryClient]);
}
```

### **Debug Helpers**
```typescript
// Helpers para debugging
export const queryDebug = {
  logCache: (queryClient: QueryClient) => {
    const queries = queryClient.getQueryCache().getAll();
    console.table(queries.map(query => ({
      key: JSON.stringify(query.queryKey),
      status: query.state.status,
      observers: query.getObserversCount(),
      lastUpdated: new Date(query.state.dataUpdatedAt).toLocaleTimeString()
    })));
  },
  
  logQuery: (queryKey: unknown[]) => {
    const query = queryClient.getQueryCache().find({ queryKey });
    console.log('Query details:', {
      key: queryKey,
      state: query?.state,
      observers: query?.getObserversCount()
    });
  }
};
```

---

## 📊 **BENCHMARKING E TESTING**

### **Performance Benchmarks**
- **Query Resolution Speed:** Measuring query performance
- **Memory Usage:** Cache memory consumption patterns
- **Bundle Size Impact:** Query's effect on application size

### **Testing Integration**
```typescript
// Jest + Testing Library
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useContainers } from './useContainers';

test('useContainers returns container data', async () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
  
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  
  const { result } = renderHook(() => useContainers(), { wrapper });
  
  await waitFor(() => {
    expect(result.current.isSuccess).toBe(true);
  });
  
  expect(result.current.data).toHaveLength(3);
});

// Mock Service Worker para testes
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/containers', (req, res, ctx) => {
    return res(ctx.json([
      { id: '1', name: 'nginx', status: 'running' },
      { id: '2', name: 'redis', status: 'stopped' }
    ]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

---

## 🔐 **SEGURANÇA E COMPLIANCE**

### **Security Best Practices**
- **Query Key Sanitization:** Validate query keys from user input
- **Data Validation:** Validate API responses before caching
- **Error Handling:** Secure error message handling

### **Authentication Integration**
```typescript
// Integração com autenticação
function useAuthenticatedQuery<T>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: UseQueryOptions<T>
) {
  const { token } = useAuth();
  
  return useQuery({
    queryKey: [...queryKey, token], // Include token in key
    queryFn: async () => {
      if (!token) throw new Error('Not authenticated');
      return queryFn();
    },
    enabled: !!token,
    ...options
  });
}

// Logout cleanup
function useLogoutCleanup() {
  const queryClient = useQueryClient();
  
  const logout = useCallback(() => {
    // Clear all cached data on logout
    queryClient.clear();
    
    // Or selectively clear sensitive data
    queryClient.removeQueries({ 
      predicate: (query) => 
        query.queryKey.some(key => 
          typeof key === 'string' && key.includes('user')
        )
    });
  }, [queryClient]);
  
  return logout;
}
```

---

## 🎯 **ESPECÍFICO PARA AUTO-INSTALADOR V3 LITE**

### **Desktop App Optimization**
- **Electron Integration:** Best practices for desktop queries
- **Offline Support:** Robust offline-first patterns
- **Performance Tuning:** Hardware-specific optimizations

### **Container Management Patterns**
- **Real-time Data:** Live container stats and logs
- **Bulk Operations:** Managing multiple containers
- **State Synchronization:** Keeping UI in sync with container state

### **Development Workflow**
```typescript
// Query patterns específicos para Auto-Instalador
export const containerQueries = {
  // Lista de containers com filtros
  list: (filters?: ContainerFilters) => ({
    queryKey: ['containers', 'list', filters],
    queryFn: () => fetchContainers(filters),
    staleTime: 30 * 1000,
    refetchInterval: 10 * 1000
  }),
  
  // Stats em tempo real
  stats: (id: string) => ({
    queryKey: ['containers', id, 'stats'],
    queryFn: () => fetchContainerStats(id),
    refetchInterval: 2 * 1000,
    staleTime: 1000,
    retry: false
  }),
  
  // Logs com infinite query
  logs: (id: string) => ({
    queryKey: ['containers', id, 'logs'],
    queryFn: ({ pageParam }) => fetchContainerLogs(id, pageParam),
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    maxPages: 10
  })
};
```

---

## 📱 **MOBILE E CROSS-PLATFORM**

### **React Native Integration**
```typescript
// React Native com React Query
import { useQuery } from '@tanstack/react-query';
import NetInfo from '@react-native-async-storage/async-storage';

function useNetworkAwareQuery<T>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: UseQueryOptions<T>
) {
  const [isConnected, setIsConnected] = useState(true);
  
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });
    
    return unsubscribe;
  }, []);
  
  return useQuery({
    queryKey,
    queryFn,
    networkMode: isConnected ? 'online' : 'offlineFirst',
    ...options
  });
}
```

### **PWA Integration**
```typescript
// Service Worker com React Query
function usePWAQuery<T>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: UseQueryOptions<T>
) {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  return useQuery({
    queryKey,
    queryFn,
    networkMode: isOnline ? 'online' : 'offlineFirst',
    staleTime: isOnline ? 30 * 1000 : Infinity,
    ...options
  });
}
```

---

## 🔄 **MIGRATION RESOURCES**

### **From Other Libraries**
- **SWR to React Query:** Migration guide and patterns
- **Apollo Client to React Query:** GraphQL to REST migration
- **Redux to React Query:** State management migration

### **Version Upgrades**
- **v4 to v5 Migration:** Breaking changes and new features
- **API Updates:** New hooks and deprecated methods
- **Performance Improvements:** Optimization opportunities

### **Migration Tools**
```bash
# Community migration tools
npm install -g @tanstack/query-migrate
tanstack-query-migrate --from=4 --to=5

# Codemod scripts
npx @tanstack/query-codemods v4-to-v5
```

---

## 🌍 **INTERNATIONALIZATION**

### **Error Messages**
```typescript
// Internacionalização de mensagens de erro
const errorMessages = {
  'pt-BR': {
    networkError: 'Erro de conexão. Verifique sua internet.',
    serverError: 'Erro no servidor. Tente novamente.',
    notFound: 'Recurso não encontrado.',
    unauthorized: 'Acesso não autorizado.'
  },
  'en-US': {
    networkError: 'Network error. Check your connection.',
    serverError: 'Server error. Please try again.',
    notFound: 'Resource not found.',
    unauthorized: 'Unauthorized access.'
  }
};

function useLocalizedQuery<T>(
  queryKey: unknown[],
  queryFn: () => Promise<T>,
  options?: UseQueryOptions<T>
) {
  const locale = useLocale();
  
  return useQuery({
    queryKey,
    queryFn,
    ...options,
    onError: (error: any) => {
      const message = errorMessages[locale]?.[error.code] || error.message;
      showErrorNotification(message);
    }
  });
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - React Query References & Resources
