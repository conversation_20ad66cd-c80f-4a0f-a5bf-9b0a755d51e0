using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Detector e instalador de engines de container
/// </summary>
public class ContainerEngineDetector : IContainerEngineDetector
{
    private readonly ILogger<ContainerEngineDetector> _logger;

    public ContainerEngineDetector(ILogger<ContainerEngineDetector> logger)
    {
        _logger = logger;
    }

    public async Task<IEnumerable<ContainerEngineInfo>> DetectEnginesAsync(CancellationToken cancellationToken = default)
    {
        var engines = new List<ContainerEngineInfo>();

        // Detectar Docker
        var dockerInfo = await DetectDockerAsync(cancellationToken);
        if (dockerInfo != null)
            engines.Add(dockerInfo);

        // Detectar Podman
        var podmanInfo = await DetectPodmanAsync(cancellationToken);
        if (podmanInfo != null)
            engines.Add(podmanInfo);

        return engines;
    }

    public async Task<bool> IsEngineInstalledAsync(ContainerEngine engine, CancellationToken cancellationToken = default)
    {
        var engineName = engine == ContainerEngine.Docker ? "docker" : "podman";
        return await IsCommandAvailableAsync(engineName, cancellationToken);
    }

    public async Task<bool> InstallEngineAsync(ContainerEngine engine, string platform, string? packageManager = null, bool force = false, CancellationToken cancellationToken = default)
    {
        try
        {
            // Verificar se já está instalado
            if (!force && await IsEngineInstalledAsync(engine, cancellationToken))
            {
                _logger.LogInformation("{Engine} já está instalado", engine);
                return true;
            }

            var commands = GetInstallCommands(engine, platform, packageManager);
            
            foreach (var command in commands)
            {
                _logger.LogInformation("Executando comando de instalação: {Command}", command);
                
                var result = await ExecuteCommandAsync(command, cancellationToken);
                if (!result.Success)
                {
                    _logger.LogError("Falha na instalação: {Error}", result.StandardError);
                    return false;
                }
            }

            // Verificar se a instalação foi bem-sucedida
            await Task.Delay(2000, cancellationToken); // Aguardar um pouco
            var isInstalled = await IsEngineInstalledAsync(engine, cancellationToken);
            
            if (isInstalled)
            {
                _logger.LogInformation("{Engine} instalado com sucesso", engine);
            }
            else
            {
                _logger.LogWarning("{Engine} pode não ter sido instalado corretamente", engine);
            }

            return isInstalled;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao instalar {Engine}", engine);
            return false;
        }
    }

    public string[] GetInstallCommands(ContainerEngine engine, string platform, string? packageManager = null)
    {
        var platformLower = platform.ToLowerInvariant();
        var engineName = engine.ToString().ToLowerInvariant();

        return platformLower switch
        {
            "windows" => GetWindowsInstallCommands(engine, packageManager),
            "macos" => GetMacOSInstallCommands(engine, packageManager),
            "linux" => GetLinuxInstallCommands(engine, packageManager),
            _ => throw new PlatformNotSupportedException($"Plataforma não suportada: {platform}")
        };
    }

    private async Task<ContainerEngineInfo?> DetectDockerAsync(CancellationToken cancellationToken)
    {
        try
        {
            var versionResult = await ExecuteCommandAsync("docker --version", cancellationToken);
            if (!versionResult.Success)
                return null;

            var info = new ContainerEngineInfo
            {
                Engine = ContainerEngine.Docker,
                IsAvailable = true
            };

            // Extrair versão
            var versionMatch = System.Text.RegularExpressions.Regex.Match(
                versionResult.StandardOutput, @"Docker version (\d+\.\d+\.\d+)");
            if (versionMatch.Success)
            {
                info.Version = versionMatch.Groups[1].Value;
            }

            // Verificar se está rodando
            var infoResult = await ExecuteCommandAsync("docker info", cancellationToken);
            info.IsRunning = infoResult.Success;

            if (info.IsRunning)
            {
                // Obter informações detalhadas
                await PopulateDockerInfoAsync(info, cancellationToken);
            }

            return info;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao detectar Docker");
            return null;
        }
    }

    private async Task<ContainerEngineInfo?> DetectPodmanAsync(CancellationToken cancellationToken)
    {
        try
        {
            var versionResult = await ExecuteCommandAsync("podman --version", cancellationToken);
            if (!versionResult.Success)
                return null;

            var info = new ContainerEngineInfo
            {
                Engine = ContainerEngine.Podman,
                IsAvailable = true
            };

            // Extrair versão
            var versionMatch = System.Text.RegularExpressions.Regex.Match(
                versionResult.StandardOutput, @"podman version (\d+\.\d+\.\d+)");
            if (versionMatch.Success)
            {
                info.Version = versionMatch.Groups[1].Value;
            }

            // Verificar se está funcionando
            var infoResult = await ExecuteCommandAsync("podman info", cancellationToken);
            info.IsRunning = infoResult.Success;

            if (info.IsRunning)
            {
                // Obter informações detalhadas
                await PopulatePodmanInfoAsync(info, cancellationToken);
            }

            return info;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao detectar Podman");
            return null;
        }
    }

    private async Task PopulateDockerInfoAsync(ContainerEngineInfo info, CancellationToken cancellationToken)
    {
        try
        {
            var infoResult = await ExecuteCommandAsync("docker info --format json", cancellationToken);
            if (infoResult.Success)
            {
                var dockerInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(infoResult.StandardOutput);
                
                if (dockerInfo.TryGetProperty("ServerVersion", out var serverVersion))
                    info.ApiVersion = serverVersion.GetString() ?? "";

                if (dockerInfo.TryGetProperty("Driver", out var driver))
                    info.StorageDriver = driver.GetString() ?? "";

                if (dockerInfo.TryGetProperty("OSType", out var osType))
                    info.Platform.Os = osType.GetString() ?? "";

                if (dockerInfo.TryGetProperty("Architecture", out var arch))
                    info.Platform.Architecture = arch.GetString() ?? "";

                if (dockerInfo.TryGetProperty("Name", out var name))
                    info.Platform.Name = name.GetString() ?? "";

                if (dockerInfo.TryGetProperty("DefaultRuntime", out var runtime))
                    info.Runtime.Name = runtime.GetString() ?? "";
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao obter informações detalhadas do Docker");
        }
    }

    private async Task PopulatePodmanInfoAsync(ContainerEngineInfo info, CancellationToken cancellationToken)
    {
        try
        {
            var infoResult = await ExecuteCommandAsync("podman info --format json", cancellationToken);
            if (infoResult.Success)
            {
                var podmanInfo = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(infoResult.StandardOutput);
                
                if (podmanInfo.TryGetProperty("host", out var host))
                {
                    if (host.TryGetProperty("os", out var os))
                        info.Platform.Os = os.GetString() ?? "";

                    if (host.TryGetProperty("arch", out var arch))
                        info.Platform.Architecture = arch.GetString() ?? "";

                    if (host.TryGetProperty("hostname", out var hostname))
                        info.Platform.Name = hostname.GetString() ?? "";

                    if (host.TryGetProperty("security", out var security))
                    {
                        if (security.TryGetProperty("rootless", out var rootless))
                            info.Rootless = rootless.GetBoolean();
                    }

                    if (host.TryGetProperty("ociRuntime", out var ociRuntime))
                    {
                        if (ociRuntime.TryGetProperty("name", out var runtimeName))
                            info.Runtime.Name = runtimeName.GetString() ?? "";

                        if (ociRuntime.TryGetProperty("version", out var runtimeVersion))
                            info.Runtime.Version = runtimeVersion.GetString() ?? "";
                    }
                }

                if (podmanInfo.TryGetProperty("store", out var store))
                {
                    if (store.TryGetProperty("graphDriverName", out var driver))
                        info.StorageDriver = driver.GetString() ?? "";
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao obter informações detalhadas do Podman");
        }
    }

    private string[] GetWindowsInstallCommands(ContainerEngine engine, string? packageManager)
    {
        var pm = packageManager?.ToLowerInvariant() ?? "winget";

        return engine switch
        {
            ContainerEngine.Docker => pm switch
            {
                "winget" => new[] { "winget install Docker.DockerDesktop" },
                "choco" => new[] { "choco install docker-desktop -y" },
                _ => new[] { "winget install Docker.DockerDesktop" }
            },
            ContainerEngine.Podman => pm switch
            {
                "winget" => new[] { "winget install RedHat.Podman" },
                "choco" => new[] { "choco install podman -y" },
                _ => new[] { "winget install RedHat.Podman" }
            },
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    private string[] GetMacOSInstallCommands(ContainerEngine engine, string? packageManager)
    {
        var pm = packageManager?.ToLowerInvariant() ?? "brew";

        return engine switch
        {
            ContainerEngine.Docker => pm switch
            {
                "brew" => new[] { "brew install --cask docker" },
                _ => new[] { "brew install --cask docker" }
            },
            ContainerEngine.Podman => pm switch
            {
                "brew" => new[] { "brew install podman" },
                _ => new[] { "brew install podman" }
            },
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    private string[] GetLinuxInstallCommands(ContainerEngine engine, string? packageManager)
    {
        var pm = packageManager?.ToLowerInvariant() ?? DetectLinuxPackageManager();

        return engine switch
        {
            ContainerEngine.Docker => pm switch
            {
                "apt" => new[]
                {
                    "apt-get update",
                    "apt-get install -y ca-certificates curl gnupg lsb-release",
                    "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg",
                    "echo \"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable\" | tee /etc/apt/sources.list.d/docker.list > /dev/null",
                    "apt-get update",
                    "apt-get install -y docker-ce docker-ce-cli containerd.io"
                },
                "dnf" => new[] { "dnf install -y docker" },
                "yum" => new[] { "yum install -y docker" },
                _ => new[] { "apt-get update && apt-get install -y docker.io" }
            },
            ContainerEngine.Podman => pm switch
            {
                "apt" => new[] { "apt-get update && apt-get install -y podman" },
                "dnf" => new[] { "dnf install -y podman" },
                "yum" => new[] { "yum install -y podman" },
                _ => new[] { "apt-get update && apt-get install -y podman" }
            },
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    private string DetectLinuxPackageManager()
    {
        var packageManagers = new[]
        {
            ("apt-get", "apt"),
            ("dnf", "dnf"),
            ("yum", "yum"),
            ("pacman", "pacman"),
            ("zypper", "zypper")
        };

        foreach (var (command, name) in packageManagers)
        {
            try
            {
                var result = ExecuteCommandAsync($"which {command}", CancellationToken.None).Result;
                if (result.Success)
                    return name;
            }
            catch
            {
                // Ignorar erros
            }
        }

        return "apt"; // Fallback padrão
    }

    private async Task<bool> IsCommandAvailableAsync(string command, CancellationToken cancellationToken)
    {
        try
        {
            var checkCommand = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) 
                ? $"where {command}" 
                : $"which {command}";

            var result = await ExecuteCommandAsync(checkCommand, cancellationToken);
            return result.Success;
        }
        catch
        {
            return false;
        }
    }

    private async Task<CommandResult> ExecuteCommandAsync(string command, CancellationToken cancellationToken)
    {
        var startTime = DateTime.UtcNow;
        
        // Separar comando e argumentos
        var parts = command.Split(' ', 2);
        var fileName = parts[0];
        var arguments = parts.Length > 1 ? parts[1] : "";

        var processStartInfo = new ProcessStartInfo
        {
            FileName = fileName,
            Arguments = arguments,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        try
        {
            using var process = new Process { StartInfo = processStartInfo };
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            process.Start();

            var outputTask = process.StandardOutput.ReadToEndAsync();
            var errorTask = process.StandardError.ReadToEndAsync();

            await process.WaitForExitAsync(combinedCts.Token);

            var output = await outputTask;
            var error = await errorTask;
            var duration = DateTime.UtcNow - startTime;

            return new CommandResult
            {
                ExitCode = process.ExitCode,
                StandardOutput = output,
                StandardError = error,
                Duration = duration,
                Command = command,
                Arguments = Array.Empty<string>()
            };
        }
        catch (OperationCanceledException)
        {
            return new CommandResult
            {
                ExitCode = -1,
                StandardError = "Comando cancelado ou expirou",
                Duration = DateTime.UtcNow - startTime,
                Command = command,
                Arguments = Array.Empty<string>()
            };
        }
        catch (Exception ex)
        {
            return new CommandResult
            {
                ExitCode = -1,
                StandardError = ex.Message,
                Duration = DateTime.UtcNow - startTime,
                Command = command,
                Arguments = Array.Empty<string>()
            };
        }
    }
}
