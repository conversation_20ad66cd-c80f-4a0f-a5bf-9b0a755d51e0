using AutoInstalador.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace AutoInstalador.Core.DTOs.Requests;

/// <summary>
/// Request para listar containers
/// </summary>
public class ContainerListRequest
{
    /// <summary>
    /// Incluir containers parados
    /// </summary>
    public bool All { get; set; } = false;

    /// <summary>
    /// Filtros de busca
    /// </summary>
    public ContainerFilters? Filters { get; set; }

    /// <summary>
    /// Engine específico (opcional)
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Filtros para containers
/// </summary>
public class ContainerFilters
{
    /// <summary>
    /// Filtrar por status
    /// </summary>
    public List<ContainerStatus>? Status { get; set; }

    /// <summary>
    /// Filtrar por nome
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filtrar por imagem
    /// </summary>
    public string? Image { get; set; }

    /// <summary>
    /// Filtrar por labels
    /// </summary>
    public List<string>? Labels { get; set; }
}

/// <summary>
/// Request para executar container
/// </summary>
public class ContainerRunRequest
{
    /// <summary>
    /// Imagem do container
    /// </summary>
    [Required]
    [StringLength(500)]
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    [StringLength(100)]
    public string? Name { get; set; }

    /// <summary>
    /// Portas do container
    /// </summary>
    public List<ContainerPortDto>? Ports { get; set; }

    /// <summary>
    /// Volumes do container
    /// </summary>
    public List<ContainerVolumeDto>? Volumes { get; set; }

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string>? Environment { get; set; }

    /// <summary>
    /// Comando a executar
    /// </summary>
    [StringLength(1000)]
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public List<string>? Args { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    [StringLength(500)]
    public string? WorkingDir { get; set; }

    /// <summary>
    /// Usuário do container
    /// </summary>
    [StringLength(100)]
    public string? User { get; set; }

    /// <summary>
    /// Executar em modo detached
    /// </summary>
    public bool Detached { get; set; } = true;

    /// <summary>
    /// Modo interativo
    /// </summary>
    public bool Interactive { get; set; } = false;

    /// <summary>
    /// Alocar TTY
    /// </summary>
    public bool Tty { get; set; } = false;

    /// <summary>
    /// Remover container após execução
    /// </summary>
    public bool Remove { get; set; } = false;

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public RestartPolicy RestartPolicy { get; set; } = RestartPolicy.No;

    /// <summary>
    /// Recursos do container
    /// </summary>
    public ContainerResourcesDto? Resources { get; set; }

    /// <summary>
    /// Modo de rede
    /// </summary>
    [StringLength(100)]
    public string NetworkMode { get; set; } = "bridge";

    /// <summary>
    /// Labels do container
    /// </summary>
    public Dictionary<string, string>? Labels { get; set; }

    /// <summary>
    /// Engine específico (opcional)
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// DTO para porta de container
/// </summary>
public class ContainerPortDto
{
    /// <summary>
    /// Porta do container
    /// </summary>
    [Range(1, 65535)]
    public int ContainerPort { get; set; }

    /// <summary>
    /// Porta do host (opcional)
    /// </summary>
    [Range(1, 65535)]
    public int? HostPort { get; set; }

    /// <summary>
    /// IP do host (opcional)
    /// </summary>
    public string? HostIp { get; set; }

    /// <summary>
    /// Protocolo da porta
    /// </summary>
    public NetworkProtocol Protocol { get; set; } = NetworkProtocol.Tcp;

    /// <summary>
    /// Tipo da porta
    /// </summary>
    public PortType Type { get; set; } = PortType.Exposed;
}

/// <summary>
/// DTO para volume de container
/// </summary>
public class ContainerVolumeDto
{
    /// <summary>
    /// Caminho de origem (host)
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// Caminho de destino (container)
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string Destination { get; set; } = string.Empty;

    /// <summary>
    /// Modo de acesso
    /// </summary>
    public VolumeMode Mode { get; set; } = VolumeMode.ReadWrite;

    /// <summary>
    /// Tipo do volume
    /// </summary>
    public VolumeType Type { get; set; } = VolumeType.Bind;
}

/// <summary>
/// DTO para recursos de container
/// </summary>
public class ContainerResourcesDto
{
    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    [Range(0, long.MaxValue)]
    public long? Memory { get; set; }

    /// <summary>
    /// Limite de memória + swap em bytes
    /// </summary>
    [Range(0, long.MaxValue)]
    public long? MemorySwap { get; set; }

    /// <summary>
    /// Shares de CPU
    /// </summary>
    [Range(0, int.MaxValue)]
    public int? CpuShares { get; set; }

    /// <summary>
    /// Quota de CPU
    /// </summary>
    [Range(0, long.MaxValue)]
    public long? CpuQuota { get; set; }

    /// <summary>
    /// Período de CPU
    /// </summary>
    [Range(0, long.MaxValue)]
    public long? CpuPeriod { get; set; }

    /// <summary>
    /// CPUs permitidas
    /// </summary>
    public string? CpusetCpus { get; set; }

    /// <summary>
    /// Nós de memória permitidos
    /// </summary>
    public string? CpusetMems { get; set; }

    /// <summary>
    /// Peso de I/O de bloco
    /// </summary>
    [Range(0, int.MaxValue)]
    public int? BlkioWeight { get; set; }

    /// <summary>
    /// Desabilitar OOM killer
    /// </summary>
    public bool? OomKillDisable { get; set; }
}

/// <summary>
/// Request para logs de container
/// </summary>
public class ContainerLogsRequest
{
    /// <summary>
    /// ID do container
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Seguir logs em tempo real
    /// </summary>
    public bool Follow { get; set; } = false;

    /// <summary>
    /// Número de linhas do final
    /// </summary>
    [Range(1, 10000)]
    public int? Tail { get; set; }

    /// <summary>
    /// Data/hora de início
    /// </summary>
    public DateTime? Since { get; set; }

    /// <summary>
    /// Data/hora de fim
    /// </summary>
    public DateTime? Until { get; set; }

    /// <summary>
    /// Incluir timestamps
    /// </summary>
    public bool Timestamps { get; set; } = true;

    /// <summary>
    /// Engine específico (opcional)
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Request para ação em container
/// </summary>
public class ContainerActionRequest
{
    /// <summary>
    /// ID do container
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Ação a executar
    /// </summary>
    [Required]
    public ContainerAction Action { get; set; }

    /// <summary>
    /// Forçar ação
    /// </summary>
    public bool Force { get; set; } = false;

    /// <summary>
    /// Timeout em segundos
    /// </summary>
    [Range(1, 300)]
    public int? Timeout { get; set; }

    /// <summary>
    /// Engine específico (opcional)
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Request para execução em container
/// </summary>
public class ContainerExecRequest
{
    /// <summary>
    /// ID do container
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Comando a executar
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public List<string>? Args { get; set; }

    /// <summary>
    /// Modo interativo
    /// </summary>
    public bool Interactive { get; set; } = false;

    /// <summary>
    /// Alocar TTY
    /// </summary>
    public bool Tty { get; set; } = false;

    /// <summary>
    /// Engine específico (opcional)
    /// </summary>
    public ContainerEngine? Engine { get; set; }
}

/// <summary>
/// Request para instalação de engine
/// </summary>
public class ContainerEngineInstallRequest
{
    /// <summary>
    /// Engine a instalar
    /// </summary>
    [Required]
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Plataforma do sistema
    /// </summary>
    [Required]
    public Platform Platform { get; set; }

    /// <summary>
    /// Gerenciador de pacotes
    /// </summary>
    public PackageManager? PackageManager { get; set; }

    /// <summary>
    /// Forçar instalação
    /// </summary>
    public bool Force { get; set; } = false;
}
