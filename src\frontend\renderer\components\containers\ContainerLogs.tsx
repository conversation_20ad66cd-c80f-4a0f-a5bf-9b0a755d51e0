/**
 * Container Logs - Visualizador de logs de container
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para exibir logs de containers em tempo real
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState, useEffect, useRef } from 'react';
import { useContainerLogs } from '../../services/container-service';
import type { ContainerEngine, ContainerLog } from '../../../../shared/types/api.types';

interface ContainerLogsProps {
  containerId: string;
  engine: ContainerEngine;
  className?: string;
}

export const ContainerLogs: React.FC<ContainerLogsProps> = ({
  containerId,
  engine,
  className = ''
}) => {
  const [follow, setFollow] = useState(false);
  const [tail, setTail] = useState(100);
  const [timestamps, setTimestamps] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(true);
  
  const logsEndRef = useRef<HTMLDivElement>(null);
  const logsContainerRef = useRef<HTMLDivElement>(null);

  // Hook para obter logs
  const { data: logs, isLoading, error } = useContainerLogs({
    containerId,
    engine,
    follow,
    tail,
    timestamps
  });

  // Auto-scroll para o final quando novos logs chegam
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);

  // Detectar se o usuário fez scroll manual
  const handleScroll = () => {
    if (logsContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = logsContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    }
  };

  // Filtrar logs baseado na busca
  const filteredLogs = React.useMemo(() => {
    if (!logs || !searchTerm) return logs || [];
    
    return logs.filter(log => 
      log.message.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [logs, searchTerm]);

  // Determinar cor do log baseado no stream e conteúdo
  const getLogColor = (log: ContainerLog): string => {
    if (log.stream === 'stderr') return 'text-red-400';
    
    const message = log.message.toLowerCase();
    if (message.includes('error') || message.includes('fail')) return 'text-red-400';
    if (message.includes('warn')) return 'text-yellow-400';
    if (message.includes('info')) return 'text-blue-400';
    if (message.includes('debug')) return 'text-gray-500';
    
    return 'text-gray-300';
  };

  // Formatar timestamp
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch {
      return timestamp;
    }
  };

  return (
    <div className={`bg-gray-800 border-t border-gray-600 ${className}`}>
      {/* Header dos logs */}
      <div className="bg-gray-700 border-b border-gray-600 px-4 py-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-white">
            📄 Logs do Container
          </h3>
          
          <div className="flex items-center gap-3">
            {/* Busca nos logs */}
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar nos logs..."
                className="
                  w-48 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs
                  placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500
                "
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <span className="text-xs">✕</span>
                </button>
              )}
            </div>

            {/* Controles */}
            <div className="flex items-center gap-2">
              {/* Tail */}
              <select
                value={tail}
                onChange={(e) => setTail(Number(e.target.value))}
                className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
              >
                <option value={50}>50 linhas</option>
                <option value={100}>100 linhas</option>
                <option value={500}>500 linhas</option>
                <option value={1000}>1000 linhas</option>
              </select>

              {/* Timestamps */}
              <label className="flex items-center gap-1 text-xs text-gray-300">
                <input
                  type="checkbox"
                  checked={timestamps}
                  onChange={(e) => setTimestamps(e.target.checked)}
                  className="rounded"
                />
                Timestamps
              </label>

              {/* Follow */}
              <label className="flex items-center gap-1 text-xs text-gray-300">
                <input
                  type="checkbox"
                  checked={follow}
                  onChange={(e) => setFollow(e.target.checked)}
                  className="rounded"
                />
                Follow
              </label>

              {/* Auto-scroll */}
              <label className="flex items-center gap-1 text-xs text-gray-300">
                <input
                  type="checkbox"
                  checked={autoScroll}
                  onChange={(e) => setAutoScroll(e.target.checked)}
                  className="rounded"
                />
                Auto-scroll
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Área dos logs */}
      <div 
        ref={logsContainerRef}
        onScroll={handleScroll}
        className="h-48 overflow-y-auto bg-black font-mono text-xs leading-relaxed p-4"
      >
        {isLoading && (
          <div className="text-gray-500 text-center py-8">
            Carregando logs...
          </div>
        )}

        {error && (
          <div className="text-red-400 text-center py-8">
            Erro ao carregar logs: {error.message}
          </div>
        )}

        {!isLoading && !error && filteredLogs.length === 0 && (
          <div className="text-gray-500 text-center py-8">
            {searchTerm ? 'Nenhum log encontrado para a busca.' : 'Nenhum log disponível.'}
          </div>
        )}

        {!isLoading && !error && filteredLogs.map((log, index) => (
          <div key={index} className="mb-1 flex">
            {timestamps && (
              <span className="text-gray-600 mr-3 select-none">
                {formatTimestamp(log.timestamp)}
              </span>
            )}
            <span className={`${getLogColor(log)} break-all`}>
              {log.message}
            </span>
          </div>
        ))}

        <div ref={logsEndRef} />
      </div>

      {/* Footer com informações */}
      <div className="bg-gray-700 border-t border-gray-600 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div>
            {filteredLogs.length > 0 && (
              <span>
                {filteredLogs.length} linha{filteredLogs.length !== 1 ? 's' : ''}
                {searchTerm && ` (filtrado de ${logs?.length || 0})`}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            {follow && (
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                Acompanhando
              </span>
            )}
            
            <button
              onClick={() => {
                if (logsEndRef.current) {
                  logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              className="text-gray-400 hover:text-white"
            >
              ⬇️ Ir para o final
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
