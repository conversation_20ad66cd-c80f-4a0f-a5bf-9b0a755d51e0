/**
 * Loading Spinner - Componente de carregamento
 * Auto-Instalador V3 Lite
 * 
 * @description Spinner de carregamento reutilizável
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'blue' | 'white' | 'gray';
  className?: string;
  text?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'blue',
  className = '',
  text
}) => {
  // Definir tamanhos
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  // Definir cores
  const colorClasses = {
    blue: 'border-blue-500',
    white: 'border-white',
    gray: 'border-gray-400'
  };

  const spinnerClass = `
    ${sizeClasses[size]}
    ${colorClasses[color]}
    border-2 border-t-transparent rounded-full animate-spin
    ${className}
  `;

  if (text) {
    return (
      <div className="flex flex-col items-center gap-3">
        <div className={spinnerClass} />
        <span className="text-sm text-gray-400">{text}</span>
      </div>
    );
  }

  return <div className={spinnerClass} />;
};
