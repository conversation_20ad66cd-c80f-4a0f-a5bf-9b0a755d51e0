# Electron 37.1.2 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 37.1.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.electronjs.org/
- **GitHub:** https://github.com/electron/electron
- **Documentação:** https://www.electronjs.org/docs/latest/
- **NPM/Package:** https://www.npmjs.com/package/electron
- **Fórum/Community:** https://github.com/electron/electron/discussions
- **Stack Overflow Tag:** `electron`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Main Process Completo**

```typescript
// src/electron/main/main.ts
import { app, BrowserWindow, ipcMain, Menu, Tray, dialog } from 'electron';
import { autoUpdater } from 'electron-updater';
import path from 'path';
import { spawn, ChildProcess } from 'child_process';

class AutoInstaladorMain {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private backendProcess: ChildProcess | null = null;

  constructor() {
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    // Configure app for i5 12th Gen
    this.configurePerformance();
    
    await app.whenReady();
    
    // Start backend service
    await this.startBackendService();
    
    // Create main window
    this.createMainWindow();
    
    // Setup system tray
    this.createSystemTray();
    
    // Register IPC handlers
    this.registerIPCHandlers();
    
    // Setup auto updater
    this.setupAutoUpdater();
    
    // Setup app events
    this.setupAppEvents();
  }

  private configurePerformance(): void {
    // Optimizations for i5 12th Gen + 32GB RAM
    app.commandLine.appendSwitch('--max-old-space-size', '4096');
    app.commandLine.appendSwitch('--max-semi-space-size', '512');
    app.commandLine.appendSwitch('--renderer-process-limit', '8');
    app.commandLine.appendSwitch('--enable-gpu-rasterization');
    app.commandLine.appendSwitch('--enable-features', 
      'VaapiVideoDecoder,ThreadedScrolling,ThreadedCompositing');
  }

  private async startBackendService(): Promise<void> {
    const backendPath = path.join(__dirname, '../backend/AutoInstaladorLite.exe');
    
    this.backendProcess = spawn(backendPath, ['--urls=http://localhost:5000'], {
      stdio: 'pipe',
      env: { ...process.env, ASPNETCORE_ENVIRONMENT: 'Production' }
    });

    // Wait for backend to be ready
    return new Promise((resolve) => {
      const checkBackend = setInterval(async () => {
        try {
          const response = await fetch('http://localhost:5000/health');
          if (response.ok) {
            clearInterval(checkBackend);
            resolve();
          }
        } catch (error) {
          // Backend not ready yet
        }
      }, 1000);
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        preload: path.join(__dirname, 'preload.js'),
        backgroundThrottling: false
      },
      titleBarStyle: 'default',
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false
    });

    // Load React app
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    // Handle window close
    this.mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
  }

  private createSystemTray(): void {
    const iconPath = path.join(__dirname, '../assets/tray-icon.png');
    this.tray = new Tray(iconPath);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Mostrar Auto-Instalador',
        click: () => this.showWindow()
      },
      { type: 'separator' },
      {
        label: 'Dashboard',
        click: () => {
          this.showWindow();
          this.navigateTo('/');
        }
      },
      {
        label: 'Containers',
        click: () => {
          this.showWindow();
          this.navigateTo('/containers');
        }
      },
      { type: 'separator' },
      {
        label: 'Sair',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('Auto-Instalador V3 Lite');
    
    this.tray.on('click', () => {
      this.toggleWindow();
    });
  }

  private registerIPCHandlers(): void {
    // Container management
    ipcMain.handle('containers:list', async () => {
      try {
        const response = await fetch('http://localhost:5000/api/containers');
        return await response.json();
      } catch (error) {
        throw new Error(`Failed to list containers: ${error.message}`);
      }
    });

    ipcMain.handle('containers:start', async (_, containerId: string) => {
      try {
        const response = await fetch(`http://localhost:5000/api/containers/${containerId}/start`, {
          method: 'POST'
        });
        return await response.json();
      } catch (error) {
        throw new Error(`Failed to start container: ${error.message}`);
      }
    });

    // File system operations
    ipcMain.handle('fs:select-folder', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory'],
        title: 'Selecionar Pasta'
      });
      
      return result.canceled ? null : result.filePaths[0];
    });

    ipcMain.handle('fs:save-file', async (_, content: string, filename: string) => {
      const result = await dialog.showSaveDialog(this.mainWindow!, {
        defaultPath: filename,
        title: 'Salvar Arquivo'
      });
      
      if (!result.canceled && result.filePath) {
        const fs = require('fs').promises;
        await fs.writeFile(result.filePath, content, 'utf8');
        return result.filePath;
      }
      
      return null;
    });

    // Notifications
    ipcMain.handle('notifications:show', async (_, title: string, body: string) => {
      const { Notification } = require('electron');
      const notification = new Notification({
        title,
        body,
        icon: path.join(__dirname, '../assets/icon.png')
      });
      
      notification.show();
      return true;
    });
  }

  private setupAutoUpdater(): void {
    autoUpdater.setFeedURL({
      provider: 'github',
      owner: 'auto-instalador-v3',
      repo: 'desktop-app'
    });

    autoUpdater.on('update-available', (info) => {
      this.mainWindow?.webContents.send('update-available', info);
    });

    autoUpdater.on('update-downloaded', () => {
      this.mainWindow?.webContents.send('update-downloaded');
    });

    // Check for updates every 4 hours
    setInterval(() => {
      autoUpdater.checkForUpdatesAndNotify();
    }, 4 * 60 * 60 * 1000);
  }

  private setupAppEvents(): void {
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow();
      }
    });

    app.on('before-quit', () => {
      app.isQuiting = true;
      
      // Cleanup backend process
      if (this.backendProcess) {
        this.backendProcess.kill();
      }
    });
  }

  private showWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  private toggleWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isVisible()) {
        this.mainWindow.hide();
      } else {
        this.showWindow();
      }
    }
  }

  private navigateTo(route: string): void {
    this.mainWindow?.webContents.send('navigate', route);
  }
}

// Initialize app
new AutoInstaladorMain();
```

### **2. Preload Script Seguro**

```typescript
// src/electron/preload/preload.ts
import { contextBridge, ipcRenderer } from 'electron';

// Define allowed channels for security
const ALLOWED_CHANNELS = [
  'containers:list',
  'containers:start',
  'containers:stop',
  'containers:remove',
  'fs:select-folder',
  'fs:save-file',
  'notifications:show',
  'update-available',
  'update-downloaded',
  'navigate'
];

// Validate channel
const validateChannel = (channel: string): boolean => {
  return ALLOWED_CHANNELS.includes(channel);
};

// Safe API exposed to renderer
const electronAPI = {
  // System info
  platform: process.platform,
  version: process.env.npm_package_version,

  // IPC communication
  invoke: async (channel: string, ...args: any[]) => {
    if (!validateChannel(channel)) {
      throw new Error(`Channel ${channel} not allowed`);
    }
    return await ipcRenderer.invoke(channel, ...args);
  },

  on: (channel: string, callback: (...args: any[]) => void) => {
    if (!validateChannel(channel)) {
      throw new Error(`Channel ${channel} not allowed`);
    }
    
    const subscription = (_event: any, ...args: any[]) => callback(...args);
    ipcRenderer.on(channel, subscription);
    
    return () => {
      ipcRenderer.removeListener(channel, subscription);
    };
  },

  removeAllListeners: (channel: string) => {
    if (validateChannel(channel)) {
      ipcRenderer.removeAllListeners(channel);
    }
  },

  // Specific APIs for Auto-Instalador
  containers: {
    list: () => ipcRenderer.invoke('containers:list'),
    start: (id: string) => ipcRenderer.invoke('containers:start', id),
    stop: (id: string) => ipcRenderer.invoke('containers:stop', id),
    remove: (id: string) => ipcRenderer.invoke('containers:remove', id)
  },

  filesystem: {
    selectFolder: () => ipcRenderer.invoke('fs:select-folder'),
    saveFile: (content: string, filename: string) => 
      ipcRenderer.invoke('fs:save-file', content, filename)
  },

  notifications: {
    show: (title: string, body: string) => 
      ipcRenderer.invoke('notifications:show', title, body)
  }
};

// Expose API to renderer
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// TypeScript definitions
export type ElectronAPI = typeof electronAPI;

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
```

### **3. React Integration**

```typescript
// src/renderer/hooks/useElectronAPI.ts
import { useEffect, useState } from 'react';

export function useElectronAPI() {
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    setIsElectron(typeof window !== 'undefined' && !!window.electronAPI);
  }, []);

  return {
    isElectron,
    api: isElectron ? window.electronAPI : null
  };
}

// src/renderer/services/containerService.ts
export class ContainerService {
  static async listContainers() {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      return await window.electronAPI.containers.list();
    } catch (error) {
      console.error('Failed to list containers:', error);
      throw error;
    }
  }

  static async startContainer(id: string) {
    if (!window.electronAPI) {
      throw new Error('Electron API not available');
    }
    
    try {
      const result = await window.electronAPI.containers.start(id);
      
      // Show success notification
      await window.electronAPI.notifications.show(
        'Container Iniciado',
        `Container ${id} foi iniciado com sucesso`
      );
      
      return result;
    } catch (error) {
      // Show error notification
      await window.electronAPI.notifications.show(
        'Erro',
        `Falha ao iniciar container: ${error.message}`
      );
      throw error;
    }
  }
}

// src/renderer/components/ContainerList.tsx
import React, { useEffect, useState } from 'react';
import { ContainerService } from '../services/containerService';

interface Container {
  id: string;
  name: string;
  status: string;
  image: string;
}

export function ContainerList() {
  const [containers, setContainers] = useState<Container[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContainers();
  }, []);

  const loadContainers = async () => {
    try {
      setLoading(true);
      const data = await ContainerService.listContainers();
      setContainers(data);
    } catch (error) {
      console.error('Failed to load containers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartContainer = async (id: string) => {
    try {
      await ContainerService.startContainer(id);
      await loadContainers(); // Refresh list
    } catch (error) {
      console.error('Failed to start container:', error);
    }
  };

  if (loading) {
    return <div className="flex justify-center p-4">Carregando containers...</div>;
  }

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Containers</h2>
      <div className="grid gap-4">
        {containers.map((container) => (
          <div key={container.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold">{container.name}</h3>
                <p className="text-sm text-gray-600">{container.image}</p>
                <span className={`inline-block px-2 py-1 rounded text-xs ${
                  container.status === 'running' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {container.status}
                </span>
              </div>
              <button
                onClick={() => handleStartContainer(container.id)}
                disabled={container.status === 'running'}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
              >
                {container.status === 'running' ? 'Executando' : 'Iniciar'}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### **4. File System Integration**

```typescript
// src/renderer/components/FileManager.tsx
import React, { useState } from 'react';

export function FileManager() {
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);

  const handleSelectFolder = async () => {
    try {
      const folder = await window.electronAPI.filesystem.selectFolder();
      if (folder) {
        setSelectedFolder(folder);
      }
    } catch (error) {
      console.error('Failed to select folder:', error);
    }
  };

  const handleExportConfig = async () => {
    try {
      const config = {
        containers: await window.electronAPI.containers.list(),
        timestamp: new Date().toISOString()
      };

      const configJson = JSON.stringify(config, null, 2);
      const filename = `auto-instalador-config-${Date.now()}.json`;
      
      const savedPath = await window.electronAPI.filesystem.saveFile(configJson, filename);
      
      if (savedPath) {
        await window.electronAPI.notifications.show(
          'Configuração Exportada',
          `Arquivo salvo em: ${savedPath}`
        );
      }
    } catch (error) {
      console.error('Failed to export config:', error);
    }
  };

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Gerenciador de Arquivos</h2>
      
      <div className="space-y-4">
        <div>
          <button
            onClick={handleSelectFolder}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Selecionar Pasta
          </button>
          {selectedFolder && (
            <p className="mt-2 text-sm text-gray-600">
              Pasta selecionada: {selectedFolder}
            </p>
          )}
        </div>

        <div>
          <button
            onClick={handleExportConfig}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Exportar Configuração
          </button>
        </div>
      </div>
    </div>
  );
}
```

### **5. Auto-Updater Integration**

```typescript
// src/renderer/components/UpdateManager.tsx
import React, { useEffect, useState } from 'react';

interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseNotes: string;
}

export function UpdateManager() {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [updateDownloaded, setUpdateDownloaded] = useState(false);

  useEffect(() => {
    // Listen for update events
    const removeUpdateAvailable = window.electronAPI.on('update-available', (info: UpdateInfo) => {
      setUpdateAvailable(true);
      setUpdateInfo(info);
    });

    const removeUpdateDownloaded = window.electronAPI.on('update-downloaded', () => {
      setUpdateDownloaded(true);
    });

    return () => {
      removeUpdateAvailable();
      removeUpdateDownloaded();
    };
  }, []);

  const handleInstallUpdate = () => {
    // The main process will handle the installation
    window.electronAPI.invoke('update:install');
  };

  if (!updateAvailable) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 bg-blue-500 text-white p-4 rounded-lg shadow-lg max-w-sm">
      <h3 className="font-bold mb-2">Atualização Disponível</h3>
      {updateInfo && (
        <div className="mb-3">
          <p className="text-sm">Versão: {updateInfo.version}</p>
          <p className="text-sm">Data: {new Date(updateInfo.releaseDate).toLocaleDateString()}</p>
        </div>
      )}
      
      {updateDownloaded ? (
        <button
          onClick={handleInstallUpdate}
          className="w-full px-4 py-2 bg-white text-blue-500 rounded hover:bg-gray-100"
        >
          Instalar e Reiniciar
        </button>
      ) : (
        <p className="text-sm">Baixando atualização...</p>
      )}
    </div>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Electron 37.1.2 Examples
