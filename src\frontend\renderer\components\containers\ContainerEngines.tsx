/**
 * Container Engines - Configuração de engines de container
 * Auto-Instalador V3 Lite
 * 
 * @description Página para configurar e gerenciar engines Docker/Podman
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { 
  useContainerEngines, 
  useInstallContainerEngine,
  useContainerEngineStatus 
} from '../../services/container-service';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { ErrorMessage } from '../common/ErrorMessage';
import { toast } from 'react-hot-toast';
import type { ContainerEngine, ContainerEngineInfo } from '../../../../shared/types/api.types';

interface ContainerEnginesProps {
  className?: string;
}

export const ContainerEngines: React.FC<ContainerEnginesProps> = ({ className = '' }) => {
  const [showInstallModal, setShowInstallModal] = useState(false);
  const [selectedEngineToInstall, setSelectedEngineToInstall] = useState<ContainerEngine>('docker');

  // Hooks de dados
  const { 
    data: engines, 
    isLoading: enginesLoading, 
    error: enginesError,
    refetch: refetchEngines
  } = useContainerEngines();

  const installEngine = useInstallContainerEngine();

  // Detectar plataforma
  const platform = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent;
      if (userAgent.includes('Win')) return 'windows';
      if (userAgent.includes('Mac')) return 'macos';
      return 'linux';
    }
    return 'linux';
  }, []);

  // Handler para instalação
  const handleInstallEngine = async () => {
    try {
      await installEngine.mutateAsync({
        engine: selectedEngineToInstall,
        platform,
        force: false
      });
      
      toast.success(`${selectedEngineToInstall} instalado com sucesso`);
      setShowInstallModal(false);
      refetchEngines();
    } catch (error) {
      toast.error(`Erro ao instalar ${selectedEngineToInstall}: ${error}`);
    }
  };

  // Renderizar card de engine
  const renderEngineCard = (engineInfo: ContainerEngineInfo) => {
    const isDocker = engineInfo.engine === 'docker';
    const icon = isDocker ? '🐳' : '🦭';
    const name = isDocker ? 'Docker' : 'Podman';
    
    return (
      <div 
        key={engineInfo.engine}
        className="bg-gray-700 border border-gray-600 rounded-lg p-6 hover:border-blue-500 transition-colors"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <span className="text-3xl">{icon}</span>
            <div>
              <h3 className="text-lg font-semibold text-white">{name}</h3>
              <p className="text-sm text-gray-400">Container Engine</p>
            </div>
          </div>
          
          {/* Status */}
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${
              engineInfo.isRunning ? 'bg-green-500' : 
              engineInfo.isAvailable ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
            <span className={`text-sm font-medium ${
              engineInfo.isRunning ? 'text-green-400' : 
              engineInfo.isAvailable ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {engineInfo.isRunning ? 'Ativo' : 
               engineInfo.isAvailable ? 'Instalado' : 'Não Instalado'}
            </span>
          </div>
        </div>

        {/* Informações */}
        <div className="space-y-3 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Versão:</span>
            <span className="text-white font-mono">
              {engineInfo.version || 'N/A'}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">API Version:</span>
            <span className="text-white font-mono">
              {engineInfo.apiVersion || 'N/A'}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Disponível:</span>
            <span className={engineInfo.isAvailable ? 'text-green-400' : 'text-red-400'}>
              {engineInfo.isAvailable ? 'Sim' : 'Não'}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Executando:</span>
            <span className={engineInfo.isRunning ? 'text-green-400' : 'text-red-400'}>
              {engineInfo.isRunning ? 'Sim' : 'Não'}
            </span>
          </div>
          
          {engineInfo.rootless && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-400">Modo:</span>
              <span className="text-blue-400">Rootless</span>
            </div>
          )}
        </div>

        {/* Ações */}
        <div className="flex gap-2">
          {!engineInfo.isAvailable && (
            <button
              type="button"
              onClick={() => {
                setSelectedEngineToInstall(engineInfo.engine);
                setShowInstallModal(true);
              }}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              📥 Instalar
            </button>
          )}
          
          {engineInfo.isAvailable && !engineInfo.isRunning && (
            <button
              type="button"
              onClick={() => {
                // TODO: Implementar start do engine
                toast.info('Funcionalidade de start em desenvolvimento');
              }}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"
            >
              ▶️ Iniciar
            </button>
          )}
          
          {engineInfo.isRunning && (
            <button
              type="button"
              onClick={() => {
                // TODO: Implementar stop do engine
                toast.info('Funcionalidade de stop em desenvolvimento');
              }}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
            >
              ⏹️ Parar
            </button>
          )}
          
          <button
            type="button"
            onClick={() => {
              // TODO: Implementar configurações do engine
              toast.info('Configurações em desenvolvimento');
            }}
            className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-600 rounded-md hover:bg-gray-500"
          >
            ⚙️
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full bg-gray-800 ${className}`}>
      {/* Header */}
      <div className="bg-gray-700 border-b border-gray-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-white">
            ⚙️ Container Engines
          </h2>
          
          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-400">
              Plataforma: <span className="text-white capitalize">{platform}</span>
            </div>
            
            <button
              type="button"
              onClick={refetchEngines}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              🔄 Atualizar
            </button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 overflow-y-auto p-6">
        {enginesLoading && (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" text="Carregando engines..." />
          </div>
        )}

        {enginesError && (
          <ErrorMessage 
            message="Erro ao carregar engines"
            details={enginesError.message}
            onRetry={refetchEngines}
          />
        )}

        {!enginesLoading && !enginesError && engines && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {engines.map(renderEngineCard)}
          </div>
        )}

        {!enginesLoading && !enginesError && (!engines || engines.length === 0) && (
          <div className="flex flex-col items-center justify-center py-16 text-gray-400">
            <div className="text-6xl mb-4">⚙️</div>
            <h3 className="text-lg font-medium mb-2">Nenhum engine detectado</h3>
            <p className="text-sm text-center max-w-md">
              Instale Docker ou Podman para começar a gerenciar containers.
            </p>
          </div>
        )}
      </div>

      {/* Modal de instalação */}
      {showInstallModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 border border-gray-600">
            <h3 className="text-lg font-semibold text-white mb-4">
              📥 Instalar {selectedEngineToInstall === 'docker' ? 'Docker' : 'Podman'}
            </h3>
            
            <div className="mb-6">
              <p className="text-sm text-gray-300 mb-4">
                Deseja instalar o {selectedEngineToInstall === 'docker' ? 'Docker' : 'Podman'} 
                automaticamente neste sistema?
              </p>
              
              <div className="bg-gray-700 rounded-md p-3 text-xs text-gray-400">
                <p><strong>Plataforma:</strong> {platform}</p>
                <p><strong>Engine:</strong> {selectedEngineToInstall}</p>
                <p><strong>Método:</strong> Package manager padrão</p>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={() => setShowInstallModal(false)}
                className="px-4 py-2 text-sm text-gray-300 hover:text-white"
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={handleInstallEngine}
                disabled={installEngine.isLoading}
                className="
                  px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                  hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                  flex items-center gap-2
                "
              >
                {installEngine.isLoading && <LoadingSpinner size="sm" />}
                Instalar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
