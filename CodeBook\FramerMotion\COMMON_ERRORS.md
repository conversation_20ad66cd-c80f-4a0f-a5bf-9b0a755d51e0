# Framer Motion 11.5.4 - <PERSON><PERSON><PERSON> Comuns e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 11.5.4  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.framer.com/motion/
- **GitHub:** https://github.com/framer/motion
- **Documentação:** https://www.framer.com/motion/introduction/
- **NPM/Package:** https://www.npmjs.com/package/framer-motion
- **Fórum/Community:** https://github.com/framer/motion/discussions
- **Stack Overflow Tag:** `framer-motion`

---

## 🚨 **ERROS CRÍTICOS FRAMER MOTION 11.5**

### **1. AnimatePresence Not Working**

#### **Erro:**
```
Exit animations not playing
Components disappearing instantly
```

#### **Causa:**
AnimatePresence não configurado corretamente ou key prop ausente.

#### **Solução:**
```typescript
// ❌ ERRADO - Sem AnimatePresence
function WrongExample({ isVisible }: { isVisible: boolean }) {
  return (
    <>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }} // Exit não funciona sem AnimatePresence
        >
          Content
        </motion.div>
      )}
    </>
  );
}

// ✅ CORRETO - Com AnimatePresence e key
import { AnimatePresence, motion } from 'framer-motion';

function CorrectExample({ isVisible }: { isVisible: boolean }) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          key="modal" // Key é obrigatória
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          Content
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// ✅ MELHOR - Com mode para múltiplos elementos
function BestExample({ items }: { items: any[] }) {
  return (
    <AnimatePresence mode="popLayout">
      {items.map(item => (
        <motion.div
          key={item.id} // Key única para cada item
          layout
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
        >
          {item.content}
        </motion.div>
      ))}
    </AnimatePresence>
  );
}
```

---

### **2. Layout Animations Flickering**

#### **Erro:**
```
Layout animations causing flicker
Elements jumping during animation
```

#### **Causa:**
CSS conflitos ou layout prop usado incorretamente.

#### **Solução:**
```typescript
// ❌ ERRADO - CSS conflitos
function FlickeringExample() {
  return (
    <motion.div
      layout
      className="transition-all duration-300" // Conflito com Framer Motion
      style={{ width: isExpanded ? 300 : 100 }}
    >
      Content
    </motion.div>
  );
}

// ✅ CORRETO - Sem CSS transitions conflitantes
function SmoothExample() {
  return (
    <motion.div
      layout
      animate={{ width: isExpanded ? 300 : 100 }}
      transition={{ type: "spring", stiffness: 400, damping: 30 }}
    >
      Content
    </motion.div>
  );
}

// ✅ MELHOR - Com LayoutGroup para elementos relacionados
import { LayoutGroup } from 'framer-motion';

function OptimizedLayout() {
  return (
    <LayoutGroup>
      <motion.div layout className="container">
        <motion.h2 layout>Title</motion.h2>
        <motion.p layout>Content that changes</motion.p>
        <motion.button layout>Action</motion.button>
      </motion.div>
    </LayoutGroup>
  );
}
```

---

### **3. Performance Issues**

#### **Erro:**
```
Animations stuttering
High CPU usage during animations
Memory leaks with many animations
```

#### **Causa:**
Muitas animações simultâneas ou propriedades não otimizadas.

#### **Solução:**
```typescript
// ❌ ERRADO - Animando propriedades não otimizadas
function SlowAnimation() {
  return (
    <motion.div
      animate={{
        width: 300,        // Causa reflow
        height: 200,       // Causa reflow
        backgroundColor: '#ff0000', // Não otimizado
        left: 100          // Não otimizado
      }}
    >
      Content
    </motion.div>
  );
}

// ✅ CORRETO - Usando propriedades otimizadas
function FastAnimation() {
  return (
    <motion.div
      animate={{
        x: 100,           // Otimizado (transform)
        y: 50,            // Otimizado (transform)
        scale: 1.2,       // Otimizado (transform)
        rotate: 45,       // Otimizado (transform)
        opacity: 0.8      // Otimizado
      }}
      transition={{ duration: 0.3 }}
    >
      Content
    </motion.div>
  );
}

// ✅ MELHOR - Com useReducedMotion para acessibilidade
import { useReducedMotion } from 'framer-motion';

function AccessibleAnimation() {
  const shouldReduceMotion = useReducedMotion();
  
  return (
    <motion.div
      animate={{
        x: shouldReduceMotion ? 0 : 100,
        transition: { 
          duration: shouldReduceMotion ? 0 : 0.3 
        }
      }}
    >
      Content
    </motion.div>
  );
}

// Performance monitoring
function PerformanceOptimized() {
  const controls = useAnimation();
  
  // Limitar animações simultâneas
  const [animationQueue, setAnimationQueue] = useState<string[]>([]);
  
  const runAnimation = useCallback(async (animationId: string) => {
    if (animationQueue.length > 5) return; // Máximo 5 animações
    
    setAnimationQueue(prev => [...prev, animationId]);
    
    try {
      await controls.start({ x: 100 });
    } finally {
      setAnimationQueue(prev => prev.filter(id => id !== animationId));
    }
  }, [controls, animationQueue.length]);
  
  return (
    <motion.div animate={controls}>
      Content
    </motion.div>
  );
}
```

---

### **4. React 19.2 Compatibility Issues**

#### **Erro:**
```
Hooks not working with React 19.2
useActionState conflicts with animations
```

#### **Causa:**
Conflitos entre React 19.2 hooks e Framer Motion.

#### **Solução:**
```typescript
// ❌ ERRADO - Conflito entre hooks
import { useActionState } from 'react';
import { useAnimation } from 'framer-motion';

function ConflictingHooks() {
  const [state, formAction] = useActionState(async () => {}, {});
  const controls = useAnimation(); // Pode conflitar
  
  // Ambos tentando controlar o mesmo elemento
  return (
    <motion.form action={formAction} animate={controls}>
      <button type="submit">Submit</button>
    </motion.form>
  );
}

// ✅ CORRETO - Separação de responsabilidades
function CompatibleHooks() {
  const [state, formAction, isPending] = useActionState(
    async (prev: any, formData: FormData) => {
      // Form logic
      return { success: true };
    }, 
    { success: false }
  );
  
  return (
    <form action={formAction}>
      <motion.button
        type="submit"
        disabled={isPending}
        animate={{ 
          scale: isPending ? 0.95 : 1,
          opacity: isPending ? 0.7 : 1
        }}
        whileHover={{ scale: isPending ? 0.95 : 1.05 }}
        transition={{ duration: 0.2 }}
      >
        {isPending ? 'Enviando...' : 'Enviar'}
      </motion.button>
      
      <AnimatePresence>
        {state.success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            Sucesso!
          </motion.div>
        )}
      </AnimatePresence>
    </form>
  );
}
```

---

### **5. TypeScript Errors**

#### **Erro:**
```
Type errors with motion components
Variant types not recognized
```

#### **Causa:**
Tipos TypeScript não configurados corretamente.

#### **Solução:**
```typescript
// ❌ ERRADO - Tipos não definidos
function TypeErrorExample() {
  const variants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };
  
  return (
    <motion.div
      variants={variants}
      initial="hidden" // TypeScript pode não reconhecer
      animate="visible"
    >
      Content
    </motion.div>
  );
}

// ✅ CORRETO - Tipos explícitos
import { Variants, motion } from 'framer-motion';

const containerVariants: Variants = {
  hidden: { 
    opacity: 0,
    y: 20
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      staggerChildren: 0.1
    }
  }
};

interface AnimatedContainerProps {
  children: React.ReactNode;
  className?: string;
}

function TypedExample({ children, className }: AnimatedContainerProps) {
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={className}
    >
      {children}
    </motion.div>
  );
}

// ✅ MELHOR - Tipos customizados
interface CustomMotionProps {
  isVisible: boolean;
  onAnimationComplete?: () => void;
}

const customVariants: Variants = {
  enter: (isVisible: boolean) => ({
    opacity: isVisible ? 1 : 0,
    scale: isVisible ? 1 : 0.8,
    transition: { duration: 0.3 }
  })
};

function CustomTypedComponent({ isVisible, onAnimationComplete }: CustomMotionProps) {
  return (
    <motion.div
      custom={isVisible}
      variants={customVariants}
      animate="enter"
      onAnimationComplete={onAnimationComplete}
    >
      Custom content
    </motion.div>
  );
}
```

---

### **6. Drag and Drop Issues**

#### **Erro:**
```
Drag not working properly
Constraints not respected
onDragEnd not firing
```

#### **Causa:**
Configuração incorreta de drag ou conflitos CSS.

#### **Solução:**
```typescript
// ❌ ERRADO - Configuração incompleta
function BrokenDrag() {
  return (
    <motion.div
      drag
      style={{ position: 'relative' }} // Pode causar problemas
    >
      Drag me
    </motion.div>
  );
}

// ✅ CORRETO - Configuração completa
function WorkingDrag() {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  
  return (
    <div style={{ position: 'relative', width: 400, height: 400 }}>
      <motion.div
        drag
        dragConstraints={{ left: 0, right: 300, top: 0, bottom: 300 }}
        dragElastic={0.1}
        dragMomentum={false}
        whileDrag={{ scale: 1.1, zIndex: 1000 }}
        onDragStart={() => console.log('Drag started')}
        onDragEnd={(event, info) => {
          console.log('Drag ended', info.point);
          setPosition({ x: info.point.x, y: info.point.y });
        }}
        style={{
          width: 100,
          height: 100,
          backgroundColor: '#3b82f6',
          borderRadius: 8,
          cursor: 'grab'
        }}
      >
        Drag me
      </motion.div>
    </div>
  );
}

// ✅ MELHOR - Com Reorder para listas
import { Reorder } from 'framer-motion';

function ReorderList({ items, onReorder }: {
  items: any[];
  onReorder: (newOrder: any[]) => void;
}) {
  return (
    <Reorder.Group axis="y" values={items} onReorder={onReorder}>
      {items.map((item) => (
        <Reorder.Item
          key={item.id}
          value={item}
          whileDrag={{ scale: 1.05, boxShadow: "0 5px 15px rgba(0,0,0,0.2)" }}
        >
          {item.content}
        </Reorder.Item>
      ))}
    </Reorder.Group>
  );
}
```

---

### **7. SSR/Hydration Issues**

#### **Erro:**
```
Hydration mismatch
Animations not working on server
```

#### **Causa:**
Framer Motion executando no servidor onde não há DOM.

#### **Solução:**
```typescript
// ❌ ERRADO - Sem verificação de cliente
function SSRProblem() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      Content
    </motion.div>
  );
}

// ✅ CORRETO - Com verificação de cliente
import { useEffect, useState } from 'react';

function SSRSafe() {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return <div>Content</div>; // Versão estática para SSR
  }
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      Content
    </motion.div>
  );
}

// ✅ MELHOR - Hook customizado para SSR
function useIsClient() {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  return isClient;
}

function OptimizedSSR() {
  const isClient = useIsClient();
  
  return (
    <motion.div
      initial={isClient ? { opacity: 0 } : false}
      animate={isClient ? { opacity: 1 } : false}
      transition={{ duration: 0.3 }}
    >
      Content
    </motion.div>
  );
}
```

---

### **8. Memory Leaks**

#### **Erro:**
```
Memory usage increasing over time
Animations not cleaning up
```

#### **Causa:**
Event listeners não removidos ou animações não canceladas.

#### **Solução:**
```typescript
// ❌ ERRADO - Sem cleanup
function MemoryLeak() {
  const controls = useAnimation();
  
  useEffect(() => {
    const interval = setInterval(() => {
      controls.start({ rotate: 360 });
    }, 1000);
    
    // Sem cleanup!
  }, [controls]);
  
  return <motion.div animate={controls}>Content</motion.div>;
}

// ✅ CORRETO - Com cleanup adequado
function ProperCleanup() {
  const controls = useAnimation();
  
  useEffect(() => {
    const interval = setInterval(() => {
      controls.start({ rotate: 360 });
    }, 1000);
    
    return () => {
      clearInterval(interval);
      controls.stop(); // Parar animações
    };
  }, [controls]);
  
  return <motion.div animate={controls}>Content</motion.div>;
}

// ✅ MELHOR - Hook customizado com cleanup
function useAnimationCleanup() {
  const controls = useAnimation();
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);
  
  const safeAnimate = useCallback((animation: any) => {
    return controls.start(animation);
  }, [controls]);
  
  const safeTimeout = useCallback((callback: () => void, delay: number) => {
    const timeout = setTimeout(callback, delay);
    timeoutsRef.current.push(timeout);
    return timeout;
  }, []);
  
  useEffect(() => {
    return () => {
      // Cleanup all timeouts
      timeoutsRef.current.forEach(clearTimeout);
      // Stop all animations
      controls.stop();
    };
  }, [controls]);
  
  return { controls, safeAnimate, safeTimeout };
}
```

---

## 🔧 **DEBUGGING TOOLS**

### **Animation Inspector**
```typescript
// Debug helper para animações
function AnimationDebugger({ children }: { children: React.ReactNode }) {
  return (
    <motion.div
      onAnimationStart={() => console.log('Animation started')}
      onAnimationComplete={() => console.log('Animation completed')}
      onUpdate={(latest) => console.log('Animation update:', latest)}
    >
      {children}
    </motion.div>
  );
}
```

### **Performance Monitor**
```typescript
// Monitor de performance
function PerformanceMonitor() {
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('framer-motion')) {
          console.log('Framer Motion performance:', entry);
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    return () => observer.disconnect();
  }, []);
  
  return null;
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Framer Motion 11.5.4 Common Errors
