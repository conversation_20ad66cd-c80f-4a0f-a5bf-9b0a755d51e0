# Vite 5.4.2 - <PERSON><PERSON><PERSON> Comuns e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.4.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://vitejs.dev/
- **GitHub:** https://github.com/vitejs/vite
- **Documentação:** https://vitejs.dev/guide/
- **NPM/Package:** https://www.npmjs.com/package/vite
- **Fórum/Community:** https://github.com/vitejs/vite/discussions
- **Stack Overflow Tag:** `vite`

---

## 🚨 **ERROS CRÍTICOS VITE 5.4.2**

### **1. Module Resolution Errors**

#### **Erro:**
```
Error: Failed to resolve import "@components/Button" from "src/App.tsx"
```

#### **Causa:**
Path aliases não configurados corretamente.

#### **Solução:**
```typescript
// vite.config.ts
import { resolve } from 'path';

export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils')
    }
  }
});

// tsconfig.json - Também configurar no TypeScript
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/utils/*"]
    }
  }
}
```

---

### **2. Electron Integration Issues**

#### **Erro:**
```
Error: require is not defined
Error: Cannot resolve module 'electron'
```

#### **Causa:**
Tentativa de usar módulos Node.js no renderer process.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      external: ['electron'] // Excluir electron do bundle
    }
  },
  
  define: {
    global: 'globalThis' // Polyfill para global
  },
  
  optimizeDeps: {
    exclude: ['electron'] // Não otimizar electron
  }
});

// Para usar APIs do Electron, usar preload script
// preload.ts
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args)
});

// renderer (React)
declare global {
  interface Window {
    electronAPI: any;
  }
}

// Usar no componente
const data = await window.electronAPI.invoke('get-data');
```

---

### **3. HMR Not Working**

#### **Erro:**
```
[vite] hmr update failed
[vite] hot reload not working
```

#### **Causa:**
Configuração incorreta do HMR ou conflitos de porta.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    hmr: {
      port: 3001, // Porta específica para HMR
      host: 'localhost'
    },
    
    // Para Electron, configurar CORS
    cors: {
      origin: ['http://localhost:3000', 'app://'],
      credentials: true
    }
  }
});

// Se ainda não funcionar, verificar:
// 1. Firewall bloqueando porta 3001
// 2. Proxy ou VPN interferindo
// 3. Extensões do browser bloqueando WebSocket

// Forçar reload manual se necessário
if (import.meta.hot) {
  import.meta.hot.accept(() => {
    window.location.reload();
  });
}
```

---

### **4. Build Failures**

#### **Erro:**
```
Error: Build failed with errors
Error: Rollup failed to resolve import
```

#### **Causa:**
Dependências não encontradas ou configuração incorreta.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      // Especificar externals explicitamente
      external: ['electron', 'fs', 'path', 'os'],
      
      output: {
        // Configurar globals para externals
        globals: {
          electron: 'electron'
        }
      }
    },
    
    // Configurar target correto
    target: 'esnext',
    
    // Configurar minificação
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false // Manter console em desenvolvimento
      }
    }
  },
  
  // Otimizar dependências
  optimizeDeps: {
    include: [
      'react',
      'react-dom'
    ],
    exclude: [
      'electron'
    ]
  }
});
```

---

### **5. CSS Import Issues**

#### **Erro:**
```
Error: Failed to parse CSS
Error: Unknown at-rule @tailwind
```

#### **Causa:**
PostCSS não configurado ou plugins ausentes.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  css: {
    postcss: {
      plugins: [
        require('tailwindcss'),
        require('autoprefixer')
      ]
    }
  }
});

// postcss.config.js (alternativa)
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {}
  }
};

// Para CSS Modules
export default defineConfig({
  css: {
    modules: {
      localsConvention: 'camelCaseOnly'
    }
  }
});
```

---

### **6. Environment Variables Not Working**

#### **Erro:**
```
Error: import.meta.env.VITE_API_URL is undefined
```

#### **Causa:**
Variáveis de ambiente não prefixadas com VITE_ ou arquivo .env não encontrado.

#### **Solução:**
```bash
# .env
VITE_API_URL=http://localhost:5000
VITE_APP_TITLE=Auto-Instalador V3 Lite

# Variáveis sem VITE_ não são expostas ao cliente
DATABASE_URL=postgresql://localhost:5432/db # Não acessível no frontend
```

```typescript
// Usar no código
const apiUrl = import.meta.env.VITE_API_URL;
const appTitle = import.meta.env.VITE_APP_TITLE;

// Tipos TypeScript
interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_APP_TITLE: string;
}

// vite.config.ts - Definir variáveis em build time
export default defineConfig({
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  }
});
```

---

### **7. Plugin Conflicts**

#### **Erro:**
```
Error: Plugin conflict detected
Error: Cannot read property of undefined
```

#### **Causa:**
Plugins incompatíveis ou ordem incorreta.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    // Ordem importa - React deve vir primeiro
    react({
      jsxRuntime: 'automatic'
    }),
    
    // Plugins condicionais
    ...(process.env.ANALYZE ? [
      visualizer({
        filename: 'dist/stats.html'
      })
    ] : []),
    
    // Plugin customizado com verificação
    {
      name: 'custom-plugin',
      configResolved(config) {
        // Verificar se outros plugins necessários estão presentes
        const hasReactPlugin = config.plugins.some(p => p.name === 'vite:react-babel');
        if (!hasReactPlugin) {
          throw new Error('React plugin is required');
        }
      }
    }
  ]
});
```

---

### **8. Performance Issues**

#### **Erro:**
```
Warning: Large chunk sizes detected
Build taking too long
```

#### **Causa:**
Bundle não otimizado ou dependências muito grandes.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Separar vendors grandes
          if (id.includes('node_modules')) {
            if (id.includes('react')) return 'react-vendor';
            if (id.includes('lodash')) return 'utils-vendor';
            return 'vendor';
          }
        }
      }
    },
    
    // Configurar limites
    chunkSizeWarningLimit: 1000,
    
    // Otimizar terser para i5 12ª Gen
    terserOptions: {
      parallel: true,
      maxWorkers: 4
    }
  },
  
  // Otimizar dependências
  optimizeDeps: {
    include: [
      'react',
      'react-dom'
    ]
  }
});
```

---

### **9. TypeScript Errors**

#### **Erro:**
```
Error: Cannot find module or its corresponding type declarations
Error: JSX element implicitly has type 'any'
```

#### **Causa:**
Configuração TypeScript incorreta ou tipos ausentes.

#### **Solução:**
```typescript
// vite-env.d.ts
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Para assets
declare module '*.svg' {
  const src: string;
  export default src;
}

declare module '*.png' {
  const src: string;
  export default src;
}

// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "jsx": "react-jsx",
    "strict": true,
    "types": ["vite/client"]
  },
  "include": ["src/**/*", "vite-env.d.ts"]
}
```

---

### **10. Asset Loading Issues**

#### **Erro:**
```
Error: Failed to load asset
Error: 404 Not Found for static assets
```

#### **Causa:**
Configuração incorreta de assets ou paths.

#### **Solução:**
```typescript
// vite.config.ts
export default defineConfig({
  base: './', // Para Electron
  publicDir: 'public', // Diretório de assets públicos
  
  build: {
    assetsInlineLimit: 4096, // Inline assets menores que 4KB
    assetsDir: 'assets' // Diretório de assets no build
  }
});

// Importar assets corretamente
import logoUrl from './assets/logo.png'; // URL do asset
import logoInline from './assets/logo.svg?inline'; // SVG inline
import logoRaw from './assets/data.txt?raw'; // Conteúdo raw

// Assets dinâmicos
const getAssetUrl = (name: string) => {
  return new URL(`./assets/${name}`, import.meta.url).href;
};

// Assets públicos (em public/)
const publicAsset = '/images/hero.jpg'; // Referência direta
```

---

## 🔧 **DEBUGGING TOOLS**

### **Vite Debug Mode**
```bash
# Executar com debug
DEBUG=vite:* npm run dev

# Debug específico
DEBUG=vite:deps npm run dev
DEBUG=vite:hmr npm run dev
```

### **Build Analysis**
```bash
# Analisar bundle
npm run build -- --mode analyze

# Verificar dependências
npx vite optimize --force
```

### **Performance Monitoring**
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      plugins: [
        {
          name: 'build-timer',
          buildStart() {
            console.time('Build Time');
          },
          buildEnd() {
            console.timeEnd('Build Time');
          }
        }
      ]
    }
  }
});
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Vite 5.4.2 Common Errors
