# TailwindCSS 4.0.0-beta.1 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 4.0.0-beta.1  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tailwindcss.com/
- **GitHub:** https://github.com/tailwindlabs/tailwindcss
- **Documentação:** https://tailwindcss.com/docs
- **NPM/Package:** https://www.npmjs.com/package/tailwindcss
- **Fórum/Community:** https://github.com/tailwindlabs/tailwindcss/discussions
- **Stack Overflow Tag:** `tailwind-css`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Dashboard Layout Completo**

```typescript
// src/components/layout/DashboardLayout.tsx
import React from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        
        <main className="flex-1 overflow-auto">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

// src/components/layout/Sidebar.tsx
import { Home, Container, Settings, Activity } from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Containers', href: '/containers', icon: Container },
  { name: 'Monitoramento', href: '/monitoring', icon: Activity },
  { name: 'Configurações', href: '/settings', icon: Settings },
];

export function Sidebar() {
  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-900">
          Auto-Instalador V3
        </h1>
        <p className="text-sm text-gray-600">Lite Desktop</p>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => (
          <a
            key={item.name}
            href={item.href}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors group"
          >
            <item.icon className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" />
            {item.name}
          </a>
        ))}
      </nav>
      
      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center">
          <div className="h-8 w-8 bg-gray-300 rounded-full"></div>
          <div className="ml-3">
            <p className="text-sm font-medium text-gray-700">Usuário</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### **2. Container Card Component**

```typescript
// src/components/containers/ContainerCard.tsx
import React from 'react';
import { Play, Square, Trash2, MoreVertical, Activity } from 'lucide-react';
import type { Container } from '@types/container';

interface ContainerCardProps {
  container: Container;
  onStart: (id: string) => void;
  onStop: (id: string) => void;
  onRemove: (id: string) => void;
}

export function ContainerCard({ container, onStart, onStop, onRemove }: ContainerCardProps) {
  const statusConfig = {
    running: {
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: '🟢',
      label: 'Executando'
    },
    stopped: {
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      icon: '⚫',
      label: 'Parado'
    },
    error: {
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: '🔴',
      label: 'Erro'
    }
  };

  const status = statusConfig[container.status] || statusConfig.stopped;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 group">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {container.name}
          </h3>
          <p className="text-sm text-gray-600 truncate">
            {container.image}
          </p>
          <p className="text-xs text-gray-500 font-mono mt-1">
            ID: {container.id.substring(0, 12)}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${status.color}`}>
            {status.icon} {status.label}
          </span>
          
          <button className="p-1 hover:bg-gray-100 rounded opacity-0 group-hover:opacity-100 transition-opacity">
            <MoreVertical className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Stats */}
      {container.status === 'running' && container.stats && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">CPU:</span>
              <span className="ml-2 font-mono">{container.stats.cpuUsage.toFixed(1)}%</span>
            </div>
            <div>
              <span className="text-gray-600">Memória:</span>
              <span className="ml-2 font-mono">{(container.stats.memoryUsage / 1024 / 1024).toFixed(0)}MB</span>
            </div>
          </div>
        </div>
      )}

      {/* Ports */}
      {container.ports && container.ports.length > 0 && (
        <div className="mb-4">
          <div className="text-xs text-gray-600 mb-2">Portas:</div>
          <div className="flex flex-wrap gap-1">
            {container.ports.map((port, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded font-mono"
              >
                {port.publicPort ? `${port.publicPort}:` : ''}{port.privatePort}/{port.type}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex space-x-2">
        {container.status === 'running' ? (
          <button
            onClick={() => onStop(container.id)}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm font-medium"
          >
            <Square className="w-4 h-4 mr-1" />
            Parar
          </button>
        ) : (
          <button
            onClick={() => onStart(container.id)}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium"
          >
            <Play className="w-4 h-4 mr-1" />
            Iniciar
          </button>
        )}
        
        <button className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium">
          <Activity className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => onRemove(container.id)}
          disabled={container.status === 'running'}
          className="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
```

### **3. Form Components**

```typescript
// src/components/forms/CreateContainerForm.tsx
import React from 'react';
import { useActionState } from 'react';

interface FormState {
  loading: boolean;
  error?: string;
  success?: boolean;
}

export function CreateContainerForm() {
  const [state, formAction, isPending] = useActionState<FormState>(
    async (previousState: FormState, formData: FormData): Promise<FormState> => {
      try {
        const containerData = {
          name: formData.get('name') as string,
          image: formData.get('image') as string,
          ports: formData.get('ports') as string,
          environment: formData.get('environment') as string
        };

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return { loading: false, success: true };
      } catch (error) {
        return { loading: false, error: 'Erro ao criar container' };
      }
    },
    { loading: false }
  );

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Criar Novo Container
      </h2>
      
      <form action={formAction} className="space-y-6">
        {/* Nome do Container */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Nome do Container *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            placeholder="ex: web-server"
          />
        </div>

        {/* Imagem Docker */}
        <div>
          <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-2">
            Imagem Docker *
          </label>
          <select
            id="image"
            name="image"
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors appearance-none bg-white"
          >
            <option value="">Selecione uma imagem</option>
            <option value="nginx:latest">nginx:latest</option>
            <option value="redis:alpine">redis:alpine</option>
            <option value="postgres:13">postgres:13</option>
            <option value="node:18-alpine">node:18-alpine</option>
          </select>
        </div>

        {/* Portas */}
        <div>
          <label htmlFor="ports" className="block text-sm font-medium text-gray-700 mb-2">
            Mapeamento de Portas
          </label>
          <input
            type="text"
            id="ports"
            name="ports"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
            placeholder="ex: 8080:80, 3000:3000"
          />
          <p className="text-sm text-gray-500 mt-1">
            Formato: porta_host:porta_container, separadas por vírgula
          </p>
        </div>

        {/* Variáveis de Ambiente */}
        <div>
          <label htmlFor="environment" className="block text-sm font-medium text-gray-700 mb-2">
            Variáveis de Ambiente
          </label>
          <textarea
            id="environment"
            name="environment"
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none"
            placeholder="NODE_ENV=production&#10;API_KEY=your-key&#10;DATABASE_URL=postgres://..."
          />
          <p className="text-sm text-gray-500 mt-1">
            Uma variável por linha no formato CHAVE=valor
          </p>
        </div>

        {/* Opções Avançadas */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Opções Avançadas
          </h3>
          
          <div className="space-y-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="autoStart"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">
                Iniciar automaticamente
              </span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                name="restartPolicy"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">
                Reiniciar automaticamente em caso de falha
              </span>
            </label>
          </div>
        </div>

        {/* Error/Success Messages */}
        {state.error && (
          <div className="rounded-md bg-red-50 p-4 border border-red-200">
            <div className="text-sm text-red-700">
              {state.error}
            </div>
          </div>
        )}

        {state.success && (
          <div className="rounded-md bg-green-50 p-4 border border-green-200">
            <div className="text-sm text-green-700">
              Container criado com sucesso!
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          
          <button
            type="submit"
            disabled={isPending}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Criando...
              </>
            ) : (
              'Criar Container'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
```

### **4. Dark Mode Implementation**

```typescript
// src/components/ui/DarkModeToggle.tsx
import React, { useState, useEffect } from 'react';
import { Moon, Sun } from 'lucide-react';

export function DarkModeToggle() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const isDarkMode = localStorage.getItem('darkMode') === 'true' ||
      (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches);
    
    setIsDark(isDarkMode);
    document.documentElement.classList.toggle('dark', isDarkMode);
  }, []);

  const toggleDarkMode = () => {
    const newDarkMode = !isDark;
    setIsDark(newDarkMode);
    localStorage.setItem('darkMode', newDarkMode.toString());
    document.documentElement.classList.toggle('dark', newDarkMode);
  };

  return (
    <button
      onClick={toggleDarkMode}
      className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors"
      aria-label="Toggle dark mode"
    >
      {isDark ? (
        <Sun className="w-5 h-5 text-yellow-500" />
      ) : (
        <Moon className="w-5 h-5 text-gray-600 dark:text-gray-300" />
      )}
    </button>
  );
}

// src/components/layout/Header.tsx
import React from 'react';
import { Bell, Search, User } from 'lucide-react';
import { DarkModeToggle } from '../ui/DarkModeToggle';

export function Header() {
  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center flex-1">
          <div className="max-w-lg w-full lg:max-w-xs">
            <label htmlFor="search" className="sr-only">Buscar</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="search"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Buscar containers..."
                type="search"
              />
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <DarkModeToggle />
          
          <button className="p-2 text-gray-400 hover:text-gray-500 dark:text-gray-300 dark:hover:text-gray-200">
            <Bell className="h-6 w-6" />
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Admin
            </span>
          </div>
        </div>
      </div>
    </header>
  );
}
```

### **5. Responsive Container Grid**

```typescript
// src/components/containers/ContainerGrid.tsx
import React from 'react';
import { ContainerCard } from './ContainerCard';
import type { Container } from '@types/container';

interface ContainerGridProps {
  containers: Container[];
  onStart: (id: string) => void;
  onStop: (id: string) => void;
  onRemove: (id: string) => void;
}

export function ContainerGrid({ containers, onStart, onStop, onRemove }: ContainerGridProps) {
  if (containers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-24 w-24 text-gray-400">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
          Nenhum container encontrado
        </h3>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Comece criando seu primeiro container.
        </p>
        <div className="mt-6">
          <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Criar Container
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {containers.map((container) => (
        <ContainerCard
          key={container.id}
          container={container}
          onStart={onStart}
          onStop={onStop}
          onRemove={onRemove}
        />
      ))}
    </div>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - TailwindCSS 4.0.0-beta.1 Examples
