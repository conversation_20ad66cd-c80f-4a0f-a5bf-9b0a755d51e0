/**
 * Container Images - Gerenciamento de imagens de containers
 * Auto-Instalador V3 Lite
 * 
 * @description Página para gerenciar imagens Docker/Podman
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { useContainerImages, usePullImage, useRemoveImage } from '../../services/container-service';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { ErrorMessage } from '../common/ErrorMessage';
import { formatBytes, formatDateTime } from '../../utils/format';
import { toast } from 'react-hot-toast';
import type { ContainerEngine, ContainerImage } from '../../../../shared/types/api.types';

interface ContainerImagesProps {
  className?: string;
}

export const ContainerImages: React.FC<ContainerImagesProps> = ({ className = '' }) => {
  // Estados locais
  const [selectedEngine, setSelectedEngine] = useState<ContainerEngine>('docker');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
  const [showPullModal, setShowPullModal] = useState(false);
  const [pullImageName, setPullImageName] = useState('');

  // Hooks de dados
  const { 
    data: images, 
    isLoading: imagesLoading, 
    error: imagesError,
    refetch: refetchImages
  } = useContainerImages({ engine: selectedEngine });

  const pullImage = usePullImage();
  const removeImage = useRemoveImage();

  // Filtrar imagens baseado na busca
  const filteredImages = React.useMemo(() => {
    if (!images || !searchTerm) return images || [];
    
    return images.filter(image => 
      image.repository.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.tag.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [images, searchTerm]);

  // Handlers
  const handleSelectImage = (imageId: string) => {
    const newSelected = new Set(selectedImages);
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId);
    } else {
      newSelected.add(imageId);
    }
    setSelectedImages(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedImages.size === filteredImages.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(filteredImages.map(img => img.id)));
    }
  };

  const handlePullImage = async () => {
    if (!pullImageName.trim()) {
      toast.error('Digite o nome da imagem');
      return;
    }

    try {
      await pullImage.mutateAsync({
        imageName: pullImageName,
        engine: selectedEngine
      });
      
      toast.success(`Imagem ${pullImageName} baixada com sucesso`);
      setPullImageName('');
      setShowPullModal(false);
      refetchImages();
    } catch (error) {
      toast.error(`Erro ao baixar imagem: ${error}`);
    }
  };

  const handleRemoveImage = async (imageId: string, force = false) => {
    try {
      await removeImage.mutateAsync({
        imageId,
        force,
        engine: selectedEngine
      });
      
      toast.success('Imagem removida com sucesso');
      refetchImages();
    } catch (error) {
      toast.error(`Erro ao remover imagem: ${error}`);
    }
  };

  const handleBulkRemove = async () => {
    if (selectedImages.size === 0) {
      toast.error('Selecione pelo menos uma imagem');
      return;
    }

    if (!confirm(`Tem certeza que deseja remover ${selectedImages.size} imagem(ns)?`)) {
      return;
    }

    try {
      const promises = Array.from(selectedImages).map(imageId =>
        removeImage.mutateAsync({
          imageId,
          force: false,
          engine: selectedEngine
        })
      );

      await Promise.all(promises);
      
      toast.success(`${selectedImages.size} imagem(ns) removida(s) com sucesso`);
      setSelectedImages(new Set());
      refetchImages();
    } catch (error) {
      toast.error(`Erro na remoção em lote: ${error}`);
    }
  };

  return (
    <div className={`flex flex-col h-full bg-gray-800 ${className}`}>
      {/* Header */}
      <div className="bg-gray-700 border-b border-gray-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-white">
            🖼️ Imagens de Container
          </h2>
          
          <div className="flex items-center gap-4">
            {/* Seletor de engine */}
            <div className="flex items-center gap-2 bg-gray-600 rounded-md p-1">
              <button
                type="button"
                onClick={() => setSelectedEngine('docker')}
                className={`
                  px-3 py-1 rounded text-xs font-medium transition-colors duration-200
                  ${selectedEngine === 'docker'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-500'
                  }
                `}
              >
                🐳 Docker
              </button>
              <button
                type="button"
                onClick={() => setSelectedEngine('podman')}
                className={`
                  px-3 py-1 rounded text-xs font-medium transition-colors duration-200
                  ${selectedEngine === 'podman'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-500'
                  }
                `}
              >
                🦭 Podman
              </button>
            </div>

            {/* Botão de pull */}
            <button
              type="button"
              onClick={() => setShowPullModal(true)}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
            >
              📥 Pull Image
            </button>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-gray-700 border-b border-gray-600 px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Busca */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400 text-sm">🔍</span>
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar imagens..."
              className="
                w-full pl-10 pr-4 py-2 bg-gray-600 border border-gray-500 rounded-md
                text-white placeholder-gray-400 text-sm
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              "
            />
          </div>

          {/* Ações em lote */}
          {selectedImages.size > 0 && (
            <div className="flex items-center gap-3">
              <span className="text-sm text-blue-400">
                {selectedImages.size} imagem(ns) selecionada(s)
              </span>
              <button
                type="button"
                onClick={handleBulkRemove}
                className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
              >
                🗑️ Remover
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 overflow-hidden">
        {imagesLoading && (
          <div className="flex items-center justify-center h-full">
            <LoadingSpinner size="lg" text="Carregando imagens..." />
          </div>
        )}

        {imagesError && (
          <div className="p-6">
            <ErrorMessage 
              message="Erro ao carregar imagens"
              details={imagesError.message}
              onRetry={refetchImages}
            />
          </div>
        )}

        {!imagesLoading && !imagesError && (
          <div className="h-full overflow-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-700 border-b border-gray-600 sticky top-0">
                <tr>
                  <th className="w-12 px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedImages.size === filteredImages.length && filteredImages.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </th>
                  <th className="px-4 py-3 text-left">Repository</th>
                  <th className="px-4 py-3 text-left">Tag</th>
                  <th className="px-4 py-3 text-left">ID</th>
                  <th className="px-4 py-3 text-left">Tamanho</th>
                  <th className="px-4 py-3 text-left">Criado</th>
                  <th className="px-4 py-3 text-left">Ações</th>
                </tr>
              </thead>
              <tbody>
                {filteredImages.map((image) => (
                  <tr 
                    key={image.id}
                    className="border-b border-gray-700 hover:bg-gray-700/50"
                  >
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedImages.has(image.id)}
                        onChange={() => handleSelectImage(image.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="px-4 py-3 font-medium text-white">
                      {image.repository}
                    </td>
                    <td className="px-4 py-3 text-blue-400 font-mono text-xs">
                      {image.tag}
                    </td>
                    <td className="px-4 py-3 font-mono text-gray-300 text-xs">
                      {image.id.substring(0, 12)}...
                    </td>
                    <td className="px-4 py-3 text-gray-300">
                      {formatBytes(image.size)}
                    </td>
                    <td className="px-4 py-3 text-gray-300 text-xs">
                      {formatDateTime(image.created)}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex gap-1">
                        <button
                          type="button"
                          onClick={() => {
                            // TODO: Criar container a partir da imagem
                            toast.info('Funcionalidade em desenvolvimento');
                          }}
                          className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                          title="Criar container"
                        >
                          ▶️
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRemoveImage(image.id)}
                          className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                          title="Remover imagem"
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {filteredImages.length === 0 && (
              <div className="flex flex-col items-center justify-center py-16 text-gray-400">
                <div className="text-6xl mb-4">🖼️</div>
                <h3 className="text-lg font-medium mb-2">Nenhuma imagem encontrada</h3>
                <p className="text-sm text-center max-w-md">
                  {searchTerm 
                    ? 'Tente ajustar o termo de busca.'
                    : 'Faça o pull de uma imagem para começar.'
                  }
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modal de Pull Image */}
      {showPullModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 border border-gray-600">
            <h3 className="text-lg font-semibold text-white mb-4">
              📥 Pull Image
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nome da Imagem
              </label>
              <input
                type="text"
                value={pullImageName}
                onChange={(e) => setPullImageName(e.target.value)}
                placeholder="nginx:latest"
                className="
                  w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md
                  text-white placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
                "
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handlePullImage();
                  }
                }}
              />
              <p className="text-xs text-gray-400 mt-1">
                Exemplo: nginx:latest, ubuntu:20.04, postgres:13
              </p>
            </div>

            <div className="flex justify-end gap-3">
              <button
                type="button"
                onClick={() => {
                  setShowPullModal(false);
                  setPullImageName('');
                }}
                className="px-4 py-2 text-sm text-gray-300 hover:text-white"
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={handlePullImage}
                disabled={pullImage.isLoading || !pullImageName.trim()}
                className="
                  px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                  hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                  flex items-center gap-2
                "
              >
                {pullImage.isLoading && <LoadingSpinner size="sm" />}
                Pull
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
