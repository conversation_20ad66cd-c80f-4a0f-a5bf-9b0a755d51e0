using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Parser para saídas de comandos CLI de containers
/// </summary>
public class ContainerOutputParser
{
    private readonly JsonSerializerOptions _jsonOptions;

    public ContainerOutputParser()
    {
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    /// <summary>
    /// Faz parse da lista de containers
    /// </summary>
    public IEnumerable<Container> ParseContainerList(string output, ContainerEngine engine)
    {
        if (string.IsNullOrWhiteSpace(output))
            return Enumerable.Empty<Container>();

        var containers = new List<Container>();
        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            try
            {
                var jsonElement = JsonSerializer.Deserialize<JsonElement>(line, _jsonOptions);
                var container = ParseContainerFromJson(jsonElement, engine);
                if (container != null)
                    containers.Add(container);
            }
            catch (JsonException)
            {
                // Linha não é JSON válido, ignorar
                continue;
            }
        }

        return containers;
    }

    /// <summary>
    /// Faz parse de inspect de container
    /// </summary>
    public Container? ParseContainerInspect(string output, ContainerEngine engine)
    {
        if (string.IsNullOrWhiteSpace(output))
            return null;

        try
        {
            var jsonArray = JsonSerializer.Deserialize<JsonElement[]>(output, _jsonOptions);
            if (jsonArray.Length == 0)
                return null;

            return ParseContainerFromInspect(jsonArray[0], engine);
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// Faz parse de logs de container
    /// </summary>
    public IEnumerable<ContainerLog> ParseContainerLogs(string output, string containerId)
    {
        if (string.IsNullOrWhiteSpace(output))
            return Enumerable.Empty<ContainerLog>();

        var logs = new List<ContainerLog>();
        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            var log = ParseLogLine(line, containerId);
            if (log != null)
                logs.Add(log);
        }

        return logs;
    }

    /// <summary>
    /// Faz parse de estatísticas de container
    /// </summary>
    public ContainerStats? ParseContainerStats(string output, string containerId)
    {
        if (string.IsNullOrWhiteSpace(output))
            return null;

        try
        {
            var jsonElement = JsonSerializer.Deserialize<JsonElement>(output, _jsonOptions);
            return ParseStatsFromJson(jsonElement, containerId);
        }
        catch (JsonException)
        {
            return null;
        }
    }

    /// <summary>
    /// Faz parse da lista de imagens
    /// </summary>
    public IEnumerable<ContainerImage> ParseImageList(string output, ContainerEngine engine)
    {
        if (string.IsNullOrWhiteSpace(output))
            return Enumerable.Empty<ContainerImage>();

        var images = new List<ContainerImage>();
        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        foreach (var line in lines)
        {
            try
            {
                var jsonElement = JsonSerializer.Deserialize<JsonElement>(line, _jsonOptions);
                var image = ParseImageFromJson(jsonElement, engine);
                if (image != null)
                    images.Add(image);
            }
            catch (JsonException)
            {
                continue;
            }
        }

        return images;
    }

    /// <summary>
    /// Faz parse de busca de imagens
    /// </summary>
    public IEnumerable<ContainerImageSearchResult> ParseImageSearch(string output)
    {
        if (string.IsNullOrWhiteSpace(output))
            return Enumerable.Empty<ContainerImageSearchResult>();

        var results = new List<ContainerImageSearchResult>();
        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        // Pular cabeçalho
        for (int i = 1; i < lines.Length; i++)
        {
            var result = ParseSearchResultLine(lines[i]);
            if (result != null)
                results.Add(result);
        }

        return results;
    }

    /// <summary>
    /// Parse de container a partir de JSON
    /// </summary>
    private Container? ParseContainerFromJson(JsonElement json, ContainerEngine engine)
    {
        try
        {
            var container = new Container
            {
                ContainerId = GetJsonString(json, "ID") ?? "",
                Name = GetJsonString(json, "Names")?.TrimStart('/') ?? "",
                Image = GetJsonString(json, "Image") ?? "",
                Status = ParseContainerStatus(GetJsonString(json, "Status")),
                State = ParseContainerState(GetJsonString(json, "State")),
                Engine = engine,
                Command = GetJsonString(json, "Command") ?? "",
                CreatedAt = ParseDateTime(GetJsonString(json, "CreatedAt")),
                Platform = Environment.OSVersion.Platform.ToString()
            };

            // Parse de portas
            var portsStr = GetJsonString(json, "Ports");
            if (!string.IsNullOrEmpty(portsStr))
            {
                container.Ports = ParsePorts(portsStr);
            }

            return container;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Parse de container a partir de inspect
    /// </summary>
    private Container? ParseContainerFromInspect(JsonElement json, ContainerEngine engine)
    {
        try
        {
            var container = new Container
            {
                ContainerId = GetJsonString(json, "Id") ?? "",
                Name = GetJsonString(json, "Name")?.TrimStart('/') ?? "",
                Engine = engine
            };

            // Config
            if (json.TryGetProperty("Config", out var config))
            {
                container.Image = GetJsonString(config, "Image") ?? "";
                container.Command = GetJsonString(config, "Cmd")?[0] ?? "";
                container.WorkingDir = GetJsonString(config, "WorkingDir");
                container.User = GetJsonString(config, "User");

                // Environment
                if (config.TryGetProperty("Env", out var env))
                {
                    foreach (var envVar in env.EnumerateArray())
                    {
                        var envStr = envVar.GetString();
                        if (!string.IsNullOrEmpty(envStr))
                        {
                            var parts = envStr.Split('=', 2);
                            if (parts.Length == 2)
                            {
                                container.Environment[parts[0]] = parts[1];
                            }
                        }
                    }
                }

                // Labels
                if (config.TryGetProperty("Labels", out var labels))
                {
                    foreach (var label in labels.EnumerateObject())
                    {
                        container.Labels[label.Name] = label.Value.GetString() ?? "";
                    }
                }
            }

            // State
            if (json.TryGetProperty("State", out var state))
            {
                container.Status = ParseContainerStatus(GetJsonString(state, "Status"));
                container.State = ParseContainerState(GetJsonString(state, "Status"));
                container.ExitCode = GetJsonInt(state, "ExitCode");
                container.StartedAt = ParseDateTime(GetJsonString(state, "StartedAt"));
                container.FinishedAt = ParseDateTime(GetJsonString(state, "FinishedAt"));
            }

            container.CreatedAt = ParseDateTime(GetJsonString(json, "Created"));

            return container;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Parse de linha de log
    /// </summary>
    private ContainerLog? ParseLogLine(string line, string containerId)
    {
        if (string.IsNullOrWhiteSpace(line))
            return null;

        // Formato: 2023-01-01T12:00:00.000000000Z message
        var timestampMatch = Regex.Match(line, @"^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z)\s+(.*)$");
        
        if (timestampMatch.Success)
        {
            return new ContainerLog
            {
                Timestamp = DateTime.Parse(timestampMatch.Groups[1].Value, null, DateTimeStyles.RoundtripKind),
                Message = timestampMatch.Groups[2].Value,
                Stream = LogStream.Stdout, // Docker não diferencia por padrão
                ContainerId = containerId
            };
        }

        return new ContainerLog
        {
            Timestamp = DateTime.UtcNow,
            Message = line,
            Stream = LogStream.Stdout,
            ContainerId = containerId
        };
    }

    /// <summary>
    /// Parse de estatísticas a partir de JSON
    /// </summary>
    private ContainerStats? ParseStatsFromJson(JsonElement json, string containerId)
    {
        try
        {
            var stats = new ContainerStats
            {
                ContainerId = containerId,
                Name = GetJsonString(json, "Name") ?? "",
                Timestamp = DateTime.UtcNow
            };

            // CPU Stats
            if (json.TryGetProperty("CPUPerc", out var cpuPerc))
            {
                var cpuStr = cpuPerc.GetString()?.Replace("%", "");
                if (double.TryParse(cpuStr, out var cpu))
                {
                    stats.Cpu.Usage = cpu;
                }
            }

            // Memory Stats
            if (json.TryGetProperty("MemUsage", out var memUsage))
            {
                var memStr = memUsage.GetString();
                if (!string.IsNullOrEmpty(memStr))
                {
                    var parts = memStr.Split('/');
                    if (parts.Length == 2)
                    {
                        stats.Memory.Usage = ParseMemorySize(parts[0].Trim());
                        stats.Memory.Limit = ParseMemorySize(parts[1].Trim());
                        
                        if (stats.Memory.Limit > 0)
                        {
                            stats.Memory.Percentage = (double)stats.Memory.Usage / stats.Memory.Limit * 100;
                        }
                    }
                }
            }

            // Network Stats
            if (json.TryGetProperty("NetIO", out var netIO))
            {
                var netStr = netIO.GetString();
                if (!string.IsNullOrEmpty(netStr))
                {
                    var parts = netStr.Split('/');
                    if (parts.Length == 2)
                    {
                        stats.Network.RxBytes = ParseMemorySize(parts[0].Trim());
                        stats.Network.TxBytes = ParseMemorySize(parts[1].Trim());
                    }
                }
            }

            // Block I/O Stats
            if (json.TryGetProperty("BlockIO", out var blockIO))
            {
                var blockStr = blockIO.GetString();
                if (!string.IsNullOrEmpty(blockStr))
                {
                    var parts = blockStr.Split('/');
                    if (parts.Length == 2)
                    {
                        stats.BlockIO.ReadBytes = ParseMemorySize(parts[0].Trim());
                        stats.BlockIO.WriteBytes = ParseMemorySize(parts[1].Trim());
                    }
                }
            }

            // PIDs
            if (json.TryGetProperty("PIDs", out var pids))
            {
                if (int.TryParse(pids.GetString(), out var pidCount))
                {
                    stats.Pids = pidCount;
                }
            }

            return stats;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Parse de imagem a partir de JSON
    /// </summary>
    private ContainerImage? ParseImageFromJson(JsonElement json, ContainerEngine engine)
    {
        try
        {
            var repository = GetJsonString(json, "Repository") ?? "";
            var tag = GetJsonString(json, "Tag") ?? "";

            return new ContainerImage
            {
                ImageId = GetJsonString(json, "ID") ?? "",
                Repository = repository,
                Tag = tag,
                Size = ParseMemorySize(GetJsonString(json, "Size") ?? "0"),
                Created = ParseDateTime(GetJsonString(json, "CreatedAt")),
                Engine = engine
            };
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Parse de linha de resultado de busca
    /// </summary>
    private ContainerImageSearchResult? ParseSearchResultLine(string line)
    {
        if (string.IsNullOrWhiteSpace(line))
            return null;

        // Formato típico: NAME DESCRIPTION STARS OFFICIAL AUTOMATED
        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length < 5)
            return null;

        return new ContainerImageSearchResult
        {
            Name = parts[0],
            Description = string.Join(" ", parts.Skip(1).Take(parts.Length - 3)),
            Stars = int.TryParse(parts[^3], out var stars) ? stars : 0,
            IsOfficial = parts[^2].Equals("[OK]", StringComparison.OrdinalIgnoreCase),
            IsAutomated = parts[^1].Equals("[OK]", StringComparison.OrdinalIgnoreCase)
        };
    }

    /// <summary>
    /// Utilitários de parsing
    /// </summary>
    private string? GetJsonString(JsonElement json, string propertyName)
    {
        return json.TryGetProperty(propertyName, out var prop) ? prop.GetString() : null;
    }

    private int? GetJsonInt(JsonElement json, string propertyName)
    {
        return json.TryGetProperty(propertyName, out var prop) && prop.TryGetInt32(out var value) ? value : null;
    }

    private ContainerStatus ParseContainerStatus(string? status)
    {
        return status?.ToLowerInvariant() switch
        {
            "created" => ContainerStatus.Created,
            "running" => ContainerStatus.Running,
            "paused" => ContainerStatus.Paused,
            "restarting" => ContainerStatus.Restarting,
            "removing" => ContainerStatus.Removing,
            "dead" => ContainerStatus.Dead,
            "exited" => ContainerStatus.Exited,
            _ => ContainerStatus.Exited
        };
    }

    private ContainerState ParseContainerState(string? state)
    {
        return state?.ToLowerInvariant() switch
        {
            "created" => ContainerState.Created,
            "running" => ContainerState.Running,
            "paused" => ContainerState.Paused,
            "restarting" => ContainerState.Restarting,
            "removing" => ContainerState.Removing,
            "dead" => ContainerState.Dead,
            "exited" => ContainerState.Exited,
            _ => ContainerState.Unknown
        };
    }

    private DateTime ParseDateTime(string? dateStr)
    {
        if (string.IsNullOrEmpty(dateStr))
            return DateTime.UtcNow;

        return DateTime.TryParse(dateStr, null, DateTimeStyles.RoundtripKind, out var date) ? date : DateTime.UtcNow;
    }

    private List<ContainerPort> ParsePorts(string portsStr)
    {
        var ports = new List<ContainerPort>();
        
        // Formato: 0.0.0.0:8080->80/tcp, 443/tcp
        var portMappings = portsStr.Split(',', StringSplitOptions.RemoveEmptyEntries);
        
        foreach (var mapping in portMappings)
        {
            var trimmed = mapping.Trim();
            var portMatch = Regex.Match(trimmed, @"(?:(\d+\.\d+\.\d+\.\d+):)?(?:(\d+)->)?(\d+)/(tcp|udp)");
            
            if (portMatch.Success)
            {
                var port = new ContainerPort
                {
                    ContainerPortNumber = int.Parse(portMatch.Groups[3].Value),
                    Protocol = portMatch.Groups[4].Value == "udp" ? NetworkProtocol.Udp : NetworkProtocol.Tcp
                };

                if (!string.IsNullOrEmpty(portMatch.Groups[1].Value))
                    port.HostIp = portMatch.Groups[1].Value;

                if (!string.IsNullOrEmpty(portMatch.Groups[2].Value))
                {
                    port.HostPort = int.Parse(portMatch.Groups[2].Value);
                    port.Type = PortType.Published;
                }
                else
                {
                    port.Type = PortType.Exposed;
                }

                ports.Add(port);
            }
        }

        return ports;
    }

    private long ParseMemorySize(string sizeStr)
    {
        if (string.IsNullOrEmpty(sizeStr))
            return 0;

        var match = Regex.Match(sizeStr, @"^([\d.]+)\s*([KMGT]?i?B?)$", RegexOptions.IgnoreCase);
        if (!match.Success)
            return 0;

        if (!double.TryParse(match.Groups[1].Value, out var size))
            return 0;

        var unit = match.Groups[2].Value.ToUpperInvariant();
        var multiplier = unit switch
        {
            "KB" or "K" => 1000L,
            "KIB" => 1024L,
            "MB" or "M" => 1000L * 1000,
            "MIB" => 1024L * 1024,
            "GB" or "G" => 1000L * 1000 * 1000,
            "GIB" => 1024L * 1024 * 1024,
            "TB" or "T" => 1000L * 1000 * 1000 * 1000,
            "TIB" => 1024L * 1024 * 1024 * 1024,
            _ => 1L
        };

        return (long)(size * multiplier);
    }
}
