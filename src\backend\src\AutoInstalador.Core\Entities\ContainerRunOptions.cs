using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Entities;

/// <summary>
/// Opções para execução de containers
/// </summary>
public class ContainerRunOptions
{
    /// <summary>
    /// Nome da imagem do container (obrigatório)
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Executar em modo detached (background)
    /// </summary>
    public bool Detached { get; set; } = true;

    /// <summary>
    /// Modo interativo
    /// </summary>
    public bool Interactive { get; set; } = false;

    /// <summary>
    /// Alocar TTY
    /// </summary>
    public bool Tty { get; set; } = false;

    /// <summary>
    /// Remover container após execução
    /// </summary>
    public bool RemoveAfterRun { get; set; } = false;

    /// <summary>
    /// Modo privilegiado
    /// </summary>
    public bool Privileged { get; set; } = false;

    /// <summary>
    /// Mapeamento de portas
    /// </summary>
    public List<ContainerPortMapping> Ports { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> Environment { get; set; } = new();

    /// <summary>
    /// Volumes/Mounts
    /// </summary>
    public List<ContainerVolumeMapping> Volumes { get; set; } = new();

    /// <summary>
    /// Labels
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Redes
    /// </summary>
    public List<string> Networks { get; set; } = new();

    /// <summary>
    /// Política de restart
    /// </summary>
    public string? RestartPolicy { get; set; }

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDirectory { get; set; }

    /// <summary>
    /// Usuário
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Hostname
    /// </summary>
    public string? Hostname { get; set; }

    /// <summary>
    /// Limite de memória em MB
    /// </summary>
    public long? MemoryLimit { get; set; }

    /// <summary>
    /// Limite de CPU (número de cores)
    /// </summary>
    public double? CpuLimit { get; set; }

    /// <summary>
    /// Entrypoint personalizado
    /// </summary>
    public List<string>? Entrypoint { get; set; }

    /// <summary>
    /// Comando a executar
    /// </summary>
    public List<string>? Command { get; set; }

    /// <summary>
    /// Engine de container a usar
    /// </summary>
    public ContainerEngine Engine { get; set; } = ContainerEngine.Docker;
}

/// <summary>
/// Mapeamento de porta para container
/// </summary>
public class ContainerPortMapping
{
    /// <summary>
    /// Porta no host (opcional para porta aleatória)
    /// </summary>
    public int? HostPort { get; set; }

    /// <summary>
    /// Porta no container
    /// </summary>
    public int ContainerPort { get; set; }

    /// <summary>
    /// Protocolo (tcp, udp)
    /// </summary>
    public string Protocol { get; set; } = "tcp";

    /// <summary>
    /// IP do host para bind
    /// </summary>
    public string? HostIp { get; set; }
}

/// <summary>
/// Mapeamento de volume para container
/// </summary>
public class ContainerVolumeMapping
{
    /// <summary>
    /// Caminho no host
    /// </summary>
    public string HostPath { get; set; } = string.Empty;

    /// <summary>
    /// Caminho no container
    /// </summary>
    public string ContainerPath { get; set; } = string.Empty;

    /// <summary>
    /// Somente leitura
    /// </summary>
    public bool ReadOnly { get; set; } = false;

    /// <summary>
    /// Tipo de volume (bind, volume, tmpfs)
    /// </summary>
    public string Type { get; set; } = "bind";
}
