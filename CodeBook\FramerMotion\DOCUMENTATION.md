# Framer Motion 11.5.4 - Documentação Oficial Resumida

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 11.5.4  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.framer.com/motion/
- **GitHub:** https://github.com/framer/motion
- **Documentação:** https://www.framer.com/motion/introduction/
- **NPM/Package:** https://www.npmjs.com/package/framer-motion
- **Fórum/Community:** https://github.com/framer/motion/discussions
- **Stack Overflow Tag:** `framer-motion`

---

## 📚 **DOCUMENTAÇÃO CORE FRAMER MOTION 11.5**

### **1. Componentes Básicos**

#### **motion Components**
```typescript
import { motion } from 'framer-motion';

// Qualquer elemento HTML pode ser animado
const AnimatedDiv = motion.div;
const AnimatedButton = motion.button;
const AnimatedSVG = motion.svg;

// Uso básico
function BasicAnimation() {
  return (
    <motion.div
      initial={{ opacity: 0, x: -100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 100 }}
      transition={{ duration: 0.5 }}
    >
      Conteúdo animado
    </motion.div>
  );
}
```

#### **AnimatePresence**
```typescript
import { AnimatePresence, motion } from 'framer-motion';

function ConditionalAnimation({ isVisible }: { isVisible: boolean }) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3 }}
        >
          Elemento condicional
        </motion.div>
      )}
    </AnimatePresence>
  );
}
```

---

### **2. Propriedades de Animação**

#### **Propriedades Básicas**
```typescript
// initial - Estado inicial
initial={{ opacity: 0, y: 50 }}

// animate - Estado final
animate={{ opacity: 1, y: 0 }}

// exit - Estado de saída (requer AnimatePresence)
exit={{ opacity: 0, y: -50 }}

// transition - Configuração da transição
transition={{ 
  duration: 0.5,
  ease: "easeOut",
  delay: 0.1
}}
```

#### **Propriedades Avançadas**
```typescript
// whileHover - Animação no hover
whileHover={{ scale: 1.05, rotate: 5 }}

// whileTap - Animação no clique
whileTap={{ scale: 0.95 }}

// whileFocus - Animação no foco
whileFocus={{ outline: "2px solid blue" }}

// whileInView - Animação quando entra na viewport
whileInView={{ opacity: 1, y: 0 }}
viewport={{ once: true, amount: 0.3 }}
```

---

### **3. Variants System**

#### **Definindo Variants**
```typescript
const containerVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.8
  },
  visible: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      when: "beforeChildren",
      staggerChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: {
      when: "afterChildren"
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};
```

#### **Usando Variants**
```typescript
function VariantExample() {
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {items.map((item, index) => (
        <motion.div
          key={item.id}
          variants={itemVariants}
        >
          {item.content}
        </motion.div>
      ))}
    </motion.div>
  );
}
```

---

### **4. Layout Animations**

#### **Layout Prop**
```typescript
// Animação automática de layout
function LayoutAnimation() {
  const [isExpanded, setIsExpanded] = useState(false);
  
  return (
    <motion.div
      layout
      className={isExpanded ? 'expanded' : 'collapsed'}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <motion.h2 layout>Título</motion.h2>
      <motion.p layout>Conteúdo que expande</motion.p>
    </motion.div>
  );
}
```

#### **LayoutGroup**
```typescript
import { LayoutGroup } from 'framer-motion';

function SharedLayoutAnimation() {
  const [selected, setSelected] = useState(null);
  
  return (
    <LayoutGroup>
      {items.map(item => (
        <motion.div
          key={item.id}
          layoutId={item.id}
          onClick={() => setSelected(item.id)}
        >
          {item.content}
        </motion.div>
      ))}
    </LayoutGroup>
  );
}
```

---

### **5. Gestures e Interações**

#### **Drag**
```typescript
function DraggableComponent() {
  return (
    <motion.div
      drag
      dragConstraints={{ left: 0, right: 300, top: 0, bottom: 300 }}
      dragElastic={0.2}
      whileDrag={{ scale: 1.1 }}
      onDragEnd={(event, info) => {
        console.log('Drag ended at:', info.point);
      }}
    >
      Arraste-me
    </motion.div>
  );
}
```

#### **Hover e Tap**
```typescript
function InteractiveButton() {
  return (
    <motion.button
      whileHover={{ 
        scale: 1.05,
        boxShadow: "0px 5px 10px rgba(0,0,0,0.2)"
      }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      Botão Interativo
    </motion.button>
  );
}
```

---

### **6. Hooks Avançados**

#### **useAnimation**
```typescript
import { useAnimation } from 'framer-motion';

function ControlledAnimation() {
  const controls = useAnimation();
  
  const startAnimation = async () => {
    await controls.start({ x: 100 });
    await controls.start({ x: 0 });
  };
  
  return (
    <div>
      <motion.div animate={controls} />
      <button onClick={startAnimation}>Animar</button>
    </div>
  );
}
```

#### **useMotionValue**
```typescript
import { useMotionValue, useTransform } from 'framer-motion';

function MotionValueExample() {
  const x = useMotionValue(0);
  const opacity = useTransform(x, [-100, 0, 100], [0, 1, 0]);
  
  return (
    <motion.div
      drag="x"
      style={{ x, opacity }}
    >
      Arraste horizontalmente
    </motion.div>
  );
}
```

#### **useScroll**
```typescript
import { useScroll, useTransform } from 'framer-motion';

function ScrollAnimation() {
  const { scrollYProgress } = useScroll();
  const scale = useTransform(scrollYProgress, [0, 1], [1, 0.8]);
  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0.5]);
  
  return (
    <motion.div style={{ scale, opacity }}>
      Elemento que muda com scroll
    </motion.div>
  );
}
```

---

### **7. Performance Optimization**

#### **will-change e transform**
```typescript
// Otimizações automáticas no Framer Motion 11.5
function OptimizedAnimation() {
  return (
    <motion.div
      // Propriedades que usam transform são otimizadas automaticamente
      animate={{ x: 100, y: 50, scale: 1.2, rotate: 45 }}
      
      // will-change é aplicado automaticamente
      transition={{ duration: 0.5 }}
    >
      Animação otimizada
    </motion.div>
  );
}
```

#### **Reduced Motion**
```typescript
import { useReducedMotion } from 'framer-motion';

function AccessibleAnimation() {
  const shouldReduceMotion = useReducedMotion();
  
  return (
    <motion.div
      animate={{ 
        x: shouldReduceMotion ? 0 : 100,
        transition: { duration: shouldReduceMotion ? 0 : 0.5 }
      }}
    >
      Animação acessível
    </motion.div>
  );
}
```

---

### **8. 3D Transforms**

#### **Transformações 3D**
```typescript
function ThreeDAnimation() {
  return (
    <motion.div
      style={{
        perspective: 1000
      }}
    >
      <motion.div
        whileHover={{
          rotateY: 180,
          rotateX: 15
        }}
        transition={{ duration: 0.6 }}
        style={{
          transformStyle: "preserve-3d"
        }}
      >
        Elemento 3D
      </motion.div>
    </motion.div>
  );
}
```

---

### **9. SVG Animations**

#### **Path Drawing**
```typescript
function SVGAnimation() {
  return (
    <motion.svg width="100" height="100">
      <motion.path
        d="M10,30 Q90,90 90,45 Q90,10 50,10 Q10,10 10,30 Z"
        stroke="#000"
        strokeWidth="2"
        fill="transparent"
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 2 }}
      />
    </motion.svg>
  );
}
```

---

### **10. React 19.2 Integration**

#### **Concurrent Features**
```typescript
import { use, Suspense } from 'react';
import { motion } from 'framer-motion';

function ConcurrentAnimation({ dataPromise }: { dataPromise: Promise<any> }) {
  const data = use(dataPromise);
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {data.map((item: any) => (
        <motion.div
          key={item.id}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: item.index * 0.1 }}
        >
          {item.content}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Uso com Suspense
function App() {
  const dataPromise = fetchData();
  
  return (
    <Suspense fallback={<LoadingAnimation />}>
      <ConcurrentAnimation dataPromise={dataPromise} />
    </Suspense>
  );
}
```

#### **useActionState Integration**
```typescript
import { useActionState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

function AnimatedForm() {
  const [state, formAction, isPending] = useActionState(
    async (previousState: any, formData: FormData) => {
      // Form processing
      return { success: true };
    },
    { success: false }
  );
  
  return (
    <form action={formAction}>
      <motion.button
        type="submit"
        disabled={isPending}
        animate={{ 
          scale: isPending ? 0.95 : 1,
          opacity: isPending ? 0.7 : 1
        }}
        whileHover={{ scale: isPending ? 0.95 : 1.05 }}
      >
        {isPending ? 'Enviando...' : 'Enviar'}
      </motion.button>
      
      <AnimatePresence>
        {state.success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            Sucesso!
          </motion.div>
        )}
      </AnimatePresence>
    </form>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Framer Motion 11.5.4 Documentation
