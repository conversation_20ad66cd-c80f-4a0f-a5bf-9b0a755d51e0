/**
 * Container Components Index
 * Auto-Instalador V3 Lite
 * 
 * @description Exportações centralizadas dos componentes de container
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

// Main Components
export { ContainerDashboard } from './ContainerDashboard';
export { ContainerList } from './ContainerList';
export { ContainerStats } from './ContainerStats';
export { ContainerLogs } from './ContainerLogs';
export { ContainerControls } from './ContainerControls';

// Re-export service hooks for convenience
export {
  useContainers,
  useContainer,
  useContainerStats,
  useContainerLogs,
  useRunContainer,
  useContainerAction,
  useContainerImages,
  usePullImage,
  useRemoveImage,
  useContainerEngines,
  useEngineStatus,
  useDetectEngines,
  useInstallEngine,
  useIsEngineAvailable,
  useRecommendedEngine,
  useContainerEvents,
  formatContainerStatus,
  formatBytes,
  formatPercentage,
  getStatusColor,
  containerQueryKeys
} from '../../../services/container-service';

// Re-export types for convenience
export type {
  Container,
  ContainerEngine,
  ContainerStatus,
  ContainerStats as ContainerStatsType,
  ContainerLog,
  ContainerImage,
  ContainerEngineInfo,
  ContainerRunRequest,
  ContainerListRequest,
  ContainerLogsRequest,
  ContainerActionRequest,
  ContainerEngineInstallRequest
} from '../../../../../shared/types/api.types';
