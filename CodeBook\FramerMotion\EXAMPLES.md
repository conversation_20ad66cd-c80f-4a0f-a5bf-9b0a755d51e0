# Framer Motion 11.5.4 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 11.5.4  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.framer.com/motion/
- **GitHub:** https://github.com/framer/motion
- **Documentação:** https://www.framer.com/motion/introduction/
- **NPM/Package:** https://www.npmjs.com/package/framer-motion
- **Fórum/Community:** https://github.com/framer/motion/discussions
- **Stack Overflow Tag:** `framer-motion`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Sistema de Notificações Animadas**

```typescript
// src/components/notifications/AnimatedNotification.tsx
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { useState, useEffect } from 'react';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

interface NotificationSystemProps {
  notifications: Notification[];
  onRemove: (id: string) => void;
}

const notificationVariants = {
  initial: { 
    opacity: 0, 
    x: 300, 
    scale: 0.8 
  },
  animate: { 
    opacity: 1, 
    x: 0, 
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  },
  exit: { 
    opacity: 0, 
    x: 300, 
    scale: 0.8,
    transition: {
      duration: 0.2
    }
  }
};

const iconVariants = {
  initial: { scale: 0, rotate: -180 },
  animate: { 
    scale: 1, 
    rotate: 0,
    transition: {
      type: "spring",
      stiffness: 600,
      damping: 20,
      delay: 0.2
    }
  }
};

export function NotificationSystem({ notifications, onRemove }: NotificationSystemProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence mode="popLayout">
        {notifications.map((notification) => (
          <NotificationCard
            key={notification.id}
            notification={notification}
            onRemove={onRemove}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

function NotificationCard({ notification, onRemove }: {
  notification: Notification;
  onRemove: (id: string) => void;
}) {
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    if (notification.duration) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev <= 0) {
            onRemove(notification.id);
            return 0;
          }
          return prev - (100 / (notification.duration! / 100));
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [notification.duration, notification.id, onRemove]);

  const getIcon = () => {
    switch (notification.type) {
      case 'success': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error': return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default: return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getColors = () => {
    switch (notification.type) {
      case 'success': return 'border-green-200 bg-green-50';
      case 'error': return 'border-red-200 bg-red-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      default: return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <motion.div
      layout
      variants={notificationVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={`relative w-80 p-4 rounded-lg border shadow-lg ${getColors()}`}
    >
      {/* Progress bar */}
      {notification.duration && (
        <motion.div
          className="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg"
          initial={{ width: "100%" }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.1, ease: "linear" }}
        />
      )}

      <div className="flex items-start space-x-3">
        <motion.div variants={iconVariants}>
          {getIcon()}
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <motion.h4 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-sm font-medium text-gray-900"
          >
            {notification.title}
          </motion.h4>
          
          <motion.p 
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-sm text-gray-600 mt-1"
          >
            {notification.message}
          </motion.p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => onRemove(notification.id)}
          className="text-gray-400 hover:text-gray-600"
        >
          <X className="w-4 h-4" />
        </motion.button>
      </div>
    </motion.div>
  );
}
```

### **2. Loading States Animados**

```typescript
// src/components/ui/AnimatedLoading.tsx
import { motion } from 'framer-motion';

export function ContainerLoadingSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="bg-white rounded-lg border p-6"
        >
          {/* Header skeleton */}
          <div className="flex justify-between items-start mb-4">
            <div className="space-y-2">
              <motion.div
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="h-4 bg-gray-200 rounded w-32"
              />
              <motion.div
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
                className="h-3 bg-gray-200 rounded w-24"
              />
            </div>
            
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
              className="h-6 w-16 bg-gray-200 rounded-full"
            />
          </div>
          
          {/* Content skeleton */}
          <div className="space-y-3">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 }}
              className="h-3 bg-gray-200 rounded w-full"
            />
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 0.8 }}
              className="h-3 bg-gray-200 rounded w-3/4"
            />
          </div>
          
          {/* Actions skeleton */}
          <div className="flex space-x-2 mt-4">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 1 }}
              className="h-8 bg-gray-200 rounded flex-1"
            />
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: 1.2 }}
              className="h-8 w-8 bg-gray-200 rounded"
            />
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function SpinnerLoader({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`${sizeClasses[size]} border-2 border-blue-200 border-t-blue-500 rounded-full`}
    />
  );
}

export function DotsLoader() {
  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.2
          }}
          className="w-2 h-2 bg-blue-500 rounded-full"
        />
      ))}
    </div>
  );
}
```

### **3. Modal com Backdrop Animado**

```typescript
// src/components/ui/AnimatedModal.tsx
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useEffect } from 'react';

interface AnimatedModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const backdropVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 }
};

const modalVariants = {
  hidden: { 
    opacity: 0, 
    scale: 0.8, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    scale: 1, 
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 25
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.8, 
    y: 50,
    transition: {
      duration: 0.2
    }
  }
};

export function AnimatedModal({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = 'md' 
}: AnimatedModalProps) {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  // Fechar com ESC
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
      document.body.style.overflow = 'hidden';
    }
    
    return () => {
      document.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={onClose}
            className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
          />
          
          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={`relative bg-white rounded-lg shadow-xl w-full ${sizeClasses[size]} max-h-[90vh] overflow-hidden`}
          >
            {/* Header */}
            <motion.div 
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex items-center justify-between p-6 border-b border-gray-200"
            >
              <h2 className="text-xl font-semibold text-gray-900">
                {title}
              </h2>
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </motion.button>
            </motion.div>
            
            {/* Content */}
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
            >
              {children}
            </motion.div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}

// Exemplo de uso
export function CreateContainerModal() {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsOpen(true)}
        className="bg-blue-500 text-white px-4 py-2 rounded-lg"
      >
        Criar Container
      </motion.button>
      
      <AnimatedModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Criar Novo Container"
        size="lg"
      >
        <CreateContainerForm onSuccess={() => setIsOpen(false)} />
      </AnimatedModal>
    </>
  );
}
```

### **4. Sidebar Animada**

```typescript
// src/components/layout/AnimatedSidebar.tsx
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  badge?: number;
}

interface AnimatedSidebarProps {
  items: SidebarItem[];
  activeItem: string;
  onItemClick: (id: string) => void;
}

const sidebarVariants = {
  expanded: { 
    width: 256,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 40
    }
  },
  collapsed: { 
    width: 64,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 40
    }
  }
};

const itemVariants = {
  expanded: {
    opacity: 1,
    x: 0,
    transition: {
      delay: 0.1
    }
  },
  collapsed: {
    opacity: 0,
    x: -20,
    transition: {
      duration: 0.1
    }
  }
};

export function AnimatedSidebar({ items, activeItem, onItemClick }: AnimatedSidebarProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <motion.div
      variants={sidebarVariants}
      animate={isExpanded ? "expanded" : "collapsed"}
      className="bg-white border-r border-gray-200 flex flex-col h-full relative"
    >
      {/* Toggle Button */}
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsExpanded(!isExpanded)}
        className="absolute -right-3 top-6 bg-white border border-gray-200 rounded-full p-1 shadow-sm z-10"
      >
        {isExpanded ? (
          <ChevronLeft className="w-4 h-4" />
        ) : (
          <ChevronRight className="w-4 h-4" />
        )}
      </motion.button>

      {/* Logo */}
      <div className="p-4 border-b border-gray-200">
        <motion.div
          layout
          className="flex items-center space-x-3"
        >
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">AI</span>
          </div>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                variants={itemVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
              >
                <h1 className="font-semibold text-gray-900">Auto-Instalador</h1>
                <p className="text-xs text-gray-500">V3 Lite</p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-2 space-y-1">
        {items.map((item) => (
          <motion.button
            key={item.id}
            layout
            whileHover={{ x: 4 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onItemClick(item.id)}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
              activeItem === item.id
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-500'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <item.icon className="w-5 h-5 flex-shrink-0" />
            
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  variants={itemVariants}
                  initial="collapsed"
                  animate="expanded"
                  exit="collapsed"
                  className="flex items-center justify-between flex-1 min-w-0"
                >
                  <span className="truncate">{item.label}</span>
                  
                  {item.badge && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center"
                    >
                      {item.badge}
                    </motion.span>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
        ))}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <motion.div
          layout
          className="flex items-center space-x-3"
        >
          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                variants={itemVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                className="min-w-0"
              >
                <p className="text-sm font-medium text-gray-900 truncate">Admin</p>
                <p className="text-xs text-gray-500 truncate"><EMAIL></p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </motion.div>
  );
}
```

### **5. Drag and Drop com Animações**

```typescript
// src/components/containers/DragDropContainers.tsx
import { motion, Reorder } from 'framer-motion';
import { GripVertical } from 'lucide-react';
import { useState } from 'react';

interface DragDropItem {
  id: string;
  name: string;
  status: string;
  image: string;
}

interface DragDropContainersProps {
  containers: DragDropItem[];
  onReorder: (newOrder: DragDropItem[]) => void;
}

export function DragDropContainers({ containers, onReorder }: DragDropContainersProps) {
  const [items, setItems] = useState(containers);

  const handleReorder = (newOrder: DragDropItem[]) => {
    setItems(newOrder);
    onReorder(newOrder);
  };

  return (
    <Reorder.Group
      axis="y"
      values={items}
      onReorder={handleReorder}
      className="space-y-2"
    >
      {items.map((container) => (
        <DragDropItem key={container.id} container={container} />
      ))}
    </Reorder.Group>
  );
}

function DragDropItem({ container }: { container: DragDropItem }) {
  return (
    <Reorder.Item
      value={container}
      id={container.id}
      className="bg-white rounded-lg border p-4 cursor-grab active:cursor-grabbing"
    >
      <motion.div
        whileDrag={{ 
          scale: 1.02,
          boxShadow: "0 10px 25px rgba(0,0,0,0.15)",
          rotate: 2
        }}
        className="flex items-center space-x-4"
      >
        <motion.div
          whileHover={{ scale: 1.1 }}
          className="text-gray-400 hover:text-gray-600"
        >
          <GripVertical className="w-5 h-5" />
        </motion.div>
        
        <div className="flex-1">
          <h3 className="font-medium text-gray-900">{container.name}</h3>
          <p className="text-sm text-gray-600">{container.image}</p>
        </div>
        
        <motion.span
          layout
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            container.status === 'running'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {container.status}
        </motion.span>
      </motion.div>
    </Reorder.Item>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Framer Motion 11.5.4 Examples
