namespace AutoInstalador.Core.Entities;

/// <summary>
/// Resultado de busca de imagem de container
/// </summary>
public class ContainerImageSearchResult
{
    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição da imagem
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Número de estrelas/favoritos
    /// </summary>
    public int Stars { get; set; }

    /// <summary>
    /// Indica se é uma imagem oficial
    /// </summary>
    public bool IsOfficial { get; set; }

    /// <summary>
    /// Indica se é uma build automatizada
    /// </summary>
    public bool IsAutomated { get; set; }

    /// <summary>
    /// URL da imagem no registry
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// Registry de origem
    /// </summary>
    public string Registry { get; set; } = "docker.io";

    /// <summary>
    /// Tags disponíveis
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime? LastUpdated { get; set; }

    /// <summary>
    /// Tamanho da imagem em bytes
    /// </summary>
    public long? Size { get; set; }

    /// <summary>
    /// Arquiteturas suportadas
    /// </summary>
    public List<string> Architectures { get; set; } = new();
}
