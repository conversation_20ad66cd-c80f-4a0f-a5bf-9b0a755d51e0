# TailwindCSS 4.0.0-beta.1 - E<PERSON><PERSON> Comu<PERSON> e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 4.0.0-beta.1  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tailwindcss.com/
- **GitHub:** https://github.com/tailwindlabs/tailwindcss
- **Documentação:** https://tailwindcss.com/docs
- **NPM/Package:** https://www.npmjs.com/package/tailwindcss
- **Fórum/Community:** https://github.com/tailwindlabs/tailwindcss/discussions
- **Stack Overflow Tag:** `tailwind-css`

---

## 🚨 **ERROS CRÍTICOS TAILWIND 4.0**

### **1. Migration from v3.4 Issues**

#### **Erro:**
```
Error: @tailwind directives not working
Error: Classes not being generated
```

#### **Causa:**
Tentativa de usar configuração v3.4 com TailwindCSS 4.0.

#### **Solução:**
```css
/* ❌ ERRADO - Configuração v3.4 */
/* tailwind.config.js não funciona mais */

/* ✅ CORRETO - TailwindCSS 4.0 CSS-first */
/* src/styles/tailwind.css */
@import "tailwindcss";

@theme {
  --color-primary: #3b82f6;
  --spacing-custom: 1.5rem;
}

@layer components {
  .btn-primary {
    @apply bg-primary text-white px-4 py-2 rounded;
  }
}
```

```typescript
// Remover configuração antiga
// ❌ Deletar: tailwind.config.js
// ❌ Deletar: postcss.config.js (se só tinha Tailwind)

// ✅ Vite config limpo
export default defineConfig({
  plugins: [react()],
  // TailwindCSS 4.0 funciona automaticamente
});
```

---

### **2. CSS Import Order Issues**

#### **Erro:**
```
Error: Styles not applying correctly
Error: Custom components overridden
```

#### **Causa:**
Ordem incorreta de imports CSS.

#### **Solução:**
```css
/* ✅ ORDEM CORRETA */
/* src/styles/tailwind.css */
@import "tailwindcss";

/* Configurações de tema DEPOIS do import */
@theme {
  --color-primary: #3b82f6;
}

/* Layers na ordem correta */
@layer base {
  /* Reset styles */
}

@layer components {
  /* Component styles */
}

@layer utilities {
  /* Utility overrides */
}

/* ❌ ERRADO - Configurações antes do import */
@theme {
  --color-primary: #3b82f6;
}
@import "tailwindcss"; /* Muito tarde */
```

---

### **3. Custom Properties Not Working**

#### **Erro:**
```
Error: CSS custom properties not recognized
Error: --color-primary not found
```

#### **Causa:**
Sintaxe incorreta para custom properties no tema.

#### **Solução:**
```css
/* ❌ ERRADO */
@theme {
  colors: {
    primary: #3b82f6; /* Sintaxe v3.4 */
  }
}

/* ✅ CORRETO */
@theme {
  --color-primary: #3b82f6;
  --color-primary-50: #eff6ff;
  --color-primary-500: #3b82f6;
  --color-primary-900: #1e3a8a;
}

/* Usar nas classes */
.text-primary {
  color: theme(--color-primary);
}

/* Ou usar diretamente */
.bg-primary {
  background-color: var(--color-primary);
}
```

---

### **4. IntelliSense Not Working**

#### **Erro:**
```
IntelliSense not suggesting classes
Autocomplete not working
```

#### **Causa:**
VS Code extension não configurada para TailwindCSS 4.0.

#### **Solução:**
```json
// .vscode/settings.json
{
  "tailwindCSS.experimental.classRegex": [
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["className\\s*=\\s*[\"']([^\"']*)[\"']", "([^\"'\\s]*)"]
  ],
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.files.exclude": [
    "**/.git/**",
    "**/node_modules/**",
    "**/.next/**",
    "**/.nuxt/**",
    "**/.vuepress/**",
    "**/dist/**"
  ]
}

// Instalar extensão atualizada
// Tailwind CSS IntelliSense v0.10.0+
```

---

### **5. Dark Mode Not Working**

#### **Erro:**
```
Dark mode classes not applying
dark: prefix not working
```

#### **Causa:**
Dark mode não configurado corretamente.

#### **Solução:**
```css
/* CSS Configuration */
@layer base {
  :root {
    color-scheme: light;
  }
  
  .dark {
    color-scheme: dark;
  }
}

@theme {
  /* Light mode colors */
  --color-bg: #ffffff;
  --color-text: #111827;
  
  /* Dark mode colors */
  --color-dark-bg: #0f172a;
  --color-dark-text: #f1f5f9;
}
```

```typescript
// JavaScript toggle
function toggleDarkMode() {
  const isDark = document.documentElement.classList.contains('dark');
  document.documentElement.classList.toggle('dark', !isDark);
  localStorage.setItem('darkMode', (!isDark).toString());
}

// Initialize on load
useEffect(() => {
  const isDark = localStorage.getItem('darkMode') === 'true' ||
    (!localStorage.getItem('darkMode') && 
     window.matchMedia('(prefers-color-scheme: dark)').matches);
  
  document.documentElement.classList.toggle('dark', isDark);
}, []);
```

---

### **6. Container Queries Issues**

#### **Erro:**
```
@container queries not working
@sm: prefix not recognized
```

#### **Causa:**
Container queries não configuradas corretamente.

#### **Solução:**
```css
/* ✅ CORRETO - Container setup */
.container-card {
  @apply @container; /* Definir como container */
}

.responsive-content {
  /* Container-based responsive classes */
  @apply @sm:flex @sm:items-center @sm:space-x-4;
  @apply @lg:grid @lg:grid-cols-2;
}

/* ❌ ERRADO - Usar sem @container */
.content {
  @apply @sm:flex; /* Não funciona sem container pai */
}
```

```typescript
// React component
function ResponsiveCard() {
  return (
    <div className="@container bg-white rounded-lg p-4">
      <div className="@sm:flex @sm:items-center @sm:space-x-4">
        <img className="@sm:w-16 @sm:h-16 w-full h-32" />
        <div>
          <h3 className="@lg:text-xl text-lg">Title</h3>
        </div>
      </div>
    </div>
  );
}
```

---

### **7. Build Performance Issues**

#### **Erro:**
```
Build taking too long
High memory usage during build
```

#### **Causa:**
Configuração não otimizada para TailwindCSS 4.0.

#### **Solução:**
```css
/* Configuração de performance */
@config {
  --build-parallel: true;
  --build-workers: 8; /* Para i5 12ª Gen */
  --cache-strategy: "aggressive";
  --memory-limit: "4gb"; /* Para 32GB RAM */
}
```

```typescript
// Vite config otimizado
export default defineConfig({
  css: {
    // TailwindCSS 4.0 não precisa de PostCSS config
    // Remover plugins desnecessários
  },
  
  build: {
    // CSS code splitting para melhor performance
    cssCodeSplit: true,
    
    rollupOptions: {
      output: {
        // Separar CSS em chunks
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'assets/css/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        }
      }
    }
  }
});
```

---

### **8. Custom Components Not Working**

#### **Erro:**
```
@layer components not applying
Custom classes not generated
```

#### **Causa:**
Sintaxe incorreta ou ordem errada dos layers.

#### **Solução:**
```css
/* ✅ CORRETO - Ordem e sintaxe */
@import "tailwindcss";

@theme {
  --color-primary: #3b82f6;
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }
  
  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-600;
  }
  
  /* Componentes complexos */
  .card {
    @apply bg-white rounded-lg shadow border p-6;
    
    &:hover {
      @apply shadow-lg;
    }
  }
}

/* ❌ ERRADO - Fora do layer */
.btn {
  @apply px-4 py-2 rounded; /* Pode ser sobrescrito */
}
```

---

### **9. Responsive Design Issues**

#### **Erro:**
```
Responsive classes not working
Mobile-first not applying
```

#### **Causa:**
Breakpoints não configurados ou uso incorreto.

#### **Solução:**
```css
/* Configurar breakpoints customizados */
@theme {
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
```

```typescript
// ✅ CORRETO - Mobile-first approach
function ResponsiveComponent() {
  return (
    <div className="
      w-full          /* Mobile: full width */
      sm:w-1/2        /* Small: half width */
      md:w-1/3        /* Medium: third width */
      lg:w-1/4        /* Large: quarter width */
      xl:w-1/5        /* XL: fifth width */
    ">
      <div className="
        text-sm         /* Mobile: small text */
        md:text-base    /* Medium+: normal text */
        lg:text-lg      /* Large+: large text */
      ">
        Responsive text
      </div>
    </div>
  );
}

// ❌ ERRADO - Desktop-first (não recomendado)
function WrongApproach() {
  return (
    <div className="w-1/4 lg:w-1/3 md:w-1/2 sm:w-full">
      Wrong order
    </div>
  );
}
```

---

### **10. Animation Performance Issues**

#### **Erro:**
```
Animations stuttering
Poor performance on transitions
```

#### **Causa:**
Animações não otimizadas ou uso excessivo.

#### **Solução:**
```css
/* ✅ Animações otimizadas */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  /* Usar transform e opacity para melhor performance */
  .smooth-transform {
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
    will-change: transform, opacity;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(10px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}
```

```typescript
// React component otimizado
function AnimatedCard({ isVisible }: { isVisible: boolean }) {
  return (
    <div className={`
      transition-all duration-300 ease-out
      ${isVisible 
        ? 'opacity-100 transform translate-y-0' 
        : 'opacity-0 transform translate-y-4'
      }
    `}>
      <div className="bg-white rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
        Card content
      </div>
    </div>
  );
}

// ❌ EVITAR - Muitas animações simultâneas
function OverAnimated() {
  return (
    <div className="animate-pulse animate-bounce animate-spin"> {/* Muito pesado */}
      Over-animated content
    </div>
  );
}
```

---

## 🔧 **DEBUGGING TOOLS**

### **CSS Debugging**
```css
/* Debug utilities */
@layer utilities {
  .debug-screens::before {
    position: fixed;
    z-index: 2147483647;
    bottom: 0;
    left: 0;
    padding: 3px 6px;
    font-size: 12px;
    line-height: 1;
    font-family: sans-serif;
    background-color: #000;
    color: #fff;
    box-shadow: 0 0 0 1px #fff;
    content: 'xs';
  }
  
  @media (min-width: 640px) {
    .debug-screens::before {
      content: 'sm';
    }
  }
  
  @media (min-width: 768px) {
    .debug-screens::before {
      content: 'md';
    }
  }
  
  @media (min-width: 1024px) {
    .debug-screens::before {
      content: 'lg';
    }
  }
}
```

### **Browser DevTools**
```javascript
// Console debugging
console.log('Current breakpoint:', getComputedStyle(document.documentElement).getPropertyValue('--current-breakpoint'));

// Check if class is applied
const element = document.querySelector('.my-element');
console.log('Classes:', element.className);
console.log('Computed styles:', getComputedStyle(element));
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - TailwindCSS 4.0.0-beta.1 Common Errors
