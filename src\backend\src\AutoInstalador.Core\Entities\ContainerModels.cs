using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Entities;

/// <summary>
/// Opções para execução de container
/// </summary>
public class ContainerRunOptions
{
    /// <summary>
    /// Imagem do container
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Portas do container
    /// </summary>
    public List<ContainerPort> Ports { get; set; } = new();

    /// <summary>
    /// Volumes do container
    /// </summary>
    public List<ContainerVolume> Volumes { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> Environment { get; set; } = new();

    /// <summary>
    /// Comando a executar
    /// </summary>
    public string? Command { get; set; }

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[] Args { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDir { get; set; }

    /// <summary>
    /// Usuário do container
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Executar em modo detached
    /// </summary>
    public bool Detached { get; set; } = true;

    /// <summary>
    /// Modo interativo
    /// </summary>
    public bool Interactive { get; set; } = false;

    /// <summary>
    /// Alocar TTY
    /// </summary>
    public bool Tty { get; set; } = false;

    /// <summary>
    /// Remover container após execução
    /// </summary>
    public bool Remove { get; set; } = false;

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public RestartPolicy RestartPolicy { get; set; } = RestartPolicy.No;

    /// <summary>
    /// Recursos do container
    /// </summary>
    public ContainerResources? Resources { get; set; }

    /// <summary>
    /// Modo de rede
    /// </summary>
    public string NetworkMode { get; set; } = "bridge";

    /// <summary>
    /// Labels do container
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();
}

/// <summary>
/// Resultado de execução de comando
/// </summary>
public class CommandResult
{
    /// <summary>
    /// Código de saída
    /// </summary>
    public int ExitCode { get; set; }

    /// <summary>
    /// Saída padrão
    /// </summary>
    public string StandardOutput { get; set; } = string.Empty;

    /// <summary>
    /// Saída de erro
    /// </summary>
    public string StandardError { get; set; } = string.Empty;

    /// <summary>
    /// Se o comando foi executado com sucesso
    /// </summary>
    public bool Success => ExitCode == 0;

    /// <summary>
    /// Duração da execução
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Comando executado
    /// </summary>
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[] Arguments { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Resultado de execução em container
/// </summary>
public class ContainerExecResult
{
    /// <summary>
    /// Código de saída
    /// </summary>
    public int ExitCode { get; set; }

    /// <summary>
    /// Saída padrão
    /// </summary>
    public string StandardOutput { get; set; } = string.Empty;

    /// <summary>
    /// Saída de erro
    /// </summary>
    public string StandardError { get; set; } = string.Empty;

    /// <summary>
    /// Se a execução foi bem-sucedida
    /// </summary>
    public bool Success => ExitCode == 0;

    /// <summary>
    /// ID do container
    /// </summary>
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Comando executado
    /// </summary>
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[] Arguments { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Duração da execução
    /// </summary>
    public TimeSpan Duration { get; set; }
}

/// <summary>
/// Resultado de busca de imagem
/// </summary>
public class ContainerImageSearchResult
{
    /// <summary>
    /// Nome da imagem
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição da imagem
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Número de estrelas
    /// </summary>
    public int Stars { get; set; }

    /// <summary>
    /// Se é imagem oficial
    /// </summary>
    public bool IsOfficial { get; set; }

    /// <summary>
    /// Se é imagem automatizada
    /// </summary>
    public bool IsAutomated { get; set; }
}

/// <summary>
/// Filtros para listagem de containers
/// </summary>
public class ContainerListFilters
{
    /// <summary>
    /// Filtrar por status
    /// </summary>
    public List<ContainerStatus> Status { get; set; } = new();

    /// <summary>
    /// Filtrar por nome
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Filtrar por imagem
    /// </summary>
    public string? Image { get; set; }

    /// <summary>
    /// Filtrar por labels
    /// </summary>
    public List<string> Labels { get; set; } = new();

    /// <summary>
    /// Incluir containers parados
    /// </summary>
    public bool All { get; set; } = false;
}

/// <summary>
/// Configuração de comando CLI
/// </summary>
public class CliCommand
{
    /// <summary>
    /// Comando para Docker
    /// </summary>
    public string DockerCommand { get; set; } = string.Empty;

    /// <summary>
    /// Comando para Podman
    /// </summary>
    public string PodmanCommand { get; set; } = string.Empty;

    /// <summary>
    /// Formato de saída esperado
    /// </summary>
    public string OutputFormat { get; set; } = "json";

    /// <summary>
    /// Timeout do comando em segundos
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Se o comando requer privilégios
    /// </summary>
    public bool RequiresPrivileges { get; set; } = false;

    /// <summary>
    /// Parser personalizado para a saída
    /// </summary>
    public Func<string, object>? OutputParser { get; set; }
}

/// <summary>
/// Informações de instalação de engine
/// </summary>
public class EngineInstallInfo
{
    /// <summary>
    /// Engine a ser instalado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Plataforma do sistema
    /// </summary>
    public Platform Platform { get; set; }

    /// <summary>
    /// Gerenciador de pacotes
    /// </summary>
    public PackageManager? PackageManager { get; set; }

    /// <summary>
    /// Comandos de instalação
    /// </summary>
    public List<string> InstallCommands { get; set; } = new();

    /// <summary>
    /// Comandos de verificação
    /// </summary>
    public List<string> VerificationCommands { get; set; } = new();

    /// <summary>
    /// Se requer privilégios administrativos
    /// </summary>
    public bool RequiresAdmin { get; set; } = true;

    /// <summary>
    /// Tempo estimado de instalação em minutos
    /// </summary>
    public int EstimatedTimeMinutes { get; set; } = 5;
}

/// <summary>
/// Status de detecção de engine
/// </summary>
public class EngineDetectionResult
{
    /// <summary>
    /// Engine detectado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Se foi detectado
    /// </summary>
    public bool IsDetected { get; set; }

    /// <summary>
    /// Versão detectada
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Caminho do executável
    /// </summary>
    public string? ExecutablePath { get; set; }

    /// <summary>
    /// Se está em execução
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; set; }
}
