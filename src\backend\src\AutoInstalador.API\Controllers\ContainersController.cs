using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace AutoInstalador.API.Controllers;

/// <summary>
/// Controller para gerenciamento de containers
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ContainersController : ControllerBase
{
    private readonly IContainerService _containerService;
    private readonly IContainerImageService _imageService;
    private readonly IContainerEngineService _engineService;
    private readonly ILogger<ContainersController> _logger;

    public ContainersController(
        IContainerService containerService,
        IContainerImageService imageService,
        IContainerEngineService engineService,
        ILogger<ContainersController> logger)
    {
        _containerService = containerService;
        _imageService = imageService;
        _engineService = engineService;
        _logger = logger;
    }

    /// <summary>
    /// Lista todos os containers
    /// </summary>
    /// <param name="all">Incluir containers parados</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <param name="status">Filtrar por status</param>
    /// <param name="name">Filtrar por nome</param>
    /// <param name="image">Filtrar por imagem</param>
    /// <returns>Lista de containers</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ContainerListResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerListResponse>> ListContainers(
        [FromQuery] bool all = false,
        [FromQuery] ContainerEngine? engine = null,
        [FromQuery] ContainerStatus[]? status = null,
        [FromQuery] string? name = null,
        [FromQuery] string? image = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new ContainerListRequest
            {
                All = all,
                Engine = engine,
                Filters = new ContainerFilters
                {
                    Status = status?.ToList(),
                    Name = name,
                    Image = image
                }
            };

            var response = await _containerService.ListContainersAsync(request, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar containers");
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Obtém informações de um container específico
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Informações do container</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ContainerResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 404)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerResponse>> GetContainer(
        [FromRoute] string id,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.GetContainerAsync(id, engine, cancellationToken);
            
            if (!response.Success)
                return NotFound(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Executa um novo container
    /// </summary>
    /// <param name="request">Configurações do container</param>
    /// <returns>Informações do container criado</returns>
    [HttpPost("run")]
    [ProducesResponseType(typeof(ContainerRunResponse), 201)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerRunResponse>> RunContainer(
        [FromBody] ContainerRunRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseContainerResponse
                {
                    Success = false,
                    Message = "Dados inválidos",
                    Errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList()
                });
            }

            var response = await _containerService.RunContainerAsync(request, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return CreatedAtAction(nameof(GetContainer), new { id = response.ContainerId }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar container");
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Inicia um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Resultado da operação</returns>
    [HttpPost("{id}/start")]
    [ProducesResponseType(typeof(ContainerActionResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerActionResponse>> StartContainer(
        [FromRoute] string id,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.StartContainerAsync(id, engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao iniciar container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Para um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Resultado da operação</returns>
    [HttpPost("{id}/stop")]
    [ProducesResponseType(typeof(ContainerActionResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerActionResponse>> StopContainer(
        [FromRoute] string id,
        [FromQuery] int? timeout = null,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.StopContainerAsync(id, timeout, engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Reinicia um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="timeout">Timeout em segundos</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Resultado da operação</returns>
    [HttpPost("{id}/restart")]
    [ProducesResponseType(typeof(ContainerActionResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerActionResponse>> RestartContainer(
        [FromRoute] string id,
        [FromQuery] int? timeout = null,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.RestartContainerAsync(id, timeout, engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao reiniciar container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Remove um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Resultado da operação</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(typeof(ContainerActionResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerActionResponse>> RemoveContainer(
        [FromRoute] string id,
        [FromQuery] bool force = false,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.RemoveContainerAsync(id, force, engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Obtém logs de um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="tail">Número de linhas do final</param>
    /// <param name="follow">Seguir logs em tempo real</param>
    /// <param name="timestamps">Incluir timestamps</param>
    /// <param name="since">Data/hora de início</param>
    /// <param name="until">Data/hora de fim</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Logs do container</returns>
    [HttpGet("{id}/logs")]
    [ProducesResponseType(typeof(ContainerLogsResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerLogsResponse>> GetContainerLogs(
        [FromRoute] string id,
        [FromQuery] int? tail = null,
        [FromQuery] bool follow = false,
        [FromQuery] bool timestamps = true,
        [FromQuery] DateTime? since = null,
        [FromQuery] DateTime? until = null,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var request = new ContainerLogsRequest
            {
                ContainerId = id,
                Tail = tail,
                Follow = follow,
                Timestamps = timestamps,
                Since = since,
                Until = until,
                Engine = engine
            };

            var response = await _containerService.GetContainerLogsAsync(request, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter logs do container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Obtém estatísticas de um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <returns>Estatísticas do container</returns>
    [HttpGet("{id}/stats")]
    [ProducesResponseType(typeof(ContainerStatsResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerStatsResponse>> GetContainerStats(
        [FromRoute] string id,
        [FromQuery] ContainerEngine? engine = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _containerService.GetContainerStatsAsync(id, engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Executa um comando em um container
    /// </summary>
    /// <param name="id">ID do container</param>
    /// <param name="request">Parâmetros de execução</param>
    /// <returns>Resultado da execução</returns>
    [HttpPost("{id}/exec")]
    [ProducesResponseType(typeof(ContainerExecResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerExecResponse>> ExecContainer(
        [FromRoute] string id,
        [FromBody] ContainerExecRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseContainerResponse
                {
                    Success = false,
                    Message = "Dados inválidos",
                    Errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList()
                });
            }

            // Definir o ID do container no request
            request.ContainerId = id;

            var response = await _containerService.ExecContainerAsync(
                id, 
                request.Command, 
                request.Args?.ToArray(), 
                request.Interactive, 
                request.Tty, 
                request.Engine, 
                cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar comando no container {ContainerId}", id);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }
}
