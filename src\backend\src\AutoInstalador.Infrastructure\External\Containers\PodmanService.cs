using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Implementação do serviço Podman usando CLI
/// </summary>
public class PodmanService : IContainerEngine
{
    private readonly ILogger<PodmanService> _logger;
    private readonly ContainerCommandBuilder _commandBuilder;
    private readonly ContainerOutputParser _outputParser;

    public string EngineName => "podman";
    public ContainerEngine EngineType => ContainerEngine.Podman;

    public PodmanService(ILogger<PodmanService> logger)
    {
        _logger = logger;
        _commandBuilder = new ContainerCommandBuilder();
        _outputParser = new ContainerOutputParser();
    }

    public async Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await ExecuteCommandAsync("--version", timeout: 10, cancellationToken: cancellationToken);
            return result.Success && result.StandardOutput.Contains("podman version");
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Podman não está disponível");
            return false;
        }
    }

    public async Task<bool> IsRunningAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Podman não requer daemon, então verificamos se conseguimos executar comandos básicos
            var result = await ExecuteCommandAsync("info", timeout: 10, cancellationToken: cancellationToken);
            return result.Success;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Podman não está funcionando corretamente");
            return false;
        }
    }

    public async Task<ContainerEngineInfo> GetEngineInfoAsync(CancellationToken cancellationToken = default)
    {
        var info = new ContainerEngineInfo
        {
            Engine = ContainerEngine.Podman,
            IsAvailable = await IsAvailableAsync(cancellationToken),
            IsRunning = await IsRunningAsync(cancellationToken)
        };

        if (!info.IsAvailable)
            return info;

        try
        {
            // Obter versão
            var versionResult = await ExecuteCommandAsync("--version", cancellationToken: cancellationToken);
            if (versionResult.Success)
            {
                var versionMatch = Regex.Match(versionResult.StandardOutput, @"podman version (\d+\.\d+\.\d+)");
                if (versionMatch.Success)
                {
                    info.Version = versionMatch.Groups[1].Value;
                }
            }

            // Obter informações detalhadas
            if (info.IsRunning)
            {
                var infoResult = await ExecuteCommandAsync("info", new[] { "--format", "json" }, cancellationToken: cancellationToken);
                if (infoResult.Success)
                {
                    var podmanInfo = JsonSerializer.Deserialize<JsonElement>(infoResult.StandardOutput);
                    
                    if (podmanInfo.TryGetProperty("version", out var version))
                    {
                        if (version.TryGetProperty("Version", out var ver))
                            info.ApiVersion = ver.GetString() ?? "";
                    }

                    if (podmanInfo.TryGetProperty("store", out var store))
                    {
                        if (store.TryGetProperty("graphDriverName", out var driver))
                            info.StorageDriver = driver.GetString() ?? "";
                    }

                    if (podmanInfo.TryGetProperty("host", out var host))
                    {
                        if (host.TryGetProperty("cgroupVersion", out var cgroupVersion))
                            info.CgroupVersion = cgroupVersion.GetString() ?? "";

                        if (host.TryGetProperty("os", out var os))
                            info.Platform.Os = os.GetString() ?? "";

                        if (host.TryGetProperty("arch", out var arch))
                            info.Platform.Architecture = arch.GetString() ?? "";

                        if (host.TryGetProperty("hostname", out var hostname))
                            info.Platform.Name = hostname.GetString() ?? "";

                        // Verificar se é rootless
                        if (host.TryGetProperty("security", out var security))
                        {
                            if (security.TryGetProperty("rootless", out var rootless))
                                info.Rootless = rootless.GetBoolean();
                        }
                    }

                    // Informações do runtime
                    if (podmanInfo.TryGetProperty("host", out var hostInfo))
                    {
                        if (hostInfo.TryGetProperty("ociRuntime", out var ociRuntime))
                        {
                            if (ociRuntime.TryGetProperty("name", out var runtimeName))
                                info.Runtime.Name = runtimeName.GetString() ?? "";

                            if (ociRuntime.TryGetProperty("version", out var runtimeVersion))
                                info.Runtime.Version = runtimeVersion.GetString() ?? "";
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao obter informações detalhadas do Podman");
        }

        return info;
    }

    public async Task<IEnumerable<Container>> ListContainersAsync(bool all = false, Dictionary<string, string>? filters = null, CancellationToken cancellationToken = default)
    {
        var args = new List<string> { "ps", "--format", "json", "--no-trunc" };
        
        if (all)
            args.Add("--all");

        if (filters != null)
        {
            foreach (var filter in filters)
            {
                args.AddRange(new[] { "--filter", $"{filter.Key}={filter.Value}" });
            }
        }

        var result = await ExecuteCommandAsync("ps", args.Skip(1).ToArray(), cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogError("Erro ao listar containers: {Error}", result.StandardError);
            return Enumerable.Empty<Container>();
        }

        return _outputParser.ParseContainerList(result.StandardOutput, ContainerEngine.Podman);
    }

    public async Task<Container?> GetContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("inspect", new[] { containerId }, cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogWarning("Container {ContainerId} não encontrado", containerId);
            return null;
        }

        return _outputParser.ParseContainerInspect(result.StandardOutput, ContainerEngine.Podman);
    }

    public async Task<string> RunContainerAsync(ContainerRunOptions options, CancellationToken cancellationToken = default)
    {
        var args = _commandBuilder.BuildRunCommand(options, ContainerEngine.Podman);
        var result = await ExecuteCommandAsync("run", args, cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            throw new InvalidOperationException($"Erro ao executar container: {result.StandardError}");
        }

        // O Podman retorna o ID do container na saída padrão
        return result.StandardOutput.Trim();
    }

    public async Task<bool> StartContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("start", new[] { containerId }, cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> StopContainerAsync(string containerId, int? timeout = null, CancellationToken cancellationToken = default)
    {
        var args = new List<string> { containerId };
        if (timeout.HasValue)
        {
            args.InsertRange(0, new[] { "--time", timeout.Value.ToString() });
        }

        var result = await ExecuteCommandAsync("stop", args.ToArray(), cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> RestartContainerAsync(string containerId, int? timeout = null, CancellationToken cancellationToken = default)
    {
        var args = new List<string> { containerId };
        if (timeout.HasValue)
        {
            args.InsertRange(0, new[] { "--time", timeout.Value.ToString() });
        }

        var result = await ExecuteCommandAsync("restart", args.ToArray(), cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> PauseContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("pause", new[] { containerId }, cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> UnpauseContainerAsync(string containerId, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("unpause", new[] { containerId }, cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> RemoveContainerAsync(string containerId, bool force = false, CancellationToken cancellationToken = default)
    {
        var args = new List<string>();
        if (force)
            args.Add("--force");
        args.Add(containerId);

        var result = await ExecuteCommandAsync("rm", args.ToArray(), cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<IEnumerable<ContainerLog>> GetContainerLogsAsync(string containerId, int? tail = null, bool follow = false, bool timestamps = true, DateTime? since = null, DateTime? until = null, CancellationToken cancellationToken = default)
    {
        var args = new List<string>();
        
        if (timestamps)
            args.Add("--timestamps");
        
        if (tail.HasValue)
            args.AddRange(new[] { "--tail", tail.Value.ToString() });
        
        if (since.HasValue)
            args.AddRange(new[] { "--since", since.Value.ToString("yyyy-MM-ddTHH:mm:ssZ") });
        
        if (until.HasValue)
            args.AddRange(new[] { "--until", until.Value.ToString("yyyy-MM-ddTHH:mm:ssZ") });
        
        if (follow)
            args.Add("--follow");
        
        args.Add(containerId);

        var result = await ExecuteCommandAsync("logs", args.ToArray(), cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogError("Erro ao obter logs do container {ContainerId}: {Error}", containerId, result.StandardError);
            return Enumerable.Empty<ContainerLog>();
        }

        return _outputParser.ParseContainerLogs(result.StandardOutput, containerId);
    }

    public async Task<ContainerStats?> GetContainerStatsAsync(string containerId, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("stats", new[] { "--no-stream", "--format", "json", containerId }, cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogWarning("Erro ao obter estatísticas do container {ContainerId}: {Error}", containerId, result.StandardError);
            return null;
        }

        return _outputParser.ParseContainerStats(result.StandardOutput, containerId);
    }

    public async Task<ContainerExecResult> ExecContainerAsync(string containerId, string command, string[]? args = null, bool interactive = false, bool tty = false, CancellationToken cancellationToken = default)
    {
        var execArgs = new List<string>();
        
        if (interactive)
            execArgs.Add("--interactive");
        
        if (tty)
            execArgs.Add("--tty");
        
        execArgs.Add(containerId);
        execArgs.Add(command);
        
        if (args != null)
            execArgs.AddRange(args);

        var result = await ExecuteCommandAsync("exec", execArgs.ToArray(), cancellationToken: cancellationToken);
        
        return new ContainerExecResult
        {
            ExitCode = result.ExitCode,
            StandardOutput = result.StandardOutput,
            StandardError = result.StandardError,
            ContainerId = containerId,
            Command = command,
            Arguments = args ?? Array.Empty<string>(),
            Duration = result.Duration
        };
    }

    public async Task<IEnumerable<ContainerImage>> ListImagesAsync(CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("images", new[] { "--format", "json", "--no-trunc" }, cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogError("Erro ao listar imagens: {Error}", result.StandardError);
            return Enumerable.Empty<ContainerImage>();
        }

        return _outputParser.ParseImageList(result.StandardOutput, ContainerEngine.Podman);
    }

    public async Task<bool> PullImageAsync(string imageName, string? tag = null, CancellationToken cancellationToken = default)
    {
        var imageRef = string.IsNullOrEmpty(tag) ? imageName : $"{imageName}:{tag}";
        var result = await ExecuteCommandAsync("pull", new[] { imageRef }, cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<bool> RemoveImageAsync(string imageId, bool force = false, CancellationToken cancellationToken = default)
    {
        var args = new List<string>();
        if (force)
            args.Add("--force");
        args.Add(imageId);

        var result = await ExecuteCommandAsync("rmi", args.ToArray(), cancellationToken: cancellationToken);
        return result.Success;
    }

    public async Task<IEnumerable<ContainerImageSearchResult>> SearchImagesAsync(string searchTerm, int limit = 25, CancellationToken cancellationToken = default)
    {
        var result = await ExecuteCommandAsync("search", new[] { "--limit", limit.ToString(), searchTerm }, cancellationToken: cancellationToken);
        
        if (!result.Success)
        {
            _logger.LogError("Erro ao buscar imagens: {Error}", result.StandardError);
            return Enumerable.Empty<ContainerImageSearchResult>();
        }

        return _outputParser.ParseImageSearch(result.StandardOutput);
    }

    public async Task<CommandResult> ExecuteCommandAsync(string command, string[]? args = null, int timeout = 30, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var processStartInfo = new ProcessStartInfo
        {
            FileName = "podman",
            Arguments = command + (args != null ? " " + string.Join(" ", args) : ""),
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        _logger.LogDebug("Executando comando Podman: {Command}", processStartInfo.Arguments);

        try
        {
            using var process = new Process { StartInfo = processStartInfo };
            using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(timeout));
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            process.Start();

            var outputTask = process.StandardOutput.ReadToEndAsync();
            var errorTask = process.StandardError.ReadToEndAsync();

            await process.WaitForExitAsync(combinedCts.Token);

            var output = await outputTask;
            var error = await errorTask;
            var duration = DateTime.UtcNow - startTime;

            var result = new CommandResult
            {
                ExitCode = process.ExitCode,
                StandardOutput = output,
                StandardError = error,
                Duration = duration,
                Command = "podman " + command,
                Arguments = args ?? Array.Empty<string>()
            };

            if (!result.Success)
            {
                _logger.LogWarning("Comando Podman falhou: {Command}, Exit Code: {ExitCode}, Error: {Error}", 
                    processStartInfo.Arguments, result.ExitCode, result.StandardError);
            }

            return result;
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogWarning("Comando Podman cancelado: {Command}", processStartInfo.Arguments);
            throw;
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Comando Podman expirou após {Timeout}s: {Command}", timeout, processStartInfo.Arguments);
            throw new TimeoutException($"Comando Podman expirou após {timeout} segundos");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar comando Podman: {Command}", processStartInfo.Arguments);
            throw;
        }
    }
}
