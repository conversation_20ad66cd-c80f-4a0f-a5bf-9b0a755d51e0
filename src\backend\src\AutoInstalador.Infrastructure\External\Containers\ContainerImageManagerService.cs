using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Serviço gerenciador de imagens de container
/// </summary>
public class ContainerImageManagerService : IContainerImageService
{
    private readonly ILogger<ContainerImageManagerService> _logger;
    private readonly DockerService _dockerService;
    private readonly PodmanService _podmanService;

    public ContainerImageManagerService(
        ILogger<ContainerImageManagerService> logger,
        DockerService dockerService,
        PodmanService podmanService)
    {
        _logger = logger;
        _dockerService = dockerService;
        _podmanService = podmanService;
    }

    public async Task<ContainerImageListResponse> ListImagesAsync(ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerImageListResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                response.Errors.Add("Docker e Podman não estão disponíveis");
                return response;
            }

            var images = await containerEngine.ListImagesAsync(cancellationToken);

            response.Success = true;
            response.Images = images.Select(MapToDto).ToList();
            response.TotalCount = response.Images.Count;
            response.Engine = containerEngine.EngineType;
            response.Message = $"Listadas {response.TotalCount} imagens usando {containerEngine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar imagens");
            response.Success = false;
            response.Message = "Erro interno ao listar imagens";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerImagePullResponse> PullImageAsync(string imageName, string? tag = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerImagePullResponse();
        var startTime = DateTime.UtcNow;

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var success = await containerEngine.PullImageAsync(imageName, tag, cancellationToken);

            response.Success = success;
            response.ImageName = imageName;
            response.Tag = tag ?? "latest";
            response.Duration = DateTime.UtcNow - startTime;
            response.Message = success 
                ? $"Imagem {imageName}:{response.Tag} baixada com sucesso usando {containerEngine.EngineName}"
                : $"Falha ao baixar imagem {imageName}:{response.Tag}";

            if (!success)
            {
                response.Errors.Add($"Não foi possível baixar a imagem {imageName}:{response.Tag}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao baixar imagem {ImageName}:{Tag}", imageName, tag);
            response.Success = false;
            response.Duration = DateTime.UtcNow - startTime;
            response.Message = "Erro ao baixar imagem";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerImageRemoveResponse> RemoveImageAsync(string imageId, bool force = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerImageRemoveResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var success = await containerEngine.RemoveImageAsync(imageId, force, cancellationToken);

            response.Success = success;
            response.ImageId = imageId;
            response.Message = success 
                ? $"Imagem {imageId} removida com sucesso usando {containerEngine.EngineName}"
                : $"Falha ao remover imagem {imageId}";

            if (!success)
            {
                response.Errors.Add($"Não foi possível remover a imagem {imageId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover imagem {ImageId}", imageId);
            response.Success = false;
            response.Message = "Erro ao remover imagem";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerImageSearchResponse> SearchImagesAsync(string searchTerm, int limit = 25, ContainerEngine? engine = null, CancellationToken cancellationToken = default)
    {
        var response = new ContainerImageSearchResponse();

        try
        {
            var containerEngine = await GetAvailableEngineAsync(engine, cancellationToken);
            if (containerEngine == null)
            {
                response.Success = false;
                response.Message = "Nenhum engine de container disponível";
                return response;
            }

            var results = await containerEngine.SearchImagesAsync(searchTerm, limit, cancellationToken);

            response.Success = true;
            response.Results = results.Select(MapSearchResultToDto).ToList();
            response.SearchTerm = searchTerm;
            response.TotalResults = response.Results.Count;
            response.Message = $"Encontrados {response.TotalResults} resultados para '{searchTerm}' usando {containerEngine.EngineName}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar imagens com termo '{SearchTerm}'", searchTerm);
            response.Success = false;
            response.Message = "Erro ao buscar imagens";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    /// <summary>
    /// Obtém engine disponível com fallback automático
    /// </summary>
    private async Task<IContainerEngine?> GetAvailableEngineAsync(ContainerEngine? preferredEngine, CancellationToken cancellationToken)
    {
        // Se um engine específico foi solicitado, tentar usá-lo
        if (preferredEngine.HasValue)
        {
            var engine = GetEngine(preferredEngine.Value);
            if (await engine.IsAvailableAsync(cancellationToken))
                return engine;

            _logger.LogWarning("Engine preferido {Engine} não está disponível", preferredEngine.Value);
        }

        // Fallback: tentar Docker primeiro, depois Podman
        if (await _dockerService.IsAvailableAsync(cancellationToken))
        {
            _logger.LogDebug("Usando Docker como engine de container");
            return _dockerService;
        }

        if (await _podmanService.IsAvailableAsync(cancellationToken))
        {
            _logger.LogDebug("Usando Podman como engine de container");
            return _podmanService;
        }

        _logger.LogWarning("Nenhum engine de container disponível");
        return null;
    }

    /// <summary>
    /// Obtém engine específico
    /// </summary>
    private IContainerEngine GetEngine(ContainerEngine engine)
    {
        return engine switch
        {
            ContainerEngine.Docker => _dockerService,
            ContainerEngine.Podman => _podmanService,
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    /// <summary>
    /// Mappers
    /// </summary>
    private ContainerImageDto MapToDto(Core.Entities.ContainerImage image)
    {
        return new ContainerImageDto
        {
            Id = image.ImageId,
            Repository = image.Repository,
            Tag = image.Tag,
            Digest = image.Digest,
            Size = image.Size,
            Created = image.Created,
            Labels = image.Labels,
            Architecture = image.Architecture,
            Os = image.Os,
            Engine = image.Engine
        };
    }

    private ContainerImageSearchResultDto MapSearchResultToDto(Core.Entities.ContainerImageSearchResult result)
    {
        return new ContainerImageSearchResultDto
        {
            Name = result.Name,
            Description = result.Description,
            Stars = result.Stars,
            IsOfficial = result.IsOfficial,
            IsAutomated = result.IsAutomated
        };
    }
}
