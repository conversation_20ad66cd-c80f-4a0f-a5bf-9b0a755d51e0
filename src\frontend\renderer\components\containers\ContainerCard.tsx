/**
 * Container Card - Card individual de container
 * Auto-Instalador V3 Lite
 * 
 * @description Card que exibe informações e ações de um container individual
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { 
  useContainerAction, 
  useContainerStats, 
  useContainerLogs 
} from '../../services/container-service';
import { formatBytes, formatCpuPercent, formatUptime } from '../../utils/format';
import { toast } from 'react-hot-toast';
import type { Container, ContainerStatus } from '../../../../shared/types/api.types';

interface ContainerCardProps {
  container: Container;
  onSelect: (containerId: string) => void;
  isSelected: boolean;
  className?: string;
}

export const ContainerCard: React.FC<ContainerCardProps> = ({
  container,
  onSelect,
  isSelected,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Hooks para ações e dados
  const containerAction = useContainerAction();
  const { data: stats } = useContainerStats(container.id, container.engine);

  // Determinar cor do status
  const getStatusColor = (status: ContainerStatus): string => {
    switch (status) {
      case 'running':
        return 'bg-green-500';
      case 'exited':
      case 'dead':
        return 'bg-red-500';
      case 'paused':
        return 'bg-yellow-500';
      case 'restarting':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Determinar texto do status
  const getStatusText = (status: ContainerStatus): string => {
    switch (status) {
      case 'running':
        return 'Executando';
      case 'exited':
        return 'Parado';
      case 'paused':
        return 'Pausado';
      case 'restarting':
        return 'Reiniciando';
      case 'dead':
        return 'Morto';
      case 'created':
        return 'Criado';
      default:
        return 'Desconhecido';
    }
  };

  // Handlers para ações
  const handleAction = async (action: 'start' | 'stop' | 'restart' | 'pause' | 'unpause' | 'remove') => {
    try {
      await containerAction.mutateAsync({
        containerId: container.id,
        action,
        engine: container.engine
      });
      
      toast.success(`Container ${action === 'remove' ? 'removido' : 
                    action === 'start' ? 'iniciado' :
                    action === 'stop' ? 'parado' :
                    action === 'restart' ? 'reiniciado' :
                    action === 'pause' ? 'pausado' : 'despausado'} com sucesso`);
    } catch (error) {
      toast.error(`Erro ao ${action === 'remove' ? 'remover' : 
                   action === 'start' ? 'iniciar' :
                   action === 'stop' ? 'parar' :
                   action === 'restart' ? 'reiniciar' :
                   action === 'pause' ? 'pausar' : 'despausar'} container`);
    }
  };

  const handleCardClick = () => {
    onSelect(container.id);
    setIsExpanded(!isExpanded);
  };

  // Renderizar botões de ação baseados no status
  const renderActionButtons = () => {
    const isRunning = container.status === 'running';
    const isPaused = container.status === 'paused';
    const isStopped = container.status === 'exited' || container.status === 'created';

    return (
      <div className="flex gap-2 mt-4">
        {/* Logs sempre disponível */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleCardClick();
          }}
          className="px-3 py-1.5 text-xs font-medium text-gray-300 bg-transparent border border-gray-600 rounded hover:bg-gray-600 hover:text-white transition-colors"
        >
          Logs
        </button>

        {/* Exec apenas se estiver rodando */}
        {isRunning && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implementar exec
              toast.info('Funcionalidade exec em desenvolvimento');
            }}
            className="px-3 py-1.5 text-xs font-medium text-gray-300 bg-transparent border border-gray-600 rounded hover:bg-gray-600 hover:text-white transition-colors"
          >
            Exec
          </button>
        )}

        {/* Ações baseadas no status */}
        {isStopped && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAction('start');
            }}
            disabled={containerAction.isLoading}
            className="px-3 py-1.5 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            Iniciar
          </button>
        )}

        {isRunning && (
          <>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAction('restart');
              }}
              disabled={containerAction.isLoading}
              className="px-3 py-1.5 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              Reiniciar
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAction('pause');
              }}
              disabled={containerAction.isLoading}
              className="px-3 py-1.5 text-xs font-medium text-gray-300 bg-transparent border border-gray-600 rounded hover:bg-gray-600 hover:text-white disabled:opacity-50 transition-colors"
            >
              Pausar
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleAction('stop');
              }}
              disabled={containerAction.isLoading}
              className="px-3 py-1.5 text-xs font-medium text-white bg-red-600 rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
            >
              Parar
            </button>
          </>
        )}

        {isPaused && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAction('unpause');
            }}
            disabled={containerAction.isLoading}
            className="px-3 py-1.5 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            Continuar
          </button>
        )}

        {/* Remover sempre disponível para containers parados */}
        {!isRunning && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (confirm(`Tem certeza que deseja remover o container "${container.name}"?`)) {
                handleAction('remove');
              }
            }}
            disabled={containerAction.isLoading}
            className="px-3 py-1.5 text-xs font-medium text-white bg-red-600 rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
          >
            Remover
          </button>
        )}
      </div>
    );
  };

  return (
    <div 
      className={`
        bg-gray-700 border border-gray-600 rounded-lg p-5 transition-all duration-200 cursor-pointer
        hover:border-blue-500 hover:shadow-lg hover:shadow-blue-500/15
        ${isSelected ? 'border-blue-500 shadow-lg shadow-blue-500/15' : ''}
        ${className}
      `}
      onClick={handleCardClick}
    >
      {/* Header do container */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-semibold text-white truncate mb-1">
            {container.name}
          </h3>
          <div className="text-xs text-gray-400 font-mono truncate">
            {container.image}
          </div>
        </div>
        
        <div className="flex items-center gap-2 ml-4">
          <div className={`w-2 h-2 rounded-full ${getStatusColor(container.status)}`} />
          <span className="text-xs font-medium text-gray-300 uppercase tracking-wide">
            {getStatusText(container.status)}
          </span>
        </div>
      </div>

      {/* Métricas do container */}
      {stats && container.status === 'running' && (
        <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-800 rounded-md">
          <div className="text-center">
            <div className="text-sm font-semibold text-blue-400 font-mono">
              {formatCpuPercent(stats.cpu.usage)}
            </div>
            <div className="text-xs text-gray-500 uppercase tracking-wide mt-0.5">
              CPU
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm font-semibold text-blue-400 font-mono">
              {formatBytes(stats.memory.usage)}
            </div>
            <div className="text-xs text-gray-500 uppercase tracking-wide mt-0.5">
              Memory
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm font-semibold text-blue-400 font-mono">
              {formatBytes(stats.blockIO.read + stats.blockIO.write)}
            </div>
            <div className="text-xs text-gray-500 uppercase tracking-wide mt-0.5">
              Disk
            </div>
          </div>
        </div>
      )}

      {/* Mapeamento de portas */}
      {container.ports.length > 0 && (
        <div className="mb-4">
          <div className="text-xs text-gray-500 uppercase tracking-wide mb-2">
            Port Mappings
          </div>
          <div className="flex flex-wrap gap-1">
            {container.ports.slice(0, 3).map((port, index) => (
              <span 
                key={index}
                className="text-xs text-gray-300 bg-gray-800 px-2 py-1 rounded font-mono"
              >
                {port.hostPort ? `${port.hostPort}:` : ''}{port.containerPort}/{port.protocol}
              </span>
            ))}
            {container.ports.length > 3 && (
              <span className="text-xs text-gray-500">
                +{container.ports.length - 3} mais
              </span>
            )}
          </div>
        </div>
      )}

      {/* Botões de ação */}
      {renderActionButtons()}
    </div>
  );
};
