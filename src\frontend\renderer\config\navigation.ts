/**
 * Navigation Configuration
 * Auto-Instalador V3 Lite
 * 
 * @description Configuração de navegação da aplicação
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import { 
  HomeIcon,
  CubeIcon,
  CloudArrowDownIcon,
  CogIcon,
  ChartBarIcon,
  DocumentTextIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

// ============================================================================
// TYPES
// ============================================================================

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: string;
  description?: string;
  badge?: string;
  children?: NavigationItem[];
  disabled?: boolean;
  beta?: boolean;
}

// ============================================================================
// NAVIGATION CONFIGURATION
// ============================================================================

export const navigationItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Início',
    icon: HomeIcon,
    path: '/',
    description: 'Dashboard principal'
  },
  {
    id: 'packages',
    label: 'Pacotes',
    icon: CloudArrowDownIcon,
    path: '/packages',
    description: 'Instalar e gerenciar software'
  },
  {
    id: 'containers',
    label: 'Containers',
    icon: CubeIcon,
    path: '/containers',
    description: 'Gerenciar containers Docker e Podman',
    children: [
      {
        id: 'containers-overview',
        label: 'Visão Geral',
        icon: ChartBarIcon,
        path: '/containers',
        description: 'Status geral dos containers'
      },
      {
        id: 'containers-list',
        label: 'Lista de Containers',
        icon: CubeIcon,
        path: '/containers/list',
        description: 'Gerenciar containers existentes'
      },
      {
        id: 'containers-images',
        label: 'Imagens',
        icon: CloudArrowDownIcon,
        path: '/containers/images',
        description: 'Gerenciar imagens de container'
      },
      {
        id: 'containers-engines',
        label: 'Engines',
        icon: CogIcon,
        path: '/containers/engines',
        description: 'Configurar Docker e Podman'
      }
    ]
  },
  {
    id: 'monitoring',
    label: 'Monitoramento',
    icon: ChartBarIcon,
    path: '/monitoring',
    description: 'Estatísticas e logs do sistema',
    beta: true
  },
  {
    id: 'settings',
    label: 'Configurações',
    icon: CogIcon,
    path: '/settings',
    description: 'Configurações da aplicação'
  },
  {
    id: 'about',
    label: 'Sobre',
    icon: InformationCircleIcon,
    path: '/about',
    description: 'Informações sobre o Auto-Instalador'
  }
];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Encontra um item de navegação pelo ID
 */
export function findNavigationItem(id: string): NavigationItem | undefined {
  function search(items: NavigationItem[]): NavigationItem | undefined {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = search(item.children);
        if (found) return found;
      }
    }
    return undefined;
  }
  
  return search(navigationItems);
}

/**
 * Obtém o caminho de breadcrumb para um item
 */
export function getBreadcrumb(id: string): NavigationItem[] {
  function search(items: NavigationItem[], path: NavigationItem[] = []): NavigationItem[] | null {
    for (const item of items) {
      const currentPath = [...path, item];
      
      if (item.id === id) return currentPath;
      
      if (item.children) {
        const found = search(item.children, currentPath);
        if (found) return found;
      }
    }
    return null;
  }
  
  return search(navigationItems) || [];
}

/**
 * Verifica se um item está ativo baseado no path atual
 */
export function isItemActive(item: NavigationItem, currentPath: string): boolean {
  if (item.path === currentPath) return true;
  
  if (item.children) {
    return item.children.some(child => isItemActive(child, currentPath));
  }
  
  return false;
}

/**
 * Obtém todos os itens de navegação em uma lista plana
 */
export function getFlatNavigationItems(): NavigationItem[] {
  function flatten(items: NavigationItem[]): NavigationItem[] {
    const result: NavigationItem[] = [];
    
    for (const item of items) {
      result.push(item);
      if (item.children) {
        result.push(...flatten(item.children));
      }
    }
    
    return result;
  }
  
  return flatten(navigationItems);
}

/**
 * Filtra itens de navegação baseado em critérios
 */
export function filterNavigationItems(
  predicate: (item: NavigationItem) => boolean
): NavigationItem[] {
  function filter(items: NavigationItem[]): NavigationItem[] {
    const result: NavigationItem[] = [];
    
    for (const item of items) {
      if (predicate(item)) {
        const filteredItem = { ...item };
        
        if (item.children) {
          filteredItem.children = filter(item.children);
        }
        
        result.push(filteredItem);
      }
    }
    
    return result;
  }
  
  return filter(navigationItems);
}

// ============================================================================
// ROUTE CONFIGURATION
// ============================================================================

export interface RouteConfig {
  path: string;
  component: string;
  title: string;
  description?: string;
  requiresAuth?: boolean;
  preload?: string[];
}

export const routeConfigs: RouteConfig[] = [
  {
    path: '/',
    component: 'Dashboard',
    title: 'Auto-Instalador V3 Lite',
    description: 'Dashboard principal'
  },
  {
    path: '/packages',
    component: 'PackageManager',
    title: 'Gerenciador de Pacotes',
    description: 'Instalar e gerenciar software'
  },
  {
    path: '/containers',
    component: 'ContainerDashboard',
    title: 'Gerenciamento de Containers',
    description: 'Gerenciar containers Docker e Podman',
    preload: ['containerAPI']
  },
  {
    path: '/containers/list',
    component: 'ContainerList',
    title: 'Lista de Containers',
    description: 'Visualizar e gerenciar containers'
  },
  {
    path: '/containers/images',
    component: 'ContainerImages',
    title: 'Imagens de Container',
    description: 'Gerenciar imagens Docker e Podman'
  },
  {
    path: '/containers/engines',
    component: 'ContainerEngines',
    title: 'Engines de Container',
    description: 'Configurar Docker e Podman'
  },
  {
    path: '/monitoring',
    component: 'SystemMonitoring',
    title: 'Monitoramento do Sistema',
    description: 'Estatísticas e logs'
  },
  {
    path: '/settings',
    component: 'Settings',
    title: 'Configurações',
    description: 'Configurações da aplicação'
  },
  {
    path: '/about',
    component: 'About',
    title: 'Sobre',
    description: 'Informações sobre o Auto-Instalador'
  }
];

/**
 * Encontra configuração de rota pelo path
 */
export function findRouteConfig(path: string): RouteConfig | undefined {
  return routeConfigs.find(config => config.path === path);
}

/**
 * Gera título da página baseado na rota
 */
export function generatePageTitle(path: string): string {
  const config = findRouteConfig(path);
  const baseTitle = 'Auto-Instalador V3 Lite';
  
  if (!config || config.path === '/') {
    return baseTitle;
  }
  
  return `${config.title} - ${baseTitle}`;
}
