using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using System.Text;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Construtor de comandos CLI para engines de container
/// </summary>
public class ContainerCommandBuilder
{
    /// <summary>
    /// Constrói comando para executar container
    /// </summary>
    /// <param name="options">Opções de execução</param>
    /// <param name="engine">Engine de container</param>
    /// <returns>Array de argumentos</returns>
    public string[] BuildRunCommand(ContainerRunOptions options, ContainerEngine engine)
    {
        var args = new List<string>();

        // Modo detached
        if (options.Detached)
            args.Add("--detach");

        // Modo interativo
        if (options.Interactive)
            args.Add("--interactive");

        // TTY
        if (options.Tty)
            args.Add("--tty");

        // Remover após execução
        if (options.Remove)
            args.Add("--rm");

        // Nome do container
        if (!string.IsNullOrEmpty(options.Name))
        {
            args.Add("--name");
            args.Add(SanitizeArgument(options.Name));
        }

        // Portas
        foreach (var port in options.Ports)
        {
            args.Add("--publish");
            args.Add(BuildPortMapping(port));
        }

        // Volumes
        foreach (var volume in options.Volumes)
        {
            args.Add("--volume");
            args.Add(BuildVolumeMapping(volume));
        }

        // Variáveis de ambiente
        foreach (var env in options.Environment)
        {
            args.Add("--env");
            args.Add($"{SanitizeArgument(env.Key)}={SanitizeArgument(env.Value)}");
        }

        // Labels
        foreach (var label in options.Labels)
        {
            args.Add("--label");
            args.Add($"{SanitizeArgument(label.Key)}={SanitizeArgument(label.Value)}");
        }

        // Diretório de trabalho
        if (!string.IsNullOrEmpty(options.WorkingDir))
        {
            args.Add("--workdir");
            args.Add(SanitizeArgument(options.WorkingDir));
        }

        // Usuário
        if (!string.IsNullOrEmpty(options.User))
        {
            args.Add("--user");
            args.Add(SanitizeArgument(options.User));
        }

        // Modo de rede
        if (!string.IsNullOrEmpty(options.NetworkMode) && options.NetworkMode != "bridge")
        {
            args.Add("--network");
            args.Add(SanitizeArgument(options.NetworkMode));
        }

        // Política de reinicialização
        if (options.RestartPolicy != RestartPolicy.No)
        {
            args.Add("--restart");
            args.Add(GetRestartPolicyString(options.RestartPolicy));
        }

        // Recursos
        if (options.Resources != null)
        {
            AddResourceArguments(args, options.Resources, engine);
        }

        // Imagem
        args.Add(SanitizeArgument(options.Image));

        // Comando e argumentos
        if (!string.IsNullOrEmpty(options.Command))
        {
            args.Add(SanitizeArgument(options.Command));
            
            if (options.Args != null && options.Args.Length > 0)
            {
                args.AddRange(options.Args.Select(SanitizeArgument));
            }
        }

        return args.ToArray();
    }

    /// <summary>
    /// Constrói comando para listar containers
    /// </summary>
    /// <param name="all">Incluir containers parados</param>
    /// <param name="filters">Filtros</param>
    /// <param name="engine">Engine de container</param>
    /// <returns>Array de argumentos</returns>
    public string[] BuildListCommand(bool all, Dictionary<string, string>? filters, ContainerEngine engine)
    {
        var args = new List<string> { "--format", "json", "--no-trunc" };

        if (all)
            args.Add("--all");

        if (filters != null)
        {
            foreach (var filter in filters)
            {
                args.Add("--filter");
                args.Add($"{SanitizeArgument(filter.Key)}={SanitizeArgument(filter.Value)}");
            }
        }

        return args.ToArray();
    }

    /// <summary>
    /// Constrói comando para logs
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="tail">Número de linhas</param>
    /// <param name="follow">Seguir logs</param>
    /// <param name="timestamps">Incluir timestamps</param>
    /// <param name="since">Data de início</param>
    /// <param name="until">Data de fim</param>
    /// <returns>Array de argumentos</returns>
    public string[] BuildLogsCommand(string containerId, int? tail, bool follow, bool timestamps, DateTime? since, DateTime? until)
    {
        var args = new List<string>();

        if (timestamps)
            args.Add("--timestamps");

        if (tail.HasValue)
        {
            args.Add("--tail");
            args.Add(tail.Value.ToString());
        }

        if (since.HasValue)
        {
            args.Add("--since");
            args.Add(since.Value.ToString("yyyy-MM-ddTHH:mm:ssZ"));
        }

        if (until.HasValue)
        {
            args.Add("--until");
            args.Add(until.Value.ToString("yyyy-MM-ddTHH:mm:ssZ"));
        }

        if (follow)
            args.Add("--follow");

        args.Add(SanitizeArgument(containerId));

        return args.ToArray();
    }

    /// <summary>
    /// Constrói comando para estatísticas
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="stream">Modo stream</param>
    /// <returns>Array de argumentos</returns>
    public string[] BuildStatsCommand(string containerId, bool stream = false)
    {
        var args = new List<string> { "--format", "json" };

        if (!stream)
            args.Add("--no-stream");

        args.Add(SanitizeArgument(containerId));

        return args.ToArray();
    }

    /// <summary>
    /// Constrói comando para execução
    /// </summary>
    /// <param name="containerId">ID do container</param>
    /// <param name="command">Comando</param>
    /// <param name="args">Argumentos</param>
    /// <param name="interactive">Modo interativo</param>
    /// <param name="tty">TTY</param>
    /// <returns>Array de argumentos</returns>
    public string[] BuildExecCommand(string containerId, string command, string[]? args, bool interactive, bool tty)
    {
        var execArgs = new List<string>();

        if (interactive)
            execArgs.Add("--interactive");

        if (tty)
            execArgs.Add("--tty");

        execArgs.Add(SanitizeArgument(containerId));
        execArgs.Add(SanitizeArgument(command));

        if (args != null)
            execArgs.AddRange(args.Select(SanitizeArgument));

        return execArgs.ToArray();
    }

    /// <summary>
    /// Constrói mapeamento de porta
    /// </summary>
    private string BuildPortMapping(ContainerPort port)
    {
        var mapping = new StringBuilder();

        if (!string.IsNullOrEmpty(port.HostIp))
        {
            mapping.Append(port.HostIp);
            mapping.Append(':');
        }

        if (port.HostPort.HasValue)
        {
            mapping.Append(port.HostPort.Value);
            mapping.Append(':');
        }

        mapping.Append(port.ContainerPortNumber);

        if (port.Protocol == NetworkProtocol.Udp)
        {
            mapping.Append("/udp");
        }

        return mapping.ToString();
    }

    /// <summary>
    /// Constrói mapeamento de volume
    /// </summary>
    private string BuildVolumeMapping(ContainerVolume volume)
    {
        var mapping = new StringBuilder();
        mapping.Append(SanitizeArgument(volume.Source));
        mapping.Append(':');
        mapping.Append(SanitizeArgument(volume.Destination));

        if (volume.Mode == VolumeMode.ReadOnly)
        {
            mapping.Append(":ro");
        }

        return mapping.ToString();
    }

    /// <summary>
    /// Adiciona argumentos de recursos
    /// </summary>
    private void AddResourceArguments(List<string> args, ContainerResources resources, ContainerEngine engine)
    {
        if (resources.Memory.HasValue)
        {
            args.Add("--memory");
            args.Add(resources.Memory.Value.ToString());
        }

        if (resources.MemorySwap.HasValue)
        {
            args.Add("--memory-swap");
            args.Add(resources.MemorySwap.Value.ToString());
        }

        if (resources.CpuShares.HasValue)
        {
            args.Add("--cpu-shares");
            args.Add(resources.CpuShares.Value.ToString());
        }

        if (resources.CpuQuota.HasValue && resources.CpuPeriod.HasValue)
        {
            args.Add("--cpu-quota");
            args.Add(resources.CpuQuota.Value.ToString());
            args.Add("--cpu-period");
            args.Add(resources.CpuPeriod.Value.ToString());
        }

        if (!string.IsNullOrEmpty(resources.CpusetCpus))
        {
            args.Add("--cpuset-cpus");
            args.Add(SanitizeArgument(resources.CpusetCpus));
        }

        if (!string.IsNullOrEmpty(resources.CpusetMems))
        {
            args.Add("--cpuset-mems");
            args.Add(SanitizeArgument(resources.CpusetMems));
        }

        if (resources.BlkioWeight.HasValue)
        {
            args.Add("--blkio-weight");
            args.Add(resources.BlkioWeight.Value.ToString());
        }

        if (resources.OomKillDisable.HasValue && resources.OomKillDisable.Value)
        {
            args.Add("--oom-kill-disable");
        }
    }

    /// <summary>
    /// Converte política de reinicialização para string
    /// </summary>
    private string GetRestartPolicyString(RestartPolicy policy)
    {
        return policy switch
        {
            RestartPolicy.Always => "always",
            RestartPolicy.OnFailure => "on-failure",
            RestartPolicy.UnlessStopped => "unless-stopped",
            _ => "no"
        };
    }

    /// <summary>
    /// Sanitiza argumento para evitar command injection
    /// </summary>
    private string SanitizeArgument(string argument)
    {
        if (string.IsNullOrEmpty(argument))
            return argument;

        // Remove caracteres perigosos
        var sanitized = argument
            .Replace(";", "")
            .Replace("&", "")
            .Replace("|", "")
            .Replace("`", "")
            .Replace("$", "")
            .Replace("(", "")
            .Replace(")", "")
            .Replace("<", "")
            .Replace(">", "");

        // Se contém espaços, adiciona aspas
        if (sanitized.Contains(' ') && !sanitized.StartsWith('"') && !sanitized.EndsWith('"'))
        {
            sanitized = $"\"{sanitized}\"";
        }

        return sanitized;
    }
}
