/**
 * Container Sidebar - Sidebar de navegação do dashboard de containers
 * Auto-Instalador V3 Lite
 * 
 * @description Sidebar lateral com navegação e configurações do gerenciamento de containers
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { useContainerEngines } from '../../services/container-service';
import type { ContainerEngine } from '../../../../shared/types/api.types';

interface ContainerSidebarProps {
  activeSection: string;
  selectedEngine: ContainerEngine;
  onEngineChange: (engine: ContainerEngine) => void;
  className?: string;
}

export const ContainerSidebar: React.FC<ContainerSidebarProps> = ({
  activeSection,
  selectedEngine,
  onEngineChange,
  className = ''
}) => {
  // Hook para obter informações dos engines
  const { data: engines } = useContainerEngines();

  // Seções da sidebar
  const sections = [
    {
      title: 'Principal',
      items: [
        { id: 'containers', label: 'Containers', icon: '📦', path: '/containers' },
        { id: 'images', label: 'Imagens', icon: '🖼️', path: '/containers/images' },
        { id: 'volumes', label: 'Volumes', icon: '💾', path: '/containers/volumes' }
      ]
    },
    {
      title: 'Automação',
      items: [
        { id: 'templates', label: 'Templates', icon: '📋', path: '/containers/templates' },
        { id: 'workflows', label: 'Workflows', icon: '⚡', path: '/containers/workflows' },
        { id: 'scheduler', label: 'Scheduler', icon: '⏰', path: '/containers/scheduler' }
      ]
    },
    {
      title: 'Monitoramento',
      items: [
        { id: 'logs', label: 'Logs', icon: '📄', path: '/containers/logs' },
        { id: 'metrics', label: 'Métricas', icon: '📊', path: '/containers/metrics' },
        { id: 'alerts', label: 'Alertas', icon: '🚨', path: '/containers/alerts' }
      ]
    }
  ];

  // Handler para mudança de engine
  const handleEngineChange = (engine: ContainerEngine) => {
    onEngineChange(engine);
  };

  // Obter status do engine
  const getEngineStatus = (engine: ContainerEngine) => {
    const engineInfo = engines?.find(e => e.engine === engine);
    if (!engineInfo) return { available: false, running: false };
    return {
      available: engineInfo.isAvailable,
      running: engineInfo.isRunning
    };
  };

  return (
    <aside className={`w-70 bg-gray-700 border-r border-gray-600 overflow-y-auto ${className}`}>
      <div className="p-5">
        {/* Seletor de Engine */}
        <div className="mb-6">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3">
            Container Engine
          </div>
          <div className="space-y-2">
            {(['docker', 'podman'] as ContainerEngine[]).map((engine) => {
              const status = getEngineStatus(engine);
              const isSelected = selectedEngine === engine;
              
              return (
                <button
                  key={engine}
                  onClick={() => handleEngineChange(engine)}
                  disabled={!status.available}
                  className={`
                    w-full flex items-center justify-between p-3 rounded-md text-sm font-medium transition-all duration-200
                    ${isSelected 
                      ? 'bg-blue-600 text-white' 
                      : status.available 
                        ? 'bg-gray-800 text-gray-300 hover:bg-gray-600 hover:text-white' 
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed opacity-50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">
                      {engine === 'docker' ? '🐳' : '🦭'}
                    </span>
                    <span className="capitalize">{engine}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {status.available && (
                      <div className={`w-2 h-2 rounded-full ${
                        status.running ? 'bg-green-500' : 'bg-yellow-500'
                      }`} />
                    )}
                    {!status.available && (
                      <div className="w-2 h-2 rounded-full bg-red-500" />
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Seções de navegação */}
        {sections.map((section) => (
          <div key={section.title} className="mb-6">
            <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-3">
              {section.title}
            </div>
            <nav className="space-y-1">
              {section.items.map((item) => {
                const isActive = activeSection === item.id;
                
                return (
                  <a
                    key={item.id}
                    href={item.path}
                    className={`
                      flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                      border-l-3 border-transparent
                      ${isActive 
                        ? 'bg-blue-900 text-blue-400 border-l-blue-400' 
                        : 'text-gray-300 hover:bg-gray-600 hover:text-white'
                      }
                    `}
                  >
                    <span className="text-base">{item.icon}</span>
                    {item.label}
                  </a>
                );
              })}
            </nav>
          </div>
        ))}

        {/* Informações do engine selecionado */}
        {engines && (
          <div className="mt-8 p-4 bg-gray-800 rounded-md">
            <div className="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">
              Engine Info
            </div>
            {(() => {
              const engineInfo = engines.find(e => e.engine === selectedEngine);
              if (!engineInfo) {
                return (
                  <div className="text-xs text-gray-500">
                    Engine não disponível
                  </div>
                );
              }
              
              return (
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Versão:</span>
                    <span className="text-gray-300 font-mono">{engineInfo.version}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">API:</span>
                    <span className="text-gray-300 font-mono">{engineInfo.apiVersion}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span className={`font-medium ${
                      engineInfo.isRunning ? 'text-green-400' : 'text-yellow-400'
                    }`}>
                      {engineInfo.isRunning ? 'Ativo' : 'Inativo'}
                    </span>
                  </div>
                  {engineInfo.rootless && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Modo:</span>
                      <span className="text-blue-400 font-medium">Rootless</span>
                    </div>
                  )}
                </div>
              );
            })()}
          </div>
        )}
      </div>
    </aside>
  );
};
