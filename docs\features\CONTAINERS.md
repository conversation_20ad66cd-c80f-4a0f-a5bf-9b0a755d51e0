# 🐳 Container Management - Auto-Instalador V3 Lite

## 📋 **Visão Geral**

O sistema de gerenciamento de containers do Auto-Instalador V3 Lite oferece uma interface unificada para trabalhar com Docker e Podman, permitindo aos usuários gerenciar containers de forma intuitiva através de uma interface gráfica moderna.

## 🎯 **Funcionalidades Principais**

### **1. Detecção Automática de Engines**
- ✅ Detecção automática de Docker e Podman
- ✅ Verificação de status e versões
- ✅ Fallback inteligente entre engines
- ✅ Suporte a instalação automática

### **2. Gerenciamento de Containers**
- ✅ Listagem de containers (ativos e parados)
- ✅ Criação de novos containers
- ✅ Controle de ciclo de vida (start, stop, restart, pause)
- ✅ Remoção de containers
- ✅ Execução de comandos em containers

### **3. Monitoramento em Tempo Real**
- ✅ Estatísticas de CPU, memória e rede
- ✅ Logs em tempo real
- ✅ Notificações de mudanças de status
- ✅ Auto-refresh configurável

### **4. Gerenciamento de Imagens**
- ✅ Listagem de imagens locais
- ✅ Download de imagens de registries
- ✅ Busca de imagens públicas
- ✅ Remoção de imagens não utilizadas

## 🏗️ **Arquitetura**

### **Backend (.NET 8.0)**

```
AutoInstalador.Infrastructure/External/Containers/
├── 📄 IContainerEngine.cs              # Interface base para engines
├── 📄 DockerService.cs                 # Implementação Docker
├── 📄 PodmanService.cs                 # Implementação Podman
├── 📄 ContainerManagerService.cs       # Serviço principal
├── 📄 ContainerImageManagerService.cs  # Gerenciamento de imagens
├── 📄 ContainerEngineManagerService.cs # Gerenciamento de engines
└── 📄 ContainerEngineDetector.cs       # Detecção e instalação
```

### **Frontend (React + TypeScript)**

```
src/frontend/renderer/components/features/containers/
├── 📄 ContainerDashboard.tsx           # Dashboard principal
├── 📄 ContainerList.tsx                # Lista de containers
├── 📄 ContainerStats.tsx               # Estatísticas em tempo real
├── 📄 ContainerLogs.tsx                # Visualização de logs
├── 📄 ContainerControls.tsx            # Controles de ação
└── 📄 index.ts                         # Exportações
```

### **Electron Bridge**

```
src/frontend/electron/
├── 📄 preload/container-bridge.ts      # Bridge seguro para renderer
└── 📄 main/handlers/container-handlers.ts # Handlers IPC
```

## 🔧 **Configuração e Uso**

### **1. Configuração do Backend**

```csharp
// Program.cs
builder.Services.AddContainerServices();
builder.Services.AddContainerHealthChecks();
```

### **2. Uso no Frontend**

```typescript
import { ContainerDashboard } from '@/components/features/containers';

function App() {
  return (
    <div className="h-screen">
      <ContainerDashboard />
    </div>
  );
}
```

### **3. Hooks Disponíveis**

```typescript
// Listagem de containers
const { data: containers, isLoading } = useContainers({
  all: true,
  filters: { status: ['running'] }
});

// Ações de container
const containerAction = useContainerAction();
await containerAction.mutateAsync({
  id: 'container-id',
  action: 'start',
  engine: 'docker'
});

// Estatísticas em tempo real
const { data: stats } = useContainerStats('container-id', 'docker');

// Logs
const { data: logs } = useContainerLogs({
  containerId: 'container-id',
  tail: 100,
  timestamps: true
});
```

## 📊 **API Endpoints**

### **Container Management**
- `GET /api/containers` - Lista containers
- `GET /api/containers/{id}` - Detalhes do container
- `POST /api/containers/run` - Executa novo container
- `POST /api/containers/{id}/start` - Inicia container
- `POST /api/containers/{id}/stop` - Para container
- `POST /api/containers/{id}/restart` - Reinicia container
- `DELETE /api/containers/{id}` - Remove container

### **Container Monitoring**
- `GET /api/containers/{id}/logs` - Logs do container
- `GET /api/containers/{id}/stats` - Estatísticas
- `POST /api/containers/{id}/exec` - Executa comando

### **Engine Management**
- `GET /api/containers/engines` - Lista engines
- `GET /api/containers/engines/{engine}/status` - Status do engine
- `POST /api/containers/engines/install` - Instala engine
- `GET /api/containers/engines/detect` - Detecta engines

## 🎨 **Interface do Usuário**

### **Dashboard Principal**
- **Sidebar de Navegação:** Acesso rápido às diferentes seções
- **Visão Geral:** Status consolidado de containers e engines
- **Lista de Containers:** Gerenciamento visual com filtros
- **Estatísticas:** Gráficos em tempo real de uso de recursos
- **Logs:** Visualização de logs com busca e filtros

### **Controles de Container**
- **Botões de Ação:** Start, Stop, Restart, Pause, Remove
- **Indicadores de Status:** Visual claro do estado atual
- **Ações Contextuais:** Terminal, Logs, Estatísticas
- **Confirmações:** Diálogos para ações destrutivas

### **Filtros e Busca**
- **Busca por Nome/Imagem:** Filtro em tempo real
- **Filtro por Status:** Running, Stopped, Paused, etc.
- **Filtro por Engine:** Docker, Podman, ou todos
- **Mostrar Parados:** Toggle para containers inativos

## 🔒 **Segurança**

### **Validação de Entrada**
- Validação de IDs de container
- Sanitização de comandos exec
- Verificação de permissões

### **Isolamento de Processos**
- Execução segura via Electron IPC
- Timeout em operações longas
- Tratamento de erros robusto

## 🧪 **Testes**

### **Testes Unitários**
```bash
# Backend
cd src/backend
dotnet test tests/AutoInstalador.UnitTests/Services/ContainerServiceTests.cs

# Frontend
cd src/frontend
npm test -- containers
```

### **Testes de Integração**
```bash
# API Integration Tests
cd src/backend
dotnet test tests/AutoInstalador.IntegrationTests/Controllers/ContainersControllerTests.cs
```

## 📈 **Monitoramento**

### **Métricas Coletadas**
- **CPU:** Uso percentual e número de cores
- **Memória:** Uso, limite, cache e swap
- **Rede:** Bytes RX/TX, pacotes
- **Disco:** Operações de leitura/escrita
- **Processos:** Número de PIDs

### **Health Checks**
- Status dos engines de container
- Conectividade com APIs
- Disponibilidade de recursos

## 🚀 **Instalação de Engines**

### **Suporte Multi-Plataforma**

#### **Windows**
```powershell
# Docker Desktop
winget install Docker.DockerDesktop
# ou
choco install docker-desktop -y

# Podman
winget install RedHat.Podman
# ou
choco install podman -y
```

#### **macOS**
```bash
# Docker
brew install --cask docker

# Podman
brew install podman
```

#### **Linux**
```bash
# Docker (Ubuntu/Debian)
apt-get update && apt-get install -y docker.io

# Podman (Ubuntu/Debian)
apt-get update && apt-get install -y podman
```

## 🔄 **Eventos em Tempo Real**

### **Eventos Suportados**
- **Container Status Changed:** Mudanças de estado
- **Container Stats Update:** Atualizações de estatísticas
- **Engine Status Changed:** Disponibilidade de engines

### **Implementação**
```typescript
// Setup de eventos
useContainerEvents(); // Hook automático

// Eventos manuais
const unsubscribe = window.containerAPI.onContainerStatusChanged((data) => {
  console.log(`Container ${data.containerId} mudou para ${data.status}`);
});
```

## 🎯 **Próximos Passos**

### **Funcionalidades Planejadas**
- [ ] Compose file support
- [ ] Container networking management
- [ ] Volume management interface
- [ ] Registry authentication
- [ ] Container templates/presets
- [ ] Backup and restore
- [ ] Performance optimization suggestions

### **Melhorias de UX**
- [ ] Drag & drop para arquivos
- [ ] Terminal integrado
- [ ] Gráficos de performance
- [ ] Notificações desktop
- [ ] Atalhos de teclado
- [ ] Temas personalizáveis

---

## 📚 **Referências**

- [Docker API Documentation](https://docs.docker.com/engine/api/)
- [Podman API Documentation](https://docs.podman.io/en/latest/markdown/podman-system-service.1.html)
- [React Query Documentation](https://tanstack.com/query/latest)
- [Electron IPC Documentation](https://www.electronjs.org/docs/latest/tutorial/ipc)
