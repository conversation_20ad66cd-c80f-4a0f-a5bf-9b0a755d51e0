using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace AutoInstalador.Infrastructure.External.Containers;

/// <summary>
/// Serviço gerenciador de engines de container
/// </summary>
public class ContainerEngineManagerService : IContainerEngineService
{
    private readonly ILogger<ContainerEngineManagerService> _logger;
    private readonly DockerService _dockerService;
    private readonly PodmanService _podmanService;
    private readonly ContainerEngineDetector _engineDetector;

    public ContainerEngineManagerService(
        ILogger<ContainerEngineManagerService> logger,
        DockerService dockerService,
        PodmanService podmanService,
        ContainerEngineDetector engineDetector)
    {
        _logger = logger;
        _dockerService = dockerService;
        _podmanService = podmanService;
        _engineDetector = engineDetector;
    }

    public async Task<ContainerEngineListResponse> ListEnginesAsync(CancellationToken cancellationToken = default)
    {
        var response = new ContainerEngineListResponse();

        try
        {
            var engines = new List<ContainerEngineInfoDto>();

            // Verificar Docker
            var dockerInfo = await _dockerService.GetEngineInfoAsync(cancellationToken);
            engines.Add(MapToDto(dockerInfo));

            // Verificar Podman
            var podmanInfo = await _podmanService.GetEngineInfoAsync(cancellationToken);
            engines.Add(MapToDto(podmanInfo));

            response.Success = true;
            response.Engines = engines;
            response.Message = $"Listados {engines.Count} engines de container";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar engines de container");
            response.Success = false;
            response.Message = "Erro interno ao listar engines";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerEngineStatusResponse> GetEngineStatusAsync(ContainerEngine engine, CancellationToken cancellationToken = default)
    {
        var response = new ContainerEngineStatusResponse
        {
            Engine = engine
        };

        try
        {
            var containerEngine = GetEngine(engine);
            var engineInfo = await containerEngine.GetEngineInfoAsync(cancellationToken);

            response.Success = true;
            response.IsAvailable = engineInfo.IsAvailable;
            response.IsRunning = engineInfo.IsRunning;
            response.Version = engineInfo.Version;
            response.Message = engineInfo.IsAvailable 
                ? $"{engine} está disponível" + (engineInfo.IsRunning ? " e em execução" : " mas não está em execução")
                : $"{engine} não está disponível";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter status do engine {Engine}", engine);
            response.Success = false;
            response.Message = $"Erro ao verificar status do {engine}";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerEngineInfoResponse> GetEngineInfoAsync(ContainerEngine engine, CancellationToken cancellationToken = default)
    {
        var response = new ContainerEngineInfoResponse();

        try
        {
            var containerEngine = GetEngine(engine);
            var engineInfo = await containerEngine.GetEngineInfoAsync(cancellationToken);

            response.Success = true;
            response.EngineInfo = MapToDto(engineInfo);
            response.Message = $"Informações do {engine} obtidas com sucesso";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter informações do engine {Engine}", engine);
            response.Success = false;
            response.Message = $"Erro ao obter informações do {engine}";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerEngineInstallResponse> InstallEngineAsync(ContainerEngineInstallRequest request, CancellationToken cancellationToken = default)
    {
        var response = new ContainerEngineInstallResponse
        {
            Engine = request.Engine
        };

        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Iniciando instalação do {Engine} na plataforma {Platform}", request.Engine, request.Platform);

            var success = await _engineDetector.InstallEngineAsync(
                request.Engine, 
                request.Platform.ToString(), 
                request.PackageManager?.ToString(), 
                request.Force, 
                cancellationToken);

            response.Success = success;
            response.InstallDuration = DateTime.UtcNow - startTime;

            if (success)
            {
                // Verificar versão instalada
                try
                {
                    var containerEngine = GetEngine(request.Engine);
                    var engineInfo = await containerEngine.GetEngineInfoAsync(cancellationToken);
                    response.InstalledVersion = engineInfo.Version;
                }
                catch
                {
                    // Ignorar erro ao obter versão
                }

                response.Message = $"{request.Engine} instalado com sucesso";
                response.RequiresRestart = request.Platform == Platform.Windows;

                // Obter comandos executados
                var commands = _engineDetector.GetInstallCommands(
                    request.Engine, 
                    request.Platform.ToString(), 
                    request.PackageManager?.ToString());
                response.ExecutedCommands = commands.ToList();
            }
            else
            {
                response.Message = $"Falha na instalação do {request.Engine}";
                response.Errors.Add($"A instalação do {request.Engine} não foi concluída com sucesso");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao instalar {Engine}", request.Engine);
            response.Success = false;
            response.InstallDuration = DateTime.UtcNow - startTime;
            response.Message = $"Erro durante a instalação do {request.Engine}";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    public async Task<ContainerEngineDetectionResponse> DetectEnginesAsync(CancellationToken cancellationToken = default)
    {
        var response = new ContainerEngineDetectionResponse();

        try
        {
            var detectedEngines = await _engineDetector.DetectEnginesAsync(cancellationToken);

            response.Success = true;
            response.DetectedEngines = detectedEngines.Select(MapDetectionResultToDto).ToList();

            // Determinar engine recomendado
            var availableEngines = detectedEngines.Where(e => e.IsAvailable).ToList();
            if (availableEngines.Any())
            {
                // Preferir Docker se disponível, senão Podman
                var dockerEngine = availableEngines.FirstOrDefault(e => e.Engine == ContainerEngine.Docker);
                response.RecommendedEngine = dockerEngine?.Engine ?? availableEngines.First().Engine;
            }

            var availableCount = response.DetectedEngines.Count(e => e.IsDetected);
            response.Message = $"Detectados {availableCount} engines de container disponíveis";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao detectar engines de container");
            response.Success = false;
            response.Message = "Erro ao detectar engines de container";
            response.Errors.Add(ex.Message);
        }

        return response;
    }

    /// <summary>
    /// Obtém engine específico
    /// </summary>
    private IContainerEngine GetEngine(ContainerEngine engine)
    {
        return engine switch
        {
            ContainerEngine.Docker => _dockerService,
            ContainerEngine.Podman => _podmanService,
            _ => throw new ArgumentException($"Engine não suportado: {engine}")
        };
    }

    /// <summary>
    /// Mappers
    /// </summary>
    private ContainerEngineInfoDto MapToDto(Core.Entities.ContainerEngineInfo engineInfo)
    {
        return new ContainerEngineInfoDto
        {
            Engine = engineInfo.Engine,
            Version = engineInfo.Version,
            ApiVersion = engineInfo.ApiVersion,
            IsAvailable = engineInfo.IsAvailable,
            IsRunning = engineInfo.IsRunning,
            Rootless = engineInfo.Rootless,
            StorageDriver = engineInfo.StorageDriver,
            CgroupVersion = engineInfo.CgroupVersion,
            Platform = new PlatformInfoDto
            {
                Name = engineInfo.Platform.Name,
                Architecture = engineInfo.Platform.Architecture,
                Os = engineInfo.Platform.Os
            },
            Runtime = new RuntimeInfoDto
            {
                Name = engineInfo.Runtime.Name,
                Version = engineInfo.Runtime.Version
            }
        };
    }

    private EngineDetectionResultDto MapDetectionResultToDto(Core.Entities.ContainerEngineInfo engineInfo)
    {
        return new EngineDetectionResultDto
        {
            Engine = engineInfo.Engine,
            IsDetected = engineInfo.IsAvailable,
            Version = engineInfo.Version,
            ExecutablePath = null, // Não temos essa informação no ContainerEngineInfo
            IsRunning = engineInfo.IsRunning,
            ErrorMessage = engineInfo.IsAvailable ? null : "Engine não encontrado ou não está funcionando"
        };
    }
}
