<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Instalador - Docker Desktop Pro Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1E1E1E;
            color: #FFFFFF;
            line-height: 1.5;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Top Navigation */
        .top-nav {
            background-color: #2D3748;
            border-bottom: 1px solid #4A5568;
            padding: 0;
            display: flex;
            align-items: center;
        }

        .nav-tabs {
            display: flex;
            flex: 1;
        }

        .nav-tab {
            padding: 12px 20px;
            background: none;
            border: none;
            color: #A0AEC0;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-right: 1px solid #4A5568;
            transition: all 0.2s ease;
        }

        .nav-tab:hover {
            background-color: #4A5568;
            color: #FFFFFF;
        }

        .nav-tab.active {
            background-color: #2496ED;
            color: #FFFFFF;
        }

        .nav-actions {
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 12px;
        }

        /* Main Layout */
        .main-layout {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background-color: #2D3748;
            border-right: 1px solid #4A5568;
            padding: 20px 0;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-title {
            padding: 0 20px;
            font-size: 12px;
            font-weight: 600;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: #A0AEC0;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-item:hover {
            background-color: #4A5568;
            color: #FFFFFF;
        }

        .sidebar-item.active {
            background-color: #2A4A6B;
            color: #2496ED;
            border-left-color: #2496ED;
        }

        .sidebar-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            fill: currentColor;
        }

        /* Content Area */
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background-color: #2D3748;
            border-bottom: 1px solid #4A5568;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .content-main {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background-color: #1A202C;
        }

        /* Container Cards */
        .containers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }

        .container-card {
            background-color: #2D3748;
            border: 1px solid #4A5568;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s ease;
        }

        .container-card:hover {
            border-color: #2496ED;
            box-shadow: 0 4px 12px rgba(36, 150, 237, 0.15);
        }

        .container-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .container-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 4px;
        }

        .container-image {
            font-size: 12px;
            color: #718096;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .container-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-running {
            background-color: #48BB78;
        }

        .status-stopped {
            background-color: #F56565;
        }

        .status-warning {
            background-color: #ED8936;
        }

        .status-text {
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .container-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #1A202C;
            border-radius: 6px;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 14px;
            font-weight: 600;
            color: #2496ED;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .metric-label {
            font-size: 10px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 2px;
        }

        .container-ports {
            margin-bottom: 16px;
        }

        .ports-title {
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .port-mapping {
            font-size: 12px;
            color: #A0AEC0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            background-color: #1A202C;
            padding: 6px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .container-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background-color: #2496ED;
            color: #FFFFFF;
        }

        .btn-primary:hover {
            background-color: #1A7BC7;
        }

        .btn-secondary {
            background-color: transparent;
            color: #A0AEC0;
            border: 1px solid #4A5568;
        }

        .btn-secondary:hover {
            background-color: #4A5568;
            color: #FFFFFF;
        }

        .btn-danger {
            background-color: #F56565;
            color: #FFFFFF;
        }

        .btn-danger:hover {
            background-color: #E53E3E;
        }

        /* Terminal-like logs section */
        .logs-section {
            background-color: #000000;
            border: 1px solid #4A5568;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-line {
            color: #A0AEC0;
            margin-bottom: 4px;
        }

        .log-timestamp {
            color: #718096;
        }

        .log-level-info {
            color: #2496ED;
        }

        .log-level-error {
            color: #F56565;
        }

        .log-level-warn {
            color: #ED8936;
        }

        /* Search and filters */
        .toolbar {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            align-items: center;
        }

        .search-input {
            background-color: #2D3748;
            border: 1px solid #4A5568;
            border-radius: 6px;
            padding: 8px 12px;
            color: #FFFFFF;
            font-size: 14px;
            width: 300px;
        }

        .search-input::placeholder {
            color: #718096;
        }

        .filter-select {
            background-color: #2D3748;
            border: 1px solid #4A5568;
            border-radius: 6px;
            padding: 8px 12px;
            color: #FFFFFF;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="top-nav">
            <div class="nav-tabs">
                <button class="nav-tab active">Containers</button>
                <button class="nav-tab">Images</button>
                <button class="nav-tab">Volumes</button>
                <button class="nav-tab">Networks</button>
                <button class="nav-tab">Templates</button>
            </div>
            <div class="nav-actions">
                <button class="btn btn-primary">+ Novo Container</button>
            </div>
        </nav>

        <div class="main-layout">
            <aside class="sidebar">
                <div class="sidebar-section">
                    <div class="sidebar-title">Principal</div>
                    <a href="#" class="sidebar-item active">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h12v2H2v-2z"/>
                        </svg>
                        Containers
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <circle cx="8" cy="8" r="6"/>
                        </svg>
                        Imagens
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <rect x="2" y="2" width="12" height="12" rx="2"/>
                        </svg>
                        Volumes
                    </a>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">Automação</div>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M2 2v12h12V2H2z"/>
                        </svg>
                        Templates
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M8 2L2 8l6 6 6-6-6-6z"/>
                        </svg>
                        Workflows
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <circle cx="8" cy="8" r="3"/>
                        </svg>
                        Scheduler
                    </a>
                </div>

                <div class="sidebar-section">
                    <div class="sidebar-title">Monitoramento</div>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M2 2v12h12V2H2z"/>
                        </svg>
                        Logs
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M2 2v12h12V2H2z"/>
                        </svg>
                        Métricas
                    </a>
                    <a href="#" class="sidebar-item">
                        <svg class="sidebar-icon" viewBox="0 0 16 16">
                            <path d="M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z"/>
                        </svg>
                        Alertas
                    </a>
                </div>
            </aside>

            <div class="content-area">
                <div class="content-header">
                    <h2 class="content-title">Containers</h2>
                    <div class="toolbar">
                        <input type="text" class="search-input" placeholder="Buscar containers...">
                        <select class="filter-select">
                            <option>Todos os status</option>
                            <option>Executando</option>
                            <option>Parado</option>
                        </select>
                    </div>
                </div>

                <div class="content-main">
                    <div class="containers-grid">
                        <div class="container-card">
                            <div class="container-header">
                                <div class="container-info">
                                    <h3>wordpress-prod</h3>
                                    <div class="container-image">wordpress:6.3-apache</div>
                                </div>
                                <div class="container-status">
                                    <div class="status-indicator status-running"></div>
                                    <span class="status-text">Running</span>
                                </div>
                            </div>
                            
                            <div class="container-metrics">
                                <div class="metric">
                                    <div class="metric-value">15%</div>
                                    <div class="metric-label">CPU</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">512MB</div>
                                    <div class="metric-label">Memory</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-value">2.1GB</div>
                                    <div class="metric-label">Disk</div>
                                </div>
                            </div>

                            <div class="container-ports">
                                <div class="ports-title">Port Mappings</div>
                                <div class="port-mapping">8080:80/tcp</div>
                            </div>

                            <div class="container-actions">
                                <button class="btn btn-secondary">Logs</button>
                                <button class="btn btn-secondary">Exec</button>
                                <button class="btn btn-primary">Restart</button>
                                <button class="btn btn-danger">Stop</button>
                            </div>
                        </div>
                    </div>

                    <div class="logs-section">
                        <div class="log-line">
                            <span class="log-timestamp">2024-01-15 10:30:15</span> 
                            <span class="log-level-info">[INFO]</span> 
                            WordPress container started successfully
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">2024-01-15 10:30:16</span> 
                            <span class="log-level-info">[INFO]</span> 
                            Database connection established
                        </div>
                        <div class="log-line">
                            <span class="log-timestamp">2024-01-15 10:30:17</span> 
                            <span class="log-level-warn">[WARN]</span> 
                            Plugin update available
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
