using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Interfaces.Services;

/// <summary>
/// Interface para serviços de imagens de container
/// </summary>
public interface IContainerImageService
{
    /// <summary>
    /// Lista imagens de container
    /// </summary>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de imagens</returns>
    Task<ContainerImageListResponse> ListImagesAsync(ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Faz download de uma imagem
    /// </summary>
    /// <param name="imageName">Nome da imagem</param>
    /// <param name="tag">Tag da imagem</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado do download</returns>
    Task<ContainerImagePullResponse> PullImageAsync(string imageName, string? tag = null, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove uma imagem
    /// </summary>
    /// <param name="imageId">ID da imagem</param>
    /// <param name="force">Forçar remoção</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da remoção</returns>
    Task<ContainerImageRemoveResponse> RemoveImageAsync(string imageId, bool force = false, ContainerEngine? engine = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Busca imagens em registries
    /// </summary>
    /// <param name="searchTerm">Termo de busca</param>
    /// <param name="limit">Limite de resultados</param>
    /// <param name="engine">Engine específico (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultados da busca</returns>
    Task<ContainerImageSearchResponse> SearchImagesAsync(string searchTerm, int limit = 25, ContainerEngine? engine = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface para serviços de engines de container
/// </summary>
public interface IContainerEngineService
{
    /// <summary>
    /// Lista engines de container disponíveis
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de engines</returns>
    Task<ContainerEngineListResponse> ListEnginesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica o status de um engine específico
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Status do engine</returns>
    Task<ContainerEngineStatusResponse> GetEngineStatusAsync(ContainerEngine engine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações detalhadas de um engine
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do engine</returns>
    Task<ContainerEngineInfoResponse> GetEngineInfoAsync(ContainerEngine engine, CancellationToken cancellationToken = default);

    /// <summary>
    /// Instala um engine de container
    /// </summary>
    /// <param name="request">Parâmetros de instalação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da instalação</returns>
    Task<ContainerEngineInstallResponse> InstallEngineAsync(ContainerEngineInstallRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Detecta engines de container instalados
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Engines detectados</returns>
    Task<ContainerEngineDetectionResponse> DetectEnginesAsync(CancellationToken cancellationToken = default);
}
