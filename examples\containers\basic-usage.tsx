/**
 * Container Management - Basic Usage Example
 * Auto-Instalador V3 Lite
 * 
 * @description Exemplo básico de uso da funcionalidade de containers
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import {
  ContainerDashboard,
  useContainers,
  useRunContainer,
  useContainerAction,
  useDetectEngines,
  useInstallEngine
} from '../../src/frontend/renderer/components/features/containers';
import type {
  ContainerEngine,
  ContainerRunRequest,
  ContainerEngineInstallRequest,
  Platform
} from '../../shared/types/api.types';

// ============================================================================
// QUERY CLIENT SETUP
// ============================================================================

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000, // 30 segundos
      refetchOnWindowFocus: false,
    },
  },
});

// ============================================================================
// BASIC CONTAINER MANAGEMENT EXAMPLE
// ============================================================================

const BasicContainerExample: React.FC = () => {
  const [selectedEngine, setSelectedEngine] = useState<ContainerEngine>('docker');

  // Hooks
  const { data: containers, isLoading: containersLoading } = useContainers({
    all: true,
    engine: selectedEngine
  });

  const runContainer = useRunContainer();
  const containerAction = useContainerAction();

  // Handlers
  const handleRunNginx = async () => {
    const request: ContainerRunRequest = {
      image: 'nginx:latest',
      name: 'my-nginx-server',
      engine: selectedEngine,
      detached: true,
      ports: [
        {
          hostPort: 8080,
          containerPort: 80,
          protocol: 'tcp'
        }
      ],
      environment: {
        'NGINX_HOST': 'localhost',
        'NGINX_PORT': '80'
      },
      labels: {
        'app': 'nginx',
        'version': 'latest'
      }
    };

    await runContainer.mutateAsync(request);
  };

  const handleStopAllContainers = async () => {
    if (!containers?.containers) return;

    const runningContainers = containers.containers.filter(c => c.status === 'running');
    
    for (const container of runningContainers) {
      await containerAction.mutateAsync({
        id: container.id,
        action: 'stop',
        engine: container.engine
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">Exemplo Básico - Gerenciamento de Containers</h2>
        
        {/* Engine Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Engine de Container:
          </label>
          <select
            value={selectedEngine}
            onChange={(e) => setSelectedEngine(e.target.value as ContainerEngine)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="docker">Docker</option>
            <option value="podman">Podman</option>
          </select>
        </div>

        {/* Quick Actions */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={handleRunNginx}
            disabled={runContainer.isPending}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {runContainer.isPending ? 'Criando...' : 'Executar Nginx'}
          </button>
          
          <button
            onClick={handleStopAllContainers}
            disabled={containerAction.isPending || !containers?.containers?.some(c => c.status === 'running')}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
          >
            {containerAction.isPending ? 'Parando...' : 'Parar Todos'}
          </button>
        </div>

        {/* Container Summary */}
        {containersLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Carregando containers...</p>
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">Status dos Containers</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Total:</span>
                <span className="ml-2">{containers?.totalCount || 0}</span>
              </div>
              <div>
                <span className="font-medium">Executando:</span>
                <span className="ml-2 text-green-600">
                  {containers?.containers?.filter(c => c.status === 'running').length || 0}
                </span>
              </div>
              <div>
                <span className="font-medium">Parados:</span>
                <span className="ml-2 text-gray-600">
                  {containers?.containers?.filter(c => c.status === 'exited').length || 0}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// ENGINE INSTALLATION EXAMPLE
// ============================================================================

const EngineInstallationExample: React.FC = () => {
  const { data: detection } = useDetectEngines();
  const installEngine = useInstallEngine();

  const handleInstallDocker = async () => {
    const request: ContainerEngineInstallRequest = {
      engine: 'docker',
      platform: 'windows' as Platform, // Detectar automaticamente em produção
      packageManager: 'winget',
      force: false
    };

    await installEngine.mutateAsync(request);
  };

  const handleInstallPodman = async () => {
    const request: ContainerEngineInstallRequest = {
      engine: 'podman',
      platform: 'windows' as Platform,
      packageManager: 'winget',
      force: false
    };

    await installEngine.mutateAsync(request);
  };

  const dockerDetected = detection?.detectedEngines?.find(e => e.engine === 'docker');
  const podmanDetected = detection?.detectedEngines?.find(e => e.engine === 'podman');

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">Instalação de Engines</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Docker */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900">Docker</h3>
              <div className={`px-2 py-1 rounded text-xs ${
                dockerDetected?.isDetected 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {dockerDetected?.isDetected ? 'Instalado' : 'Não Instalado'}
              </div>
            </div>
            
            {dockerDetected?.version && (
              <p className="text-sm text-gray-600 mb-3">
                Versão: {dockerDetected.version}
              </p>
            )}
            
            {!dockerDetected?.isDetected && (
              <button
                onClick={handleInstallDocker}
                disabled={installEngine.isPending}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {installEngine.isPending ? 'Instalando...' : 'Instalar Docker'}
              </button>
            )}
          </div>

          {/* Podman */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900">Podman</h3>
              <div className={`px-2 py-1 rounded text-xs ${
                podmanDetected?.isDetected 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {podmanDetected?.isDetected ? 'Instalado' : 'Não Instalado'}
              </div>
            </div>
            
            {podmanDetected?.version && (
              <p className="text-sm text-gray-600 mb-3">
                Versão: {podmanDetected.version}
              </p>
            )}
            
            {!podmanDetected?.isDetected && (
              <button
                onClick={handleInstallPodman}
                disabled={installEngine.isPending}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                {installEngine.isPending ? 'Instalando...' : 'Instalar Podman'}
              </button>
            )}
          </div>
        </div>

        {/* Recommended Engine */}
        {detection?.recommendedEngine && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Engine Recomendado:</strong> {detection.recommendedEngine}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

// ============================================================================
// MAIN EXAMPLE APP
// ============================================================================

const ContainerExampleApp: React.FC = () => {
  const [currentExample, setCurrentExample] = useState<'dashboard' | 'basic' | 'installation'>('dashboard');

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-100">
        {/* Navigation */}
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center space-x-8">
                <h1 className="text-xl font-semibold text-gray-900">
                  Container Management Examples
                </h1>
                
                <div className="flex space-x-4">
                  <button
                    onClick={() => setCurrentExample('dashboard')}
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      currentExample === 'dashboard'
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Dashboard
                  </button>
                  <button
                    onClick={() => setCurrentExample('basic')}
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      currentExample === 'basic'
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Uso Básico
                  </button>
                  <button
                    onClick={() => setCurrentExample('installation')}
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      currentExample === 'installation'
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Instalação
                  </button>
                </div>
              </div>
            </div>
          </div>
        </nav>

        {/* Content */}
        <main className="max-w-7xl mx-auto">
          {currentExample === 'dashboard' && (
            <div className="h-screen">
              <ContainerDashboard />
            </div>
          )}
          
          {currentExample === 'basic' && <BasicContainerExample />}
          
          {currentExample === 'installation' && <EngineInstallationExample />}
        </main>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </div>
    </QueryClientProvider>
  );
};

export default ContainerExampleApp;
