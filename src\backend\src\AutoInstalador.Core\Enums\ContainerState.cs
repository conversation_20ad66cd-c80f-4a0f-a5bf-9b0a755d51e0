namespace AutoInstalador.Core.Enums;

/// <summary>
/// Estado detalhado do container
/// </summary>
public enum ContainerState
{
    /// <summary>
    /// Container foi criado mas não iniciado
    /// </summary>
    Created,

    /// <summary>
    /// Container está em execução
    /// </summary>
    Running,

    /// <summary>
    /// Container foi pausado
    /// </summary>
    Paused,

    /// <summary>
    /// Container está reiniciando
    /// </summary>
    Restarting,

    /// <summary>
    /// Container está sendo removido
    /// </summary>
    Removing,

    /// <summary>
    /// Container saiu/parou
    /// </summary>
    Exited,

    /// <summary>
    /// Container morreu/falhou
    /// </summary>
    Dead,

    /// <summary>
    /// Estado desconhecido
    /// </summary>
    Unknown
}
