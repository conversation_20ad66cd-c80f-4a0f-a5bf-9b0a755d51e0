{"feature": {"name": "Container Management", "version": "1.0.0", "description": "Sistema completo de gerenciamento de containers Docker e Podman", "author": "Augment Agent", "status": "stable", "tags": ["containers", "docker", "podman", "devops", "infrastructure"]}, "dependencies": {"backend": ["Microsoft.Extensions.DependencyInjection", "Microsoft.Extensions.Logging", "Microsoft.Extensions.Diagnostics.HealthChecks", "System.Text.Json"], "frontend": ["@tanstack/react-query", "react-hot-toast", "framer-motion", "@heroicons/react"]}, "api": {"baseUrl": "/api/containers", "endpoints": {"containers": {"list": "GET /", "get": "GET /{id}", "run": "POST /run", "start": "POST /{id}/start", "stop": "POST /{id}/stop", "restart": "POST /{id}/restart", "pause": "POST /{id}/pause", "unpause": "POST /{id}/unpause", "remove": "DELETE /{id}", "logs": "GET /{id}/logs", "stats": "GET /{id}/stats", "exec": "POST /{id}/exec"}, "images": {"list": "GET /images", "pull": "POST /images/pull", "remove": "DELETE /images/{id}", "search": "GET /images/search"}, "engines": {"list": "GET /engines", "status": "GET /engines/{engine}/status", "info": "GET /engines/{engine}/info", "install": "POST /engines/install", "detect": "GET /engines/detect"}}}, "components": {"main": ["ContainerDashboard", "ContainerList", "ContainerStats", "ContainerLogs", "ContainerControls"], "hooks": ["useContainers", "useContainer", "useContainerStats", "useContainerLogs", "useRunContainer", "useContainerAction", "useContainerImages", "useContainerEngines", "useDetectEngines", "useInstallEngine", "useContainerEvents"], "services": ["ContainerManagerService", "ContainerImageManagerService", "ContainerEngineManagerService", "ContainerEngineDetector"]}, "configuration": {"defaultEngine": "docker", "refreshIntervals": {"containerList": 30000, "containerStats": 5000, "containerLogs": 10000, "engineStatus": 120000}, "limits": {"maxLogLines": 1000, "maxContainersPerPage": 50, "commandTimeout": 30000}, "features": {"realTimeUpdates": true, "autoInstallEngines": true, "containerTemplates": false, "networkManagement": false, "volumeManagement": false}}, "platforms": {"supported": ["windows", "macos", "linux"], "engines": {"docker": {"platforms": ["windows", "macos", "linux"], "packageManagers": {"windows": ["winget", "chocolatey"], "macos": ["brew"], "linux": ["apt", "dnf", "yum", "pacman"]}}, "podman": {"platforms": ["windows", "macos", "linux"], "packageManagers": {"windows": ["winget", "chocolatey"], "macos": ["brew"], "linux": ["apt", "dnf", "yum", "pacman"]}}}}, "testing": {"unitTests": {"backend": "src/backend/tests/AutoInstalador.UnitTests/Services/ContainerServiceTests.cs", "frontend": "src/frontend/renderer/__tests__/components/containers/"}, "integrationTests": {"backend": "src/backend/tests/AutoInstalador.IntegrationTests/Controllers/ContainersControllerTests.cs"}, "e2eTests": {"scenarios": ["container-lifecycle", "engine-detection", "real-time-monitoring"]}}, "documentation": {"main": "docs/features/CONTAINERS.md", "api": "docs/api/containers.md", "examples": "examples/containers/", "troubleshooting": "docs/troubleshooting/containers.md"}, "security": {"validation": {"containerIds": "^[a-zA-Z0-9][a-zA-Z0-9_.-]*$", "imageNames": "^[a-z0-9]+(?:[._-][a-z0-9]+)*(?:/[a-z0-9]+(?:[._-][a-z0-9]+)*)*(?::[a-zA-Z0-9_.-]+)?$", "commandWhitelist": ["sh", "bash", "cmd", "powershell", "echo", "ls", "ps"]}, "permissions": {"requiresElevation": false, "dockerGroup": true, "rootlessSupport": true}}, "monitoring": {"healthChecks": ["container-engines", "api-connectivity", "resource-availability"], "metrics": ["container-count", "engine-status", "api-response-time", "error-rate"], "alerts": ["engine-unavailable", "container-failed", "high-resource-usage"]}, "roadmap": {"v1.1": ["Docker Compose support", "Container networking UI", "Volume management", "Registry authentication"], "v1.2": ["Container templates", "Backup and restore", "Performance optimization", "Multi-host support"], "v2.0": ["Kubernetes integration", "CI/CD pipelines", "Advanced monitoring", "Team collaboration"]}}