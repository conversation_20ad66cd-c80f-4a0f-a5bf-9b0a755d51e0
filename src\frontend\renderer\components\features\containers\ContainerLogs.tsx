/**
 * Container Logs Component
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para visualização de logs de container
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React, { useState, useRef, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowDownIcon,
  ArrowPathIcon,
  FunnelIcon,
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useContainerLogs } from '../../../services/container-service';
import type { 
  ContainerEngine,
  ContainerLogsRequest,
  ContainerLog,
  LogStream 
} from '../../../../../shared/types/api.types';

// ============================================================================
// INTERFACES
// ============================================================================

interface ContainerLogsProps {
  containerId: string;
  containerName?: string;
  engine?: ContainerEngine;
  className?: string;
  autoScroll?: boolean;
  maxLines?: number;
}

interface LogFilters {
  search: string;
  stream: LogStream | 'all';
  tail: number;
  since?: Date;
  until?: Date;
  timestamps: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

export const ContainerLogs: React.FC<ContainerLogsProps> = ({
  containerId,
  containerName,
  engine,
  className = '',
  autoScroll = true,
  maxLines = 1000
}) => {
  // State
  const [filters, setFilters] = useState<LogFilters>({
    search: '',
    stream: 'all',
    tail: 100,
    timestamps: true
  });
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(autoScroll);
  const [showFilters, setShowFilters] = useState(false);

  // Refs
  const logsContainerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);

  // Build request
  const request: ContainerLogsRequest = useMemo(() => ({
    containerId,
    tail: filters.tail,
    timestamps: filters.timestamps,
    since: filters.since,
    until: filters.until,
    engine
  }), [containerId, filters, engine]);

  // Query
  const { 
    data: logsResponse, 
    isLoading, 
    error, 
    refetch,
    isRefetching 
  } = useContainerLogs(request, {
    refetchInterval: isAutoScrollEnabled ? 5000 : false,
    enabled: !!containerId
  });

  // Filtered logs
  const filteredLogs = useMemo(() => {
    if (!logsResponse?.logs) return [];

    let logs = logsResponse.logs;

    // Filter by stream
    if (filters.stream !== 'all') {
      logs = logs.filter(log => log.stream === filters.stream);
    }

    // Filter by search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      logs = logs.filter(log => 
        log.message.toLowerCase().includes(searchLower)
      );
    }

    // Limit lines
    if (logs.length > maxLines) {
      logs = logs.slice(-maxLines);
    }

    return logs;
  }, [logsResponse?.logs, filters, maxLines]);

  // Auto scroll to bottom
  useEffect(() => {
    if (isAutoScrollEnabled && bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [filteredLogs, isAutoScrollEnabled]);

  // Handlers
  const handleFilterChange = (key: keyof LogFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleDownloadLogs = () => {
    if (!filteredLogs.length) return;

    const logsText = filteredLogs
      .map(log => {
        const timestamp = filters.timestamps 
          ? `[${new Date(log.timestamp).toISOString()}] `
          : '';
        const stream = log.stream === 'stderr' ? '[STDERR] ' : '';
        return `${timestamp}${stream}${log.message}`;
      })
      .join('\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${containerName || containerId}-logs.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('pt-BR', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  const getLogLineColor = (log: ContainerLog) => {
    if (log.stream === 'stderr') return 'text-red-600';
    if (log.message.toLowerCase().includes('error')) return 'text-red-600';
    if (log.message.toLowerCase().includes('warn')) return 'text-yellow-600';
    if (log.message.toLowerCase().includes('info')) return 'text-blue-600';
    return 'text-gray-700';
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Carregando logs...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
          <span className="ml-2 text-red-800">Erro ao carregar logs</span>
        </div>
        <p className="mt-2 text-sm text-red-600">{error.message}</p>
        <button
          onClick={() => refetch()}
          className="mt-3 px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200 transition-colors"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Logs do Container</h3>
          {containerName && (
            <p className="text-sm text-gray-600">{containerName}</p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {isRefetching && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
            }`}
            title="Filtros"
          >
            <FunnelIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={handleDownloadLogs}
            disabled={!filteredLogs.length}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors disabled:opacity-50"
            title="Baixar logs"
          >
            <DocumentArrowDownIcon className="h-4 w-4" />
          </button>
          
          <button
            onClick={() => refetch()}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
            title="Atualizar"
          >
            <ArrowPathIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white rounded-lg border border-gray-200 p-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar nos logs..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Stream Filter */}
              <select
                value={filters.stream}
                onChange={(e) => handleFilterChange('stream', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Todos os streams</option>
                <option value="stdout">STDOUT</option>
                <option value="stderr">STDERR</option>
              </select>

              {/* Tail Lines */}
              <select
                value={filters.tail}
                onChange={(e) => handleFilterChange('tail', parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={50}>50 linhas</option>
                <option value={100}>100 linhas</option>
                <option value={500}>500 linhas</option>
                <option value={1000}>1000 linhas</option>
              </select>

              {/* Timestamps Toggle */}
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.timestamps}
                  onChange={(e) => handleFilterChange('timestamps', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Timestamps</span>
              </label>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Auto-scroll Toggle */}
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-4">
          <span className="text-gray-600">
            {filteredLogs.length} linha{filteredLogs.length !== 1 ? 's' : ''}
          </span>
          {isAutoScrollEnabled && (
            <div className="flex items-center text-green-600">
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>Auto-atualização ativa</span>
            </div>
          )}
        </div>
        
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={isAutoScrollEnabled}
            onChange={(e) => setIsAutoScrollEnabled(e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-gray-700">Auto-scroll</span>
        </label>
      </div>

      {/* Logs Container */}
      <div 
        ref={logsContainerRef}
        className="bg-gray-900 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm"
      >
        {filteredLogs.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <p>Nenhum log encontrado</p>
              {filters.search && (
                <p className="text-xs mt-1">Tente ajustar os filtros de busca</p>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-1">
            {filteredLogs.map((log, index) => (
              <motion.div
                key={`${log.timestamp}-${index}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`flex ${getLogLineColor(log)}`}
              >
                {filters.timestamps && (
                  <span className="text-gray-500 mr-3 flex-shrink-0">
                    {formatTimestamp(log.timestamp)}
                  </span>
                )}
                {log.stream === 'stderr' && (
                  <span className="text-red-400 mr-2 flex-shrink-0">[STDERR]</span>
                )}
                <span className="break-all">{log.message}</span>
              </motion.div>
            ))}
            <div ref={bottomRef} />
          </div>
        )}
      </div>

      {/* Scroll to Bottom Button */}
      {!isAutoScrollEnabled && (
        <div className="flex justify-center">
          <button
            onClick={() => bottomRef.current?.scrollIntoView({ behavior: 'smooth' })}
            className="flex items-center px-3 py-1 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <ArrowDownIcon className="h-4 w-4 mr-1" />
            Ir para o final
          </button>
        </div>
      )}
    </div>
  );
};
