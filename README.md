# 🚀 Auto-Instalador V3 Lite Desktop

**📅 Data de Criação:** 04 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Augment Agent  

## 🌟 **VISÃO GERAL**

O Auto-Instalador V3 Lite Desktop é uma aplicação desktop moderna construída com **Electron 37.1.2** e **React 19.2.0**, otimizada especificamente para hardware **Intel i5 12ª Geração, 32GB RAM e SSD 512GB**.

### **Características Principais**
- ✅ **Interface Moderna:** React 19.2 com Actions, useOptimistic e use() hooks
- ✅ **Performance Otimizada:** Configurado para i5 12ª Gen (6P + 6E cores)
- ✅ **Multiplataforma:** Windows, Linux e macOS
- ✅ **Gerenciamento de Containers:** Docker e Podman integration
- ✅ **Auto-Update:** Sistema de atualização automática
- ✅ **Offline-First:** Funciona sem conexão com internet

---

## 🏗️ **ARQUITETURA**

### **Stack Tecnológico**
```yaml
Frontend (Electron + React):
  - React: 19.2.0 (Actions, useOptimistic, use())
  - TypeScript: 5.6.2
  - Electron: 37.1.2 (Chromium 130)
  - Vite: 5.4.2
  - Tailwind CSS: 4.0.0-beta.1
  - Framer Motion: 11.5.4
  - React Query: 5.56.2
  - Zustand: 5.0.0

Backend (.NET 8.0):
  - ASP.NET Core: 8.0 (Web API)
  - Entity Framework Core: 8.0
  - AutoMapper: 13.0.1
  - FluentValidation: 11.9.2
  - Serilog: 4.0.1
  - SQLite: better-sqlite3 11.3.0
  - Redis: ioredis 5.4.1

Runtime & Tools:
  - Node.js: 22.7.0 LTS
  - .NET SDK: 8.0 LTS
  - Electron Builder: 25.0.5
  - ESLint: 9.9.1
  - Prettier: 3.3.3
  - Husky: 9.1.4

Testing & Quality:
  - Frontend: Vitest 2.0.5 + Testing Library 16.0.0
  - Backend: xUnit 2.9.0 + TestContainers
  - E2E: Playwright 1.47.0
  - Contract: Pact.NET + OpenAPI
```

### **Estrutura do Projeto**
```
auto-instalador-v3-lite/
├── docs/                    # Documentação do projeto
│   └── DEPENDENCIES_VERSIONS.md
├── CodeBook/               # Documentação técnica das ferramentas
│   ├── Electron/          # Documentação Electron 37.1.2
│   ├── React/             # Documentação React 19.2.0
│   ├── TypeScript/        # Documentação TypeScript 5.6.2
│   ├── Vite/              # Documentação Vite 5.4.2
│   └── [outras ferramentas]/
├── src/                   # Código fonte
│   ├── frontend/          # 🎨 FRONTEND (Electron + React)
│   │   ├── electron/      # Código Electron (Main/Preload/Renderer)
│   │   └── renderer/      # Aplicação React (UI)
│   └── backend/           # 🏗️ BACKEND (.NET 8.0)
│       ├── src/           # Projetos .NET (API, Core, Application, Infrastructure)
│       └── tests/         # Testes backend (Unit, Integration, E2E)
├── shared/                # 🤝 RECURSOS COMPARTILHADOS
│   ├── types/             # Types TypeScript compartilhados
│   ├── contracts/         # Contratos de API
│   └── schemas/           # Schemas de validação
├── tests/                 # 🧪 TESTES FRONTEND
│   ├── unit/              # Testes unitários
│   ├── integration/       # Testes integração
│   ├── e2e/               # Testes E2E
│   └── contracts/         # Testes de contrato
├── scripts/               # Scripts de automação
│   ├── setup-project.ps1 # Setup Windows (PowerShell)
│   └── setup-project.sh  # Setup Linux/macOS (Bash)
└── README.md             # Este arquivo
```

---

## 🚀 **QUICK START**

### **Pré-requisitos**
- **Node.js:** 22.7.0 LTS
- **.NET SDK:** 9.0.0
- **Git:** Última versão
- **Hardware:** Intel i5 12ª Gen, 32GB RAM, SSD 512GB (recomendado)

### **Instalação Automática**

#### **Windows (PowerShell)**
```powershell
# Clone o repositório
git clone https://github.com/auto-instalador-v3/desktop-app.git
cd auto-instalador-v3-lite

# Execute o script de setup
.\scripts\setup-project.ps1
```

#### **Linux/macOS (Bash)**
```bash
# Clone o repositório
git clone https://github.com/auto-instalador-v3/desktop-app.git
cd auto-instalador-v3-lite

# Torne o script executável e execute
chmod +x scripts/setup-project.sh
./scripts/setup-project.sh
```

### **Desenvolvimento**
```bash
# Iniciar em modo desenvolvimento
npm run dev

# Build para produção
npm run build

# Executar testes
npm test

# Gerar distribuível
npm run dist
```

---

## 📊 **PERFORMANCE OTIMIZADA**

### **Configurações para i5 12ª Gen**
```yaml
CPU Utilization:
  Performance Cores (6): 85% efficiency
  Efficiency Cores (6): 60% efficiency
  Total Threads: 16 (otimizado)

Memory Configuration:
  Node.js Heap: 4GB (vs 1GB padrão)
  Electron Cache: 500MB
  SQLite Cache: 32MB
  Redis Cache: 2GB

Storage Optimization:
  Build Cache: 4GB
  Temp Files: 20GB reserved
  App Size: ~1.2GB installed
```

### **Benchmarks Esperados**
```yaml
Startup Time: 2.5-4.2 segundos
Memory Usage (Idle): 640MB-960MB
CPU Usage (Normal): 10-18%
Build Time: 35-45 segundos
Bundle Size: ~350MB
```

---

## 🔧 **CONFIGURAÇÃO AVANÇADA**

### **Variáveis de Ambiente**
```bash
# .env file
NODE_OPTIONS=--max-old-space-size=4096 --max-semi-space-size=512
UV_THREADPOOL_SIZE=12
ELECTRON_DISABLE_SECURITY_WARNINGS=true

# .NET Optimizations
DOTNET_GCHeapCount=6
DOTNET_GCConcurrent=1
DOTNET_GCServer=0
```

### **Scripts Disponíveis**
```json
{
  "dev": "Desenvolvimento com hot reload",
  "build": "Build completo (React + Electron)",
  "dist": "Gerar distribuível multiplataforma",
  "dist:win": "Distribuível Windows (NSIS + Portable)",
  "dist:linux": "Distribuível Linux (AppImage + deb + rpm)",
  "dist:mac": "Distribuível macOS (dmg + pkg)",
  "test": "Executar todos os testes",
  "test:coverage": "Testes com coverage",
  "test:e2e": "Testes end-to-end",
  "lint": "Verificar código (ESLint)",
  "format": "Formatar código (Prettier)"
}
```

---

## 📚 **DOCUMENTAÇÃO TÉCNICA**

### **CodeBook - Documentação das Ferramentas**
Cada ferramenta possui documentação completa na pasta `CodeBook/`:

#### **Electron 37.1.2**
- `README.md` - Visão geral e links
- `DOCUMENTATION.md` - Documentação oficial resumida
- `EXAMPLES.md` - Exemplos práticos para o projeto
- `COMMON_ERRORS.md` - Erros comuns e soluções
- `UPDATES_CHANGELOG.md` - Histórico de atualizações
- `REFERENCES.md` - Links e recursos adicionais

#### **React 19.2.0**
- Documentação completa dos novos recursos
- Actions, useOptimistic, use() hooks
- Exemplos específicos para Electron
- Padrões de performance

#### **Outras Ferramentas**
- TypeScript 5.6.2
- Vite 5.4.2
- Tailwind CSS 4.0.0-beta.1
- E todas as demais dependências

### **Dependências e Versões**
Consulte `docs/DEPENDENCIES_VERSIONS.md` para lista completa de todas as dependências com versões específicas e justificativas.

---

## 🧪 **TESTING**

### **Estratégia de Testes**
```yaml
Unit Tests: Vitest 2.0.5
  - Components React
  - Hooks customizados
  - Utility functions
  - Services

Integration Tests: Vitest + Testing Library
  - Electron IPC communication
  - API integrations
  - Database operations

E2E Tests: Playwright 1.47.0
  - User workflows
  - Cross-platform testing
  - Performance testing
```

### **Executar Testes**
```bash
# Todos os testes
npm test

# Com coverage
npm run test:coverage

# E2E tests
npm run test:e2e

# Watch mode
npm run test:watch
```

---

## 📦 **BUILD E DISTRIBUIÇÃO**

### **Multiplataforma**
```bash
# Windows
npm run dist:win
# Gera: .exe (NSIS installer) + portable

# Linux
npm run dist:linux
# Gera: .AppImage + .deb + .rpm

# macOS
npm run dist:mac
# Gera: .dmg + .pkg

# Todas as plataformas
npm run dist:all
```

### **Auto-Updater**
- Configurado para GitHub Releases
- Updates automáticos em background
- Rollback em caso de falha
- Notificações para o usuário

---

## 🤝 **CONTRIBUIÇÃO**

### **Desenvolvimento**
1. Fork o repositório
2. Crie uma branch: `git checkout -b feature/nova-funcionalidade`
3. Commit suas mudanças: `git commit -m 'Add nova funcionalidade'`
4. Push para a branch: `git push origin feature/nova-funcionalidade`
5. Abra um Pull Request

### **Padrões de Código**
- **ESLint:** Configuração strict
- **Prettier:** Formatação automática
- **Husky:** Git hooks para qualidade
- **Conventional Commits:** Padrão de commit messages

---

## 📄 **LICENÇA**

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

---

## 🆘 **SUPORTE**

### **Documentação**
- **CodeBook:** Documentação técnica completa
- **Examples:** Exemplos práticos de uso
- **Common Errors:** Soluções para problemas conhecidos

### **Comunidade**
- **Issues:** GitHub Issues para bugs e features
- **Discussions:** GitHub Discussions para dúvidas
- **Wiki:** Documentação colaborativa

### **Hardware Específico**
Este projeto foi otimizado especificamente para:
- **CPU:** Intel i5 12ª Geração (Alder Lake)
- **RAM:** 32GB (configurações otimizadas)
- **Storage:** SSD 512GB (cache expandido)

Para outros hardwares, consulte a documentação de configuração.

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Auto-Instalador V3 Lite Desktop  
**✨ Status:** Pronto para Desenvolvimento**
