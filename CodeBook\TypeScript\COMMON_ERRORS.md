# TypeScript 5.6.2 - <PERSON><PERSON><PERSON> Comuns e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.6.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.typescriptlang.org/
- **GitHub:** https://github.com/microsoft/TypeScript
- **Documentação:** https://www.typescriptlang.org/docs/
- **NPM/Package:** https://www.npmjs.com/package/typescript
- **Fórum/Community:** https://github.com/microsoft/TypeScript/discussions
- **Stack Overflow Tag:** `typescript`

---

## 🚨 **ERROS CRÍTICOS TYPESCRIPT 5.6.2**

### **1. Electron API Type Errors**

#### **Erro:**
```typescript
// Error: Property 'electronAPI' does not exist on type 'Window & typeof globalThis'
const containers = await window.electronAPI.containers.list();
```

#### **Causa:**
Tipos do Electron API não declarados corretamente.

#### **Solução:**
```typescript
// src/types/electron.d.ts
export interface ElectronAPI {
  containers: {
    list: () => Promise<Container[]>;
    start: (id: string) => Promise<void>;
    stop: (id: string) => Promise<void>;
  };
  // ... outras APIs
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// src/services/containerService.ts
export class ContainerService {
  private get api(): ElectronAPI {
    if (!window.electronAPI) {
      throw new Error('Electron API não disponível');
    }
    return window.electronAPI;
  }

  async listContainers(): Promise<Container[]> {
    return await this.api.containers.list();
  }
}
```

---

### **2. React 19.2 Hook Type Errors**

#### **Erro:**
```typescript
// Error: Cannot find name 'useActionState'
// Error: This expression is not callable
const [state, formAction, isPending] = useActionState(action, initialState);
```

#### **Causa:**
Tipos do React 19.2 não configurados corretamente.

#### **Solução:**
```typescript
// src/types/react-19.d.ts
declare module 'react' {
  function useActionState<State>(
    action: (previousState: State, formData: FormData) => State | Promise<State>,
    initialState: State,
    permalink?: string
  ): [state: State, formAction: (formData: FormData) => void, isPending: boolean];

  function useOptimistic<State, Action>(
    state: State,
    updateFn: (currentState: State, optimisticValue: Action) => State
  ): [optimisticState: State, addOptimistic: (action: Action) => void];

  function use<T>(promise: Promise<T>): T;
  function use<T>(context: Context<T>): T;
}

// Uso correto
import { useActionState } from 'react';

interface FormState {
  loading: boolean;
  error?: string;
}

function MyForm() {
  const [state, formAction, isPending] = useActionState<FormState>(
    async (previousState: FormState, formData: FormData): Promise<FormState> => {
      // Implementation
      return { loading: false };
    },
    { loading: false }
  );

  return (
    <form action={formAction}>
      {/* Form content */}
    </form>
  );
}
```

---

### **3. Path Mapping Resolution Errors**

#### **Erro:**
```typescript
// Error: Cannot find module '@components/ContainerCard' or its corresponding type declarations
import ContainerCard from '@components/ContainerCard';
```

#### **Causa:**
Path mapping não configurado corretamente no tsconfig.json.

#### **Solução:**
```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/renderer/components/*"],
      "@services/*": ["src/renderer/services/*"],
      "@types/*": ["src/types/*"],
      "@utils/*": ["src/renderer/utils/*"],
      "@hooks/*": ["src/renderer/hooks/*"]
    }
  },
  "include": [
    "src/**/*"
  ]
}

// vite.config.ts - Também configurar no Vite
import { defineConfig } from 'vite';
import path from 'path';

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@components': path.resolve(__dirname, 'src/renderer/components'),
      '@services': path.resolve(__dirname, 'src/renderer/services'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@utils': path.resolve(__dirname, 'src/renderer/utils'),
      '@hooks': path.resolve(__dirname, 'src/renderer/hooks')
    }
  }
});
```

---

### **4. Generic Type Inference Errors**

#### **Erro:**
```typescript
// Error: Argument of type 'unknown' is not assignable to parameter of type 'Container'
const result = await apiCall();
processContainer(result); // Error aqui
```

#### **Causa:**
TypeScript não consegue inferir o tipo correto de retorno da API.

#### **Solução:**
```typescript
// ❌ ERRADO - Tipo não especificado
async function apiCall() {
  const response = await fetch('/api/containers');
  return response.json(); // Retorna 'any'
}

// ✅ CORRETO - Tipo explícito
async function apiCall(): Promise<Container[]> {
  const response = await fetch('/api/containers');
  const data = await response.json();
  return data as Container[];
}

// ✅ MELHOR - Com validação de tipo
import { z } from 'zod';

const ContainerSchema = z.object({
  id: z.string(),
  name: z.string(),
  status: z.enum(['running', 'stopped', 'paused']),
  image: z.string()
});

const ContainerArraySchema = z.array(ContainerSchema);

async function apiCallWithValidation(): Promise<Container[]> {
  const response = await fetch('/api/containers');
  const data = await response.json();
  
  // Validar e garantir tipo correto
  return ContainerArraySchema.parse(data);
}

// ✅ EXCELENTE - Com Result type
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

async function safeApiCall(): Promise<Result<Container[]>> {
  try {
    const response = await fetch('/api/containers');
    const data = await response.json();
    const containers = ContainerArraySchema.parse(data);
    
    return { success: true, data: containers };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('Unknown error')
    };
  }
}
```

---

### **5. Strict Mode Errors**

#### **Erro:**
```typescript
// Error: Object is possibly 'null' or 'undefined'
// Error: Property 'length' does not exist on type 'Container[] | undefined'
const count = containers.length; // Error
```

#### **Causa:**
Strict null checks habilitado, mas código não trata valores null/undefined.

#### **Solução:**
```typescript
// ❌ ERRADO - Não verifica null/undefined
function getContainerCount(containers: Container[] | undefined): number {
  return containers.length; // Error: containers pode ser undefined
}

// ✅ CORRETO - Verificação explícita
function getContainerCount(containers: Container[] | undefined): number {
  if (!containers) {
    return 0;
  }
  return containers.length;
}

// ✅ MELHOR - Optional chaining
function getContainerCount(containers: Container[] | undefined): number {
  return containers?.length ?? 0;
}

// ✅ EXCELENTE - Type guard
function isContainerArray(value: unknown): value is Container[] {
  return Array.isArray(value) && value.every(item => 
    typeof item === 'object' && 
    item !== null && 
    'id' in item && 
    'name' in item
  );
}

function processContainers(data: unknown) {
  if (isContainerArray(data)) {
    // TypeScript sabe que data é Container[]
    console.log(`Found ${data.length} containers`);
    data.forEach(container => console.log(container.name));
  }
}
```

---

### **6. Module Resolution Errors**

#### **Erro:**
```typescript
// Error: Module '"better-sqlite3"' has no default export
import Database from 'better-sqlite3';
```

#### **Causa:**
Configuração incorreta de imports para módulos CommonJS.

#### **Solução:**
```typescript
// ❌ ERRADO - Import default de módulo CommonJS
import Database from 'better-sqlite3';

// ✅ CORRETO - Import namespace
import * as Database from 'better-sqlite3';

// ✅ ALTERNATIVA - Import com require
import Database = require('better-sqlite3');

// ✅ MELHOR - Com tipos corretos
import type { Database as DatabaseType } from 'better-sqlite3';
import Database from 'better-sqlite3';

class DatabaseService {
  private db: DatabaseType;

  constructor(path: string) {
    this.db = new Database(path);
  }
}

// Para módulos sem tipos
// src/types/modules.d.ts
declare module 'some-module-without-types' {
  export interface SomeInterface {
    property: string;
  }
  
  export function someFunction(param: string): SomeInterface;
  export default someFunction;
}
```

---

### **7. JSX Type Errors**

#### **Erro:**
```typescript
// Error: JSX element type 'Component' does not have any construct or call signatures
// Error: Property 'children' does not exist on type 'IntrinsicAttributes'
```

#### **Causa:**
Configuração incorreta do JSX ou tipos de componentes.

#### **Solução:**
```typescript
// tsconfig.json - Configuração JSX correta
{
  "compilerOptions": {
    "jsx": "react-jsx",
    "jsxImportSource": "react"
  }
}

// ❌ ERRADO - Tipo de props incorreto
interface ComponentProps {
  title: string;
}

function Component(props: ComponentProps) {
  return <div>{props.children}</div>; // Error: children não existe
}

// ✅ CORRETO - Com PropsWithChildren
import type { PropsWithChildren } from 'react';

interface ComponentProps {
  title: string;
}

function Component({ title, children }: PropsWithChildren<ComponentProps>) {
  return (
    <div>
      <h1>{title}</h1>
      {children}
    </div>
  );
}

// ✅ MELHOR - Tipo explícito para children
interface ComponentProps {
  title: string;
  children?: React.ReactNode;
}

function Component({ title, children }: ComponentProps) {
  return (
    <div>
      <h1>{title}</h1>
      {children}
    </div>
  );
}

// ✅ EXCELENTE - Generic component
interface GenericComponentProps<T> {
  data: T;
  render: (item: T) => React.ReactNode;
  children?: React.ReactNode;
}

function GenericComponent<T>({ data, render, children }: GenericComponentProps<T>) {
  return (
    <div>
      {render(data)}
      {children}
    </div>
  );
}

// Uso
<GenericComponent<Container>
  data={container}
  render={(container) => <span>{container.name}</span>}
>
  <p>Additional content</p>
</GenericComponent>
```

---

### **8. Async/Await Type Errors**

#### **Erro:**
```typescript
// Error: Argument of type 'Promise<Container>' is not assignable to parameter of type 'Container'
// Error: Property 'then' does not exist on type 'Container'
```

#### **Causa:**
Mistura de código síncrono e assíncrono sem await.

#### **Solução:**
```typescript
// ❌ ERRADO - Esqueceu await
async function processContainer(id: string) {
  const container = getContainer(id); // Retorna Promise<Container>
  console.log(container.name); // Error: container é Promise, não Container
}

// ✅ CORRETO - Com await
async function processContainer(id: string) {
  const container = await getContainer(id);
  console.log(container.name); // OK: container é Container
}

// ✅ MELHOR - Com error handling
async function processContainer(id: string): Promise<void> {
  try {
    const container = await getContainer(id);
    console.log(container.name);
  } catch (error) {
    console.error('Failed to get container:', error);
  }
}

// ✅ EXCELENTE - Com Result type
async function processContainer(id: string): Promise<Result<void>> {
  try {
    const containerResult = await getContainer(id);
    if (!containerResult.success) {
      return { success: false, error: containerResult.error };
    }
    
    console.log(containerResult.data.name);
    return { success: true, data: undefined };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error('Unknown error')
    };
  }
}

// Type guard para promises
function isPromise<T>(value: T | Promise<T>): value is Promise<T> {
  return value instanceof Promise;
}

function handleValue<T>(value: T | Promise<T>) {
  if (isPromise(value)) {
    return value.then(result => console.log(result));
  } else {
    console.log(value);
  }
}
```

---

### **9. Performance Issues**

#### **Erro:**
```
// TypeScript compilation is slow
// High memory usage during compilation
```

#### **Causa:**
Configuração não otimizada para hardware i5 12ª Gen.

#### **Solução:**
```json
// tsconfig.json - Otimizado para i5 12ª Gen + 32GB RAM
{
  "compilerOptions": {
    // Performance optimizations
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo",
    "assumeChangesOnlyAffectDirectDependencies": true,
    
    // Memory optimization
    "maxNodeModuleJsDepth": 0,
    "disableSourceOfProjectReferenceRedirect": true,
    
    // Parallel processing
    "preserveWatchOutput": true
  },
  
  // Watch options otimizadas para SSD
  "watchOptions": {
    "watchFile": "useFsEvents",
    "watchDirectory": "useFsEvents",
    "fallbackPolling": "dynamicPriority",
    "synchronousWatchDirectory": true,
    "excludeDirectories": [
      "**/node_modules",
      "**/.git",
      "**/dist",
      "**/coverage"
    ]
  },
  
  // Exclude desnecessários
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ]
}

// package.json - Scripts otimizados
{
  "scripts": {
    "type-check": "tsc --noEmit --incremental",
    "type-check:watch": "tsc --noEmit --incremental --watch",
    "build:types": "tsc --build --verbose"
  }
}
```

---

### **10. Build Configuration Errors**

#### **Erro:**
```
// Error: Cannot find type definition file for 'node'
// Error: Module resolution failed
```

#### **Causa:**
Configuração incorreta para diferentes ambientes (Electron vs Renderer).

#### **Solução:**
```json
// tsconfig.json (base)
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020"],
    "moduleResolution": "bundler",
    "strict": true,
    "skipLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}

// tsconfig.renderer.json (React)
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "jsx": "react-jsx",
    "noEmit": true,
    "types": ["vite/client"]
  },
  "include": [
    "src/renderer/**/*",
    "src/types/**/*"
  ],
  "exclude": [
    "src/electron/**/*"
  ]
}

// tsconfig.electron.json (Electron)
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "module": "CommonJS",
    "outDir": "dist/electron",
    "rootDir": "src/electron",
    "types": ["node", "electron"]
  },
  "include": [
    "src/electron/**/*",
    "src/types/electron.d.ts"
  ],
  "exclude": [
    "src/renderer/**/*"
  ]
}
```

---

## 🔧 **DEBUGGING TOOLS**

### **TypeScript Compiler Diagnostics**
```bash
# Verificar configuração
npx tsc --showConfig

# Análise detalhada de performance
npx tsc --diagnostics --extendedDiagnostics

# Verificar resolução de módulos
npx tsc --traceResolution

# Verificar apenas tipos (sem emitir arquivos)
npx tsc --noEmit
```

### **VS Code Configuration**
```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - TypeScript 5.6.2 Common Errors
