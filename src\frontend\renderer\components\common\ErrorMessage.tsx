/**
 * Error Message - Componente de mensagem de erro
 * Auto-Instalador V3 Lite
 * 
 * @description Componente para exibir mensagens de erro
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';

interface ErrorMessageProps {
  message: string;
  details?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  variant?: 'error' | 'warning' | 'info';
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  details,
  onRetry,
  onDismiss,
  className = '',
  variant = 'error'
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // Definir cores baseadas na variante
  const variantClasses = {
    error: {
      container: 'bg-red-900/20 border-red-500/50',
      icon: '❌',
      text: 'text-red-400',
      button: 'bg-red-600 hover:bg-red-700'
    },
    warning: {
      container: 'bg-yellow-900/20 border-yellow-500/50',
      icon: '⚠️',
      text: 'text-yellow-400',
      button: 'bg-yellow-600 hover:bg-yellow-700'
    },
    info: {
      container: 'bg-blue-900/20 border-blue-500/50',
      icon: 'ℹ️',
      text: 'text-blue-400',
      button: 'bg-blue-600 hover:bg-blue-700'
    }
  };

  const styles = variantClasses[variant];

  return (
    <div className={`border rounded-lg p-4 ${styles.container} ${className}`}>
      <div className="flex items-start gap-3">
        {/* Ícone */}
        <span className="text-lg flex-shrink-0 mt-0.5">
          {styles.icon}
        </span>

        {/* Conteúdo */}
        <div className="flex-1 min-w-0">
          {/* Mensagem principal */}
          <h3 className={`font-medium ${styles.text} mb-1`}>
            {message}
          </h3>

          {/* Detalhes (se houver) */}
          {details && (
            <div className="mt-2">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-sm text-gray-400 hover:text-white underline"
              >
                {showDetails ? 'Ocultar detalhes' : 'Ver detalhes'}
              </button>
              
              {showDetails && (
                <div className="mt-2 p-3 bg-gray-800 rounded text-sm text-gray-300 font-mono whitespace-pre-wrap">
                  {details}
                </div>
              )}
            </div>
          )}

          {/* Ações */}
          {(onRetry || onDismiss) && (
            <div className="flex items-center gap-2 mt-3">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className={`
                    px-3 py-1.5 text-sm font-medium text-white rounded
                    ${styles.button} transition-colors duration-200
                  `}
                >
                  🔄 Tentar novamente
                </button>
              )}
              
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className="
                    px-3 py-1.5 text-sm font-medium text-gray-400 
                    hover:text-white transition-colors duration-200
                  "
                >
                  Dispensar
                </button>
              )}
            </div>
          )}
        </div>

        {/* Botão de fechar */}
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-gray-400 hover:text-white flex-shrink-0"
          >
            <span className="text-lg">✕</span>
          </button>
        )}
      </div>
    </div>
  );
};
