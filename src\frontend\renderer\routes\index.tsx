/**
 * Routes Configuration - Configuração de rotas da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Configuração das rotas React Router para a aplicação
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Páginas principais
import { DashboardPage } from '../pages/DashboardPage';
import { PackageManagerPage } from '../pages/PackageManagerPage';
import { SettingsPage } from '../pages/SettingsPage';
import { AboutPage } from '../pages/AboutPage';
import { MonitoringPage } from '../pages/MonitoringPage';

// Páginas de containers
import { ContainersPage } from '../pages/ContainersPage';
import { ContainerListPage } from '../pages/ContainerListPage';
import { ContainerImagesPage } from '../pages/ContainerImagesPage';
import { ContainerEnginesPage } from '../pages/ContainerEnginesPage';

// Página de erro 404
import { NotFoundPage } from '../pages/NotFoundPage';

/**
 * Componente principal de rotas
 */
export const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Rota principal */}
      <Route path="/" element={<DashboardPage />} />
      
      {/* Gerenciamento de pacotes */}
      <Route path="/packages" element={<PackageManagerPage />} />
      
      {/* Gerenciamento de containers */}
      <Route path="/containers" element={<ContainersPage />} />
      <Route path="/containers/list" element={<ContainerListPage />} />
      <Route path="/containers/images" element={<ContainerImagesPage />} />
      <Route path="/containers/engines" element={<ContainerEnginesPage />} />
      
      {/* Monitoramento */}
      <Route path="/monitoring" element={<MonitoringPage />} />
      
      {/* Configurações */}
      <Route path="/settings" element={<SettingsPage />} />
      
      {/* Sobre */}
      <Route path="/about" element={<AboutPage />} />
      
      {/* Redirecionamentos */}
      <Route path="/container" element={<Navigate to="/containers" replace />} />
      <Route path="/docker" element={<Navigate to="/containers" replace />} />
      <Route path="/podman" element={<Navigate to="/containers" replace />} />
      
      {/* Página 404 */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

/**
 * Hook para obter informações da rota atual
 */
export const useCurrentRoute = () => {
  const location = window.location;
  
  return {
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    isContainerRoute: location.pathname.startsWith('/containers'),
    isPackageRoute: location.pathname.startsWith('/packages'),
    isSettingsRoute: location.pathname.startsWith('/settings'),
    isMonitoringRoute: location.pathname.startsWith('/monitoring')
  };
};
