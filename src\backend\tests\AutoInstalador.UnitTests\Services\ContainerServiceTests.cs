using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using AutoInstalador.Infrastructure.External.Containers;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AutoInstalador.UnitTests.Services;

/// <summary>
/// Testes unitários para serviços de container
/// </summary>
public class ContainerServiceTests
{
    private readonly Mock<ILogger<ContainerManagerService>> _loggerMock;
    private readonly Mock<DockerService> _dockerServiceMock;
    private readonly Mock<PodmanService> _podmanServiceMock;
    private readonly Mock<ContainerEngineDetector> _engineDetectorMock;
    private readonly ContainerManagerService _containerService;

    public ContainerServiceTests()
    {
        _loggerMock = new Mock<ILogger<ContainerManagerService>>();
        _dockerServiceMock = new Mock<DockerService>();
        _podmanServiceMock = new Mock<PodmanService>();
        _engineDetectorMock = new Mock<ContainerEngineDetector>();

        _containerService = new ContainerManagerService(
            _loggerMock.Object,
            _dockerServiceMock.Object,
            _podmanServiceMock.Object,
            _engineDetectorMock.Object
        );
    }

    [Fact]
    public async Task ListContainersAsync_WithDockerAvailable_ShouldReturnContainers()
    {
        // Arrange
        var request = new ContainerListRequest { All = true };
        var expectedContainers = new List<Container>
        {
            new Container
            {
                Id = "container1",
                Name = "test-container",
                Image = "nginx:latest",
                Status = ContainerStatus.Running,
                Engine = ContainerEngine.Docker,
                Created = DateTime.UtcNow.AddHours(-1),
                Ports = new List<ContainerPort>(),
                Mounts = new List<ContainerMount>(),
                Networks = new List<ContainerNetwork>(),
                Labels = new Dictionary<string, string>(),
                Environment = new Dictionary<string, string>()
            }
        };

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.ListContainersAsync(It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedContainers);

        // Act
        var result = await _containerService.ListContainersAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.Single(result.Containers);
        Assert.Equal("test-container", result.Containers.First().Name);
        Assert.Equal(ContainerEngine.Docker, result.Engine);
    }

    [Fact]
    public async Task ListContainersAsync_WithNoEngineAvailable_ShouldReturnError()
    {
        // Arrange
        var request = new ContainerListRequest { All = false };

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);
        _podmanServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _containerService.ListContainersAsync(request);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Nenhum engine de container disponível", result.Message);
        Assert.Empty(result.Containers);
    }

    [Fact]
    public async Task RunContainerAsync_WithValidRequest_ShouldCreateContainer()
    {
        // Arrange
        var request = new ContainerRunRequest
        {
            Image = "nginx:latest",
            Name = "test-nginx",
            Engine = ContainerEngine.Docker,
            Ports = new List<ContainerPortMapping>
            {
                new ContainerPortMapping { HostPort = 8080, ContainerPort = 80, Protocol = "tcp" }
            },
            Environment = new Dictionary<string, string>
            {
                { "ENV_VAR", "test_value" }
            },
            Detached = true
        };

        var expectedContainer = new Container
        {
            Id = "new-container-id",
            Name = "test-nginx",
            Image = "nginx:latest",
            Status = ContainerStatus.Running,
            Engine = ContainerEngine.Docker,
            Created = DateTime.UtcNow,
            Ports = new List<ContainerPort>
            {
                new ContainerPort { HostPort = 8080, ContainerPort = 80, Protocol = "tcp" }
            },
            Mounts = new List<ContainerMount>(),
            Networks = new List<ContainerNetwork>(),
            Labels = new Dictionary<string, string>(),
            Environment = new Dictionary<string, string> { { "ENV_VAR", "test_value" } }
        };

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.RunContainerAsync(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string[]>(),
            It.IsAny<Dictionary<string, string>>(),
            It.IsAny<List<ContainerPortMapping>>(),
            It.IsAny<List<ContainerVolumeMapping>>(),
            It.IsAny<bool>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedContainer);

        // Act
        var result = await _containerService.RunContainerAsync(request);

        // Assert
        Assert.True(result.Success);
        Assert.Equal("new-container-id", result.ContainerId);
        Assert.Equal("test-nginx", result.ContainerName);
        Assert.Equal(ContainerEngine.Docker, result.Engine);
    }

    [Fact]
    public async Task StartContainerAsync_WithValidId_ShouldStartContainer()
    {
        // Arrange
        var containerId = "test-container-id";

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.StartContainerAsync(containerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _containerService.StartContainerAsync(containerId);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(containerId, result.ContainerId);
        Assert.Contains("iniciado com sucesso", result.Message);
    }

    [Fact]
    public async Task StopContainerAsync_WithValidId_ShouldStopContainer()
    {
        // Arrange
        var containerId = "test-container-id";
        var timeout = 30;

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.StopContainerAsync(containerId, timeout, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _containerService.StopContainerAsync(containerId, timeout);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(containerId, result.ContainerId);
        Assert.Contains("parado com sucesso", result.Message);
    }

    [Fact]
    public async Task GetContainerAsync_WithValidId_ShouldReturnContainer()
    {
        // Arrange
        var containerId = "test-container-id";
        var expectedContainer = new Container
        {
            Id = containerId,
            Name = "test-container",
            Image = "nginx:latest",
            Status = ContainerStatus.Running,
            Engine = ContainerEngine.Docker,
            Created = DateTime.UtcNow.AddHours(-1),
            Ports = new List<ContainerPort>(),
            Mounts = new List<ContainerMount>(),
            Networks = new List<ContainerNetwork>(),
            Labels = new Dictionary<string, string>(),
            Environment = new Dictionary<string, string>()
        };

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.GetContainerAsync(containerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedContainer);

        // Act
        var result = await _containerService.GetContainerAsync(containerId);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Container);
        Assert.Equal(containerId, result.Container.Id);
        Assert.Equal("test-container", result.Container.Name);
    }

    [Fact]
    public async Task RemoveContainerAsync_WithForce_ShouldRemoveContainer()
    {
        // Arrange
        var containerId = "test-container-id";
        var force = true;

        _dockerServiceMock.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _dockerServiceMock.Setup(x => x.RemoveContainerAsync(containerId, force, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _containerService.RemoveContainerAsync(containerId, force);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(containerId, result.ContainerId);
        Assert.Contains("removido com sucesso", result.Message);
    }

    [Theory]
    [InlineData(ContainerEngine.Docker)]
    [InlineData(ContainerEngine.Podman)]
    public async Task GetContainerStatsAsync_WithDifferentEngines_ShouldReturnStats(ContainerEngine engine)
    {
        // Arrange
        var containerId = "test-container-id";
        var expectedStats = new ContainerStats
        {
            ContainerId = containerId,
            Timestamp = DateTime.UtcNow,
            Cpu = new CpuStats { Usage = 25.5, OnlineCpus = 4 },
            Memory = new MemoryStats { Usage = 1024 * 1024 * 512, Limit = 1024 * 1024 * 1024, Percentage = 50.0, Cache = 1024 * 1024 * 100 },
            Network = new NetworkStats { RxBytes = 1024, TxBytes = 2048, RxPackets = 10, TxPackets = 15 },
            BlockIO = new BlockIOStats { ReadBytes = 4096, WriteBytes = 8192, ReadOps = 5, WriteOps = 10 },
            Pids = 25
        };

        var engineService = engine == ContainerEngine.Docker ? _dockerServiceMock : _podmanServiceMock;
        
        engineService.Setup(x => x.IsAvailableAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        engineService.Setup(x => x.GetContainerStatsAsync(containerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedStats);

        // Act
        var result = await _containerService.GetContainerStatsAsync(containerId, engine);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Stats);
        Assert.Equal(containerId, result.Stats.ContainerId);
        Assert.Equal(25.5, result.Stats.Cpu.Usage);
        Assert.Equal(50.0, result.Stats.Memory.Percentage);
    }
}
