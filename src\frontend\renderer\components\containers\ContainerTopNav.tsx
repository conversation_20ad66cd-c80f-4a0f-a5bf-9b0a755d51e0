/**
 * Container Top Navigation - Navegação superior do dashboard de containers
 * Auto-Instalador V3 Lite
 * 
 * @description Navegação superior com abas para diferentes seções do gerenciamento de containers
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { useRunContainer } from '../../services/container-service';
import { toast } from 'react-hot-toast';

interface ContainerTopNavProps {
  activeTab: 'containers' | 'images' | 'volumes' | 'networks' | 'templates';
  onTabChange: (tab: 'containers' | 'images' | 'volumes' | 'networks' | 'templates') => void;
  className?: string;
}

export const ContainerTopNav: React.FC<ContainerTopNavProps> = ({
  activeTab,
  onTabChange,
  className = ''
}) => {
  const runContainer = useRunContainer();

  // Definição das abas
  const tabs = [
    { id: 'containers' as const, label: 'Containers', icon: '📦' },
    { id: 'images' as const, label: 'Images', icon: '🖼️' },
    { id: 'volumes' as const, label: 'Volumes', icon: '💾' },
    { id: 'networks' as const, label: 'Networks', icon: '🌐' },
    { id: 'templates' as const, label: 'Templates', icon: '📋' }
  ];

  // Handler para criar novo container
  const handleNewContainer = () => {
    // TODO: Implementar modal de criação de container
    toast.info('Modal de criação de container em desenvolvimento');
  };

  // Handler para mudança de aba
  const handleTabClick = (tabId: typeof activeTab) => {
    onTabChange(tabId);
  };

  return (
    <nav className={`bg-gray-700 border-b border-gray-600 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Abas de navegação */}
        <div className="flex">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={`
                px-5 py-3 text-sm font-medium border-r border-gray-600 transition-all duration-200
                ${activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:bg-gray-600 hover:text-white'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Ações da navegação */}
        <div className="flex items-center gap-3 px-4">
          {/* Botão de novo container - apenas na aba containers */}
          {activeTab === 'containers' && (
            <button
              onClick={handleNewContainer}
              disabled={runContainer.isLoading}
              className="
                px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed
                transition-colors duration-200 flex items-center gap-2
              "
            >
              <span className="text-lg">+</span>
              Novo Container
            </button>
          )}

          {/* Botão de nova imagem - apenas na aba images */}
          {activeTab === 'images' && (
            <button
              onClick={() => toast.info('Funcionalidade em desenvolvimento')}
              className="
                px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2
              "
            >
              <span className="text-lg">+</span>
              Pull Image
            </button>
          )}

          {/* Botão de novo volume - apenas na aba volumes */}
          {activeTab === 'volumes' && (
            <button
              onClick={() => toast.info('Funcionalidade em desenvolvimento')}
              className="
                px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2
              "
            >
              <span className="text-lg">+</span>
              Novo Volume
            </button>
          )}

          {/* Botão de nova rede - apenas na aba networks */}
          {activeTab === 'networks' && (
            <button
              onClick={() => toast.info('Funcionalidade em desenvolvimento')}
              className="
                px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2
              "
            >
              <span className="text-lg">+</span>
              Nova Rede
            </button>
          )}

          {/* Botão de novo template - apenas na aba templates */}
          {activeTab === 'templates' && (
            <button
              onClick={() => toast.info('Funcionalidade em desenvolvimento')}
              className="
                px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md
                hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2
              "
            >
              <span className="text-lg">+</span>
              Novo Template
            </button>
          )}

          {/* Indicador de status do engine */}
          <div className="flex items-center gap-2 px-3 py-1 bg-gray-800 rounded-md">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-xs text-gray-300 font-medium">
              Docker Ativo
            </span>
          </div>
        </div>
      </div>
    </nav>
  );
};
