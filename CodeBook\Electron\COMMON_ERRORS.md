# Electron 37.1.2 - <PERSON><PERSON><PERSON> Comuns e Soluções

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 37.1.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://www.electronjs.org/
- **GitHub:** https://github.com/electron/electron
- **Documentação:** https://www.electronjs.org/docs/latest/
- **NPM/Package:** https://www.npmjs.com/package/electron
- **Fórum/Community:** https://github.com/electron/electron/discussions
- **Stack Overflow Tag:** `electron`

---

## 🚨 **ERROS CRÍTICOS ELECTRON 37.1.2**

### **1. Context Isolation Errors**

#### **Erro:**
```
Error: contextBridge is not defined
ReferenceError: require is not defined in renderer process
```

#### **Causa:**
Context isolation obrigatório na v37, mas preload script não configurado corretamente.

#### **Solução:**
```typescript
// ❌ ERRADO - Configuração antiga
const window = new BrowserWindow({
  webPreferences: {
    nodeIntegration: true,
    contextIsolation: false
  }
});

// ✅ CORRETO - Configuração v37
const window = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,        // Obrigatório
    contextIsolation: true,        // Obrigatório
    preload: path.join(__dirname, 'preload.js')  // Necessário
  }
});

// preload.js
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args)
});
```

---

### **2. IPC Communication Failures**

#### **Erro:**
```
Error: Cannot read property 'invoke' of undefined
TypeError: window.electronAPI is undefined
```

#### **Causa:**
Renderer process tentando acessar APIs antes do preload script carregar.

#### **Solução:**
```typescript
// ❌ ERRADO - Acesso imediato
function App() {
  const data = window.electronAPI.invoke('get-data'); // Erro!
  return <div>{data}</div>;
}

// ✅ CORRETO - Verificação e aguardar
function App() {
  const [data, setData] = useState(null);
  const [isElectron, setIsElectron] = useState(false);

  useEffect(() => {
    // Verificar se Electron API está disponível
    if (typeof window !== 'undefined' && window.electronAPI) {
      setIsElectron(true);
      
      // Aguardar e fazer chamada
      window.electronAPI.invoke('get-data')
        .then(setData)
        .catch(console.error);
    }
  }, []);

  if (!isElectron) {
    return <div>Carregando...</div>;
  }

  return <div>{data}</div>;
}
```

---

### **3. Security Policy Violations**

#### **Erro:**
```
Refused to load the script because it violates the following Content Security Policy directive
```

#### **Causa:**
CSP muito restritivo ou configuração de segurança inadequada.

#### **Solução:**
```typescript
// Configurar CSP adequado para desenvolvimento
session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
  callback({
    responseHeaders: {
      ...details.responseHeaders,
      'Content-Security-Policy': [
        "default-src 'self' 'unsafe-inline' data:; " +
        "script-src 'self' 'unsafe-eval' 'unsafe-inline'; " +
        "connect-src 'self' http://localhost:* ws://localhost:*; " +
        "img-src 'self' data: https:;"
      ]
    }
  });
});
```

---

### **4. Module Resolution Errors**

#### **Erro:**
```
Error: Cannot resolve module 'electron'
Module not found: Can't resolve 'fs' in renderer process
```

#### **Causa:**
Tentativa de importar módulos Node.js no renderer process.

#### **Solução:**
```typescript
// ❌ ERRADO - Import direto no renderer
import { ipcRenderer } from 'electron'; // Erro!
import fs from 'fs'; // Erro!

// ✅ CORRETO - Usar preload script
// preload.ts
import { contextBridge, ipcRenderer } from 'electron';
import fs from 'fs';

contextBridge.exposeInMainWorld('electronAPI', {
  readFile: (path: string) => fs.readFileSync(path, 'utf8'),
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args)
});

// renderer (React)
const content = window.electronAPI.readFile('/path/to/file');
```

---

### **5. Build and Distribution Errors**

#### **Erro:**
```
Error: Application entry file "dist/main.js" does not exist
Cannot find module './app.asar'
```

#### **Causa:**
Configuração incorreta do electron-builder ou paths errados.

#### **Solução:**
```json
// package.json
{
  "main": "dist/electron/main/main.js",
  "scripts": {
    "build:electron": "tsc -p tsconfig.electron.json",
    "build": "npm run build:react && npm run build:electron",
    "dist": "npm run build && electron-builder"
  },
  "build": {
    "files": [
      "dist/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "directories": {
      "output": "release"
    }
  }
}

// tsconfig.electron.json
{
  "compilerOptions": {
    "outDir": "dist/electron",
    "rootDir": "src/electron"
  },
  "include": ["src/electron/**/*"]
}
```

---

### **6. Auto-Updater Issues**

#### **Erro:**
```
Error: Cannot check for updates - no update server configured
Error: Update download failed
```

#### **Causa:**
Configuração incorreta do auto-updater ou servidor de updates.

#### **Solução:**
```typescript
// Configuração correta do auto-updater
import { autoUpdater } from 'electron-updater';

// Para desenvolvimento - desabilitar
if (process.env.NODE_ENV === 'development') {
  autoUpdater.updateConfigPath = path.join(__dirname, 'dev-app-update.yml');
}

// Para produção
autoUpdater.setFeedURL({
  provider: 'github',
  owner: 'auto-instalador-v3',
  repo: 'desktop-app',
  private: false
});

// Error handling
autoUpdater.on('error', (error) => {
  console.error('Auto-updater error:', error);
  // Não quebrar a aplicação por erro de update
});
```

---

### **7. Performance Issues**

#### **Erro:**
```
High memory usage detected
Application becomes unresponsive
```

#### **Causa:**
Memory leaks ou configuração inadequada para hardware.

#### **Solução:**
```typescript
// Configurações de performance para i5 12th Gen
app.commandLine.appendSwitch('--max-old-space-size', '4096');
app.commandLine.appendSwitch('--max-semi-space-size', '512');

// Memory monitoring
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
  
  console.log(`Memory usage: ${heapUsedMB.toFixed(1)}MB`);
  
  // Force GC if memory usage is high
  if (heapUsedMB > 800 && global.gc) {
    global.gc();
  }
}, 30000);

// Cleanup event listeners
window.on('closed', () => {
  // Remove all listeners
  ipcMain.removeAllListeners();
});
```

---

### **8. React Integration Problems**

#### **Erro:**
```
React DevTools not working in Electron
Hot reload not working
```

#### **Causa:**
Configuração inadequada do ambiente de desenvolvimento.

#### **Solução:**
```typescript
// main.ts - Development setup
if (process.env.NODE_ENV === 'development') {
  // Install React DevTools
  const { default: installExtension, REACT_DEVELOPER_TOOLS } = require('electron-devtools-installer');
  
  app.whenReady().then(() => {
    installExtension(REACT_DEVELOPER_TOOLS)
      .then((name) => console.log(`Added Extension: ${name}`))
      .catch((err) => console.log('An error occurred: ', err));
  });

  // Enable hot reload
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

// Vite config for Electron
export default defineConfig({
  base: './',
  server: {
    port: 3000,
    strictPort: true
  },
  build: {
    outDir: 'dist/renderer'
  }
});
```

---

### **9. File Path Issues**

#### **Erro:**
```
Error: ENOENT: no such file or directory
Path resolution failed in packaged app
```

#### **Causa:**
Paths relativos não funcionam em app empacotado.

#### **Solução:**
```typescript
// ❌ ERRADO - Path relativo
const iconPath = './assets/icon.png';

// ✅ CORRETO - Path absoluto
const iconPath = path.join(__dirname, '../assets/icon.png');

// Para recursos em app empacotado
const getAssetPath = (asset: string): string => {
  if (process.env.NODE_ENV === 'development') {
    return path.join(__dirname, '../assets', asset);
  } else {
    return path.join(process.resourcesPath, 'assets', asset);
  }
};

const iconPath = getAssetPath('icon.png');
```

---

### **10. TypeScript Configuration Errors**

#### **Erro:**
```
Cannot find module 'electron' or its corresponding type declarations
Property 'electronAPI' does not exist on type 'Window'
```

#### **Causa:**
Configuração inadequada do TypeScript para Electron.

#### **Solução:**
```typescript
// types/electron.d.ts
export interface ElectronAPI {
  invoke: (channel: string, ...args: any[]) => Promise<any>;
  on: (channel: string, callback: (...args: any[]) => void) => () => void;
  platform: string;
  version: string;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

// tsconfig.json
{
  "compilerOptions": {
    "types": ["electron", "node"],
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  },
  "include": [
    "src/**/*",
    "types/**/*"
  ]
}
```

---

## 🔧 **DEBUGGING TOOLS**

### **Console Debugging**
```typescript
// Enable detailed logging
process.env.ELECTRON_ENABLE_LOGGING = 'true';

// Debug IPC communication
ipcMain.on('*', (event, ...args) => {
  console.log('IPC Event:', event.type, args);
});
```

### **Performance Monitoring**
```typescript
// Monitor performance
const { PerformanceObserver, performance } = require('perf_hooks');

const obs = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    console.log(`${entry.name}: ${entry.duration}ms`);
  });
});

obs.observe({ entryTypes: ['measure'] });
```

---

## 📋 **CHECKLIST DE TROUBLESHOOTING**

### **Antes de Reportar Bug:**
- [ ] Verificar versão do Electron (deve ser 37.1.2)
- [ ] Verificar configuração de segurança (contextIsolation: true)
- [ ] Testar em ambiente limpo
- [ ] Verificar logs do console
- [ ] Testar em diferentes plataformas
- [ ] Verificar configuração do preload script
- [ ] Validar paths de arquivos
- [ ] Confirmar configuração do TypeScript

### **Informações para Suporte:**
- Versão do Electron
- Sistema operacional
- Versão do Node.js
- Configuração do BrowserWindow
- Código do preload script
- Logs de erro completos
- Steps para reproduzir

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - Electron 37.1.2 Common Errors
