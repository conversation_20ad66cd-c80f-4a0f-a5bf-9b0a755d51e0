/**
 * Contratos de API compartilhados
 * Auto-Instalador V3 Lite
 * 
 * @description Definições de contratos para comunicação Frontend-Backend
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import {
  BaseResponse,
  PaginatedResponse,
  Package,
  Installation,
  SystemInfo,
  AppConfiguration,
  SearchPackagesRequest,
  InstallPackageRequest,
  BatchInstallRequest,
  ConfigurationRequest,
  PackageCategory,
  InstallationStatus
} from '../types/api.types';

// ============================================================================
// PACKAGE CONTRACTS
// ============================================================================

export namespace PackageContracts {
  
  export interface SearchRequest extends SearchPackagesRequest {}
  
  export interface SearchResponse extends PaginatedResponse<Package> {
    facets: {
      categories: Array<{ category: PackageCategory; count: number }>;
      publishers: Array<{ publisher: string; count: number }>;
      tags: Array<{ tag: string; count: number }>;
    };
  }
  
  export interface GetByIdRequest {
    id: string;
    includeMetadata?: boolean;
  }
  
  export interface GetByIdResponse extends BaseResponse<Package> {
    metadata?: {
      downloadCount: number;
      rating: number;
      reviews: number;
      lastChecked: string;
    };
  }
  
  export interface GetCategoriesResponse extends BaseResponse<PackageCategory[]> {}
  
  export interface GetPopularRequest {
    category?: PackageCategory;
    limit?: number;
    timeframe?: 'day' | 'week' | 'month' | 'year';
  }
  
  export interface GetPopularResponse extends BaseResponse<Package[]> {}
}

// ============================================================================
// INSTALLATION CONTRACTS
// ============================================================================

export namespace InstallationContracts {
  
  export interface InstallRequest extends InstallPackageRequest {}
  
  export interface InstallResponse extends BaseResponse<Installation> {
    estimatedDuration: number; // seconds
    requiredSpace: number; // MB
    warnings?: string[];
  }
  
  export interface BatchInstallRequest extends BatchInstallRequest {}
  
  export interface BatchInstallResponse extends BaseResponse<Installation[]> {
    summary: {
      totalPackages: number;
      estimatedDuration: number;
      requiredSpace: number;
      conflicts: Array<{
        packageId: string;
        reason: string;
      }>;
    };
  }
  
  export interface GetStatusRequest {
    id: string;
  }
  
  export interface GetStatusResponse extends BaseResponse<Installation> {}
  
  export interface CancelRequest {
    id: string;
    reason?: string;
  }
  
  export interface CancelResponse extends BaseResponse<boolean> {}
  
  export interface GetLogsRequest {
    id: string;
    level?: 'debug' | 'info' | 'warning' | 'error';
    limit?: number;
    offset?: number;
  }
  
  export interface GetLogsResponse extends BaseResponse<Installation['logs']> {}
  
  export interface GetActiveInstallationsResponse extends BaseResponse<Installation[]> {}
  
  export interface GetInstallationHistoryRequest {
    page: number;
    pageSize: number;
    status?: InstallationStatus;
    packageId?: string;
    dateFrom?: string;
    dateTo?: string;
  }
  
  export interface GetInstallationHistoryResponse extends PaginatedResponse<Installation> {}
}

// ============================================================================
// SYSTEM CONTRACTS
// ============================================================================

export namespace SystemContracts {
  
  export interface GetInfoResponse extends BaseResponse<SystemInfo> {}
  
  export interface GetHealthResponse extends BaseResponse<{
    status: 'healthy' | 'warning' | 'critical';
    checks: Array<{
      name: string;
      status: 'pass' | 'fail' | 'warn';
      message?: string;
      duration: number; // ms
    }>;
    uptime: number; // seconds
    version: string;
  }> {}
  
  export interface CheckRequirementsRequest {
    packageIds: string[];
  }
  
  export interface CheckRequirementsResponse extends BaseResponse<{
    compatible: boolean;
    issues: Array<{
      packageId: string;
      requirement: string;
      current: string;
      severity: 'error' | 'warning';
      message: string;
    }>;
    recommendations: string[];
  }> {}
  
  export interface GetPerformanceMetricsResponse extends BaseResponse<{
    cpu: {
      usage: number; // percentage
      temperature?: number; // celsius
    };
    memory: {
      usage: number; // percentage
      available: number; // MB
    };
    disk: {
      usage: number; // percentage
      readSpeed: number; // MB/s
      writeSpeed: number; // MB/s
    };
    network: {
      downloadSpeed: number; // MB/s
      uploadSpeed: number; // MB/s
      latency: number; // ms
    };
  }> {}
}

// ============================================================================
// CONFIGURATION CONTRACTS
// ============================================================================

export namespace ConfigurationContracts {
  
  export interface GetResponse extends BaseResponse<AppConfiguration> {}
  
  export interface UpdateRequest extends ConfigurationRequest {}
  
  export interface UpdateResponse extends BaseResponse<AppConfiguration> {
    restartRequired: boolean;
    changedSettings: string[];
  }
  
  export interface ResetRequest {
    section?: keyof AppConfiguration;
    confirmReset: boolean;
  }
  
  export interface ResetResponse extends BaseResponse<AppConfiguration> {}
  
  export interface ValidateRequest {
    configuration: Partial<AppConfiguration>;
  }
  
  export interface ValidateResponse extends BaseResponse<{
    valid: boolean;
    errors: Array<{
      field: string;
      message: string;
      code: string;
    }>;
    warnings: Array<{
      field: string;
      message: string;
    }>;
  }> {}
}

// ============================================================================
// WEBSOCKET CONTRACTS
// ============================================================================

export namespace WebSocketContracts {
  
  export interface ClientMessage<T = any> {
    type: string;
    id: string;
    payload: T;
    timestamp: string;
  }
  
  export interface ServerMessage<T = any> {
    type: string;
    id?: string; // Response to client message
    payload: T;
    timestamp: string;
  }
  
  // Client -> Server messages
  export interface SubscribeToInstallationUpdates {
    installationId: string;
  }
  
  export interface UnsubscribeFromInstallationUpdates {
    installationId: string;
  }
  
  export interface SubscribeToSystemMetrics {
    interval: number; // seconds
  }
  
  // Server -> Client messages
  export interface InstallationProgressUpdate {
    installationId: string;
    progress: number;
    status: InstallationStatus;
    message?: string;
    estimatedTimeRemaining?: number;
  }
  
  export interface SystemMetricsUpdate {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkActivity: boolean;
    timestamp: string;
  }
  
  export interface NotificationMessage {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    actions?: Array<{
      label: string;
      action: string;
    }>;
    autoClose?: number; // seconds
  }
}

// ============================================================================
// ERROR CONTRACTS
// ============================================================================

export namespace ErrorContracts {
  
  export interface ValidationError {
    field: string;
    message: string;
    code: string;
    value?: any;
  }
  
  export interface BusinessError {
    code: string;
    message: string;
    details?: Record<string, any>;
    retryable: boolean;
  }
  
  export interface SystemError {
    code: string;
    message: string;
    stackTrace?: string;
    innerException?: SystemError;
    timestamp: string;
  }
  
  export interface ApiErrorResponse extends BaseResponse<never> {
    success: false;
    error: {
      type: 'validation' | 'business' | 'system' | 'network';
      code: string;
      message: string;
      details?: any;
      retryable: boolean;
    };
  }
}

// ============================================================================
// TYPE GUARDS
// ============================================================================

export namespace TypeGuards {
  
  export function isBaseResponse<T>(obj: any): obj is BaseResponse<T> {
    return obj && typeof obj === 'object' && typeof obj.success === 'boolean';
  }
  
  export function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {
    return isBaseResponse(obj) && obj.pagination && typeof obj.pagination === 'object';
  }
  
  export function isApiErrorResponse(obj: any): obj is ErrorContracts.ApiErrorResponse {
    return isBaseResponse(obj) && obj.success === false && obj.error;
  }
  
  export function isWebSocketMessage<T>(obj: any): obj is WebSocketContracts.ServerMessage<T> {
    return obj && typeof obj === 'object' && 
           typeof obj.type === 'string' && 
           typeof obj.timestamp === 'string';
  }
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const API_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_TIMEOUT: 30000, // ms
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // ms
  WEBSOCKET_RECONNECT_DELAY: 5000, // ms
  MAX_LOG_ENTRIES: 1000,
  SUPPORTED_FILE_TYPES: ['.exe', '.msi', '.zip', '.tar.gz'],
  MIN_DISK_SPACE: 1024, // MB
  MIN_MEMORY: 512 // MB
} as const;
