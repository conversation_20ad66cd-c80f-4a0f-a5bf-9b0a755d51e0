/**
 * Tipos compartilhados entre Frontend e Backend
 * Auto-Instalador V3 Lite
 * 
 * @description Definições de tipos para comunicação entre camadas
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

// ============================================================================
// BASE TYPES
// ============================================================================

export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  timestamp: string;
}

export interface PaginatedResponse<T> extends BaseResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// ============================================================================
// PACKAGE TYPES
// ============================================================================

export interface Package {
  id: string;
  name: string;
  displayName: string;
  description: string;
  version: string;
  category: PackageCategory;
  status: PackageStatus;
  size: number;
  downloadUrl: string;
  installCommand: string;
  dependencies: string[];
  tags: string[];
  publisher: string;
  publishedDate: string;
  lastUpdated: string;
  isRequired: boolean;
  isRecommended: boolean;
  systemRequirements: SystemRequirements;
}

export enum PackageCategory {
  Development = 'development',
  Productivity = 'productivity',
  Media = 'media',
  Gaming = 'gaming',
  Security = 'security',
  Utilities = 'utilities',
  Communication = 'communication',
  Education = 'education'
}

export enum PackageStatus {
  Available = 'available',
  Installing = 'installing',
  Installed = 'installed',
  Failed = 'failed',
  Updating = 'updating',
  Uninstalling = 'uninstalling'
}

export interface SystemRequirements {
  minRam: number; // MB
  minStorage: number; // MB
  supportedOs: OperatingSystem[];
  requiredFeatures: string[];
}

export enum OperatingSystem {
  Windows10 = 'windows10',
  Windows11 = 'windows11',
  Ubuntu = 'ubuntu',
  MacOS = 'macos'
}

// ============================================================================
// INSTALLATION TYPES
// ============================================================================

export interface Installation {
  id: string;
  packageId: string;
  status: InstallationStatus;
  progress: number; // 0-100
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  logs: InstallationLog[];
  estimatedTimeRemaining?: number; // seconds
}

export enum InstallationStatus {
  Queued = 'queued',
  Downloading = 'downloading',
  Installing = 'installing',
  Completed = 'completed',
  Failed = 'failed',
  Cancelled = 'cancelled'
}

export interface InstallationLog {
  timestamp: string;
  level: LogLevel;
  message: string;
  details?: Record<string, any>;
}

export enum LogLevel {
  Debug = 'debug',
  Info = 'info',
  Warning = 'warning',
  Error = 'error'
}

// ============================================================================
// SYSTEM TYPES
// ============================================================================

export interface SystemInfo {
  os: {
    name: string;
    version: string;
    architecture: string;
    platform: string;
  };
  hardware: {
    cpu: {
      model: string;
      cores: number;
      threads: number;
      frequency: number; // GHz
    };
    memory: {
      total: number; // MB
      available: number; // MB
      usage: number; // percentage
    };
    storage: {
      drives: StorageDrive[];
      totalSpace: number; // MB
      availableSpace: number; // MB
    };
  };
  runtime: {
    nodeVersion: string;
    electronVersion: string;
    dotnetVersion: string;
  };
}

export interface StorageDrive {
  letter: string;
  type: 'HDD' | 'SSD' | 'USB' | 'Network';
  totalSize: number; // MB
  availableSize: number; // MB
  usage: number; // percentage
}

// ============================================================================
// REQUEST/RESPONSE TYPES
// ============================================================================

export interface SearchPackagesRequest {
  query?: string;
  category?: PackageCategory;
  tags?: string[];
  page: number;
  pageSize: number;
  sortBy: 'name' | 'popularity' | 'date' | 'size';
  sortOrder: 'asc' | 'desc';
}

export interface InstallPackageRequest {
  packageId: string;
  installDependencies: boolean;
  customInstallPath?: string;
  installOptions?: Record<string, any>;
}

export interface BatchInstallRequest {
  packages: InstallPackageRequest[];
  continueOnError: boolean;
}

export interface ConfigurationRequest {
  downloadPath: string;
  installPath: string;
  autoUpdate: boolean;
  parallelInstalls: number;
  retryAttempts: number;
  logLevel: LogLevel;
}

export interface ContainerRunRequest {
  image: string;
  name?: string;
  ports?: ContainerPort[];
  volumes?: ContainerVolume[];
  environment?: KeyValuePair[];
  command?: string;
  args?: string[];
  workingDir?: string;
  user?: string;
  detached?: boolean;
  interactive?: boolean;
  tty?: boolean;
  remove?: boolean;
  restartPolicy?: RestartPolicy;
  resources?: ContainerResources;
  networkMode?: string;
  labels?: KeyValuePair[];
  engine?: ContainerEngine;
}

export interface ContainerListRequest {
  all?: boolean;
  filters?: {
    status?: ContainerStatus[];
    name?: string;
    image?: string;
    label?: string[];
  };
  engine?: ContainerEngine;
}

export interface ContainerLogsRequest {
  containerId: string;
  follow?: boolean;
  tail?: number;
  since?: string;
  until?: string;
  timestamps?: boolean;
  engine?: ContainerEngine;
}

export interface ContainerStatsRequest {
  containerId: string;
  stream?: boolean;
  engine?: ContainerEngine;
}

export interface ContainerActionRequest {
  containerId: string;
  action: 'start' | 'stop' | 'restart' | 'pause' | 'unpause' | 'remove';
  force?: boolean;
  timeout?: number;
  engine?: ContainerEngine;
}

export interface ContainerEngineInstallRequest {
  engine: ContainerEngine;
  platform: 'windows' | 'macos' | 'linux';
  packageManager?: 'winget' | 'choco' | 'brew' | 'apt' | 'dnf' | 'yum';
  force?: boolean;
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface AppConfiguration {
  general: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoStart: boolean;
    minimizeToTray: boolean;
  };
  installation: {
    defaultPath: string;
    parallelInstalls: number;
    retryAttempts: number;
    autoInstallDependencies: boolean;
    verifyChecksums: boolean;
  };
  network: {
    timeout: number; // seconds
    retryDelay: number; // seconds
    useProxy: boolean;
    proxyUrl?: string;
  };
  logging: {
    level: LogLevel;
    maxFileSize: number; // MB
    maxFiles: number;
    enableConsole: boolean;
  };
}

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: string;
  source: 'frontend' | 'backend' | 'system';
}

export interface InstallationProgressEvent {
  installationId: string;
  packageName: string;
  progress: number;
  status: InstallationStatus;
  message?: string;
}

export interface SystemStatusEvent {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkActivity: boolean;
}

// ============================================================================
// CONTAINER TYPES
// ============================================================================

export interface Container {
  id: string;
  name: string;
  image: string;
  imageId: string;
  status: ContainerStatus;
  state: ContainerState;
  ports: ContainerPort[];
  volumes: ContainerVolume[];
  environment: KeyValuePair[];
  labels: KeyValuePair[];
  createdAt: string;
  startedAt?: string;
  finishedAt?: string;
  exitCode?: number;
  engine: ContainerEngine;
  platform: string;
  command: string;
  args: string[];
  workingDir?: string;
  user?: string;
  networkMode: string;
  restartPolicy: RestartPolicy;
  resources: ContainerResources;
}

export enum ContainerStatus {
  Created = 'created',
  Running = 'running',
  Paused = 'paused',
  Restarting = 'restarting',
  Removing = 'removing',
  Dead = 'dead',
  Exited = 'exited'
}

export enum ContainerState {
  Created = 'created',
  Running = 'running',
  Paused = 'paused',
  Restarting = 'restarting',
  Removing = 'removing',
  Dead = 'dead',
  Exited = 'exited',
  Unknown = 'unknown'
}

export enum ContainerEngine {
  Docker = 'docker',
  Podman = 'podman'
}

export interface ContainerPort {
  containerPort: number;
  hostPort?: number;
  hostIp?: string;
  protocol: 'tcp' | 'udp';
  type: 'published' | 'exposed';
}

export interface ContainerVolume {
  source: string;
  destination: string;
  mode: 'ro' | 'rw';
  type: 'bind' | 'volume' | 'tmpfs';
}

export interface ContainerResources {
  memory?: number; // bytes
  memorySwap?: number; // bytes
  cpuShares?: number;
  cpuQuota?: number;
  cpuPeriod?: number;
  cpusetCpus?: string;
  cpusetMems?: string;
  blkioWeight?: number;
  oomKillDisable?: boolean;
}

export enum RestartPolicy {
  No = 'no',
  Always = 'always',
  OnFailure = 'on-failure',
  UnlessStopped = 'unless-stopped'
}

export interface ContainerStats {
  containerId: string;
  name: string;
  cpu: {
    usage: number; // percentage
    systemUsage: number;
    onlineCpus: number;
    throttledTime: number;
  };
  memory: {
    usage: number; // bytes
    limit: number; // bytes
    percentage: number;
    cache: number;
    swap?: number;
  };
  network: {
    rxBytes: number;
    txBytes: number;
    rxPackets: number;
    txPackets: number;
    rxErrors: number;
    txErrors: number;
  };
  blockIO: {
    readBytes: number;
    writeBytes: number;
    readOps: number;
    writeOps: number;
  };
  pids: number;
  timestamp: string;
}

export interface ContainerImage {
  id: string;
  repository: string;
  tag: string;
  digest?: string;
  size: number; // bytes
  created: string;
  labels: KeyValuePair[];
  architecture: string;
  os: string;
  engine: ContainerEngine;
}

export interface ContainerLog {
  timestamp: string;
  stream: 'stdout' | 'stderr';
  message: string;
  containerId: string;
}

export interface ContainerEngineInfo {
  engine: ContainerEngine;
  version: string;
  apiVersion: string;
  isAvailable: boolean;
  isRunning: boolean;
  rootless: boolean;
  storageDriver: string;
  cgroupVersion: string;
  platform: {
    name: string;
    architecture: string;
    os: string;
  };
  runtimeInfo: {
    name: string;
    version: string;
  };
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export interface KeyValuePair<T = string> {
  key: string;
  value: T;
}

export interface SelectOption {
  label: string;
  value: string;
  disabled?: boolean;
  group?: string;
}

// ============================================================================
// API ENDPOINTS
// ============================================================================

export const API_ENDPOINTS = {
  PACKAGES: {
    SEARCH: '/api/packages/search',
    GET_BY_ID: '/api/packages/:id',
    GET_CATEGORIES: '/api/packages/categories',
    GET_POPULAR: '/api/packages/popular'
  },
  INSTALLATION: {
    INSTALL: '/api/installation/install',
    BATCH_INSTALL: '/api/installation/batch',
    GET_STATUS: '/api/installation/:id/status',
    CANCEL: '/api/installation/:id/cancel',
    GET_LOGS: '/api/installation/:id/logs'
  },
  SYSTEM: {
    INFO: '/api/system/info',
    HEALTH: '/api/system/health',
    REQUIREMENTS: '/api/system/requirements'
  },
  CONFIGURATION: {
    GET: '/api/configuration',
    UPDATE: '/api/configuration',
    RESET: '/api/configuration/reset'
  },
  CONTAINERS: {
    LIST: '/api/containers',
    GET_BY_ID: '/api/containers/:id',
    RUN: '/api/containers/run',
    START: '/api/containers/:id/start',
    STOP: '/api/containers/:id/stop',
    RESTART: '/api/containers/:id/restart',
    PAUSE: '/api/containers/:id/pause',
    UNPAUSE: '/api/containers/:id/unpause',
    REMOVE: '/api/containers/:id/remove',
    LOGS: '/api/containers/:id/logs',
    STATS: '/api/containers/:id/stats',
    EXEC: '/api/containers/:id/exec'
  },
  CONTAINER_IMAGES: {
    LIST: '/api/containers/images',
    PULL: '/api/containers/images/pull',
    REMOVE: '/api/containers/images/:id/remove',
    SEARCH: '/api/containers/images/search'
  },
  CONTAINER_ENGINES: {
    LIST: '/api/containers/engines',
    INSTALL: '/api/containers/engines/install',
    STATUS: '/api/containers/engines/:engine/status',
    INFO: '/api/containers/engines/:engine/info'
  }
} as const;

export type ApiEndpoint = typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS][keyof typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS]];
