# React Query 5.56.2 - Exemplos Práticos

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.56.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tanstack.com/query/latest
- **GitHub:** https://github.com/TanStack/query
- **Documentação:** https://tanstack.com/query/latest/docs/framework/react/overview
- **NPM/Package:** https://www.npmjs.com/package/@tanstack/react-query
- **Fórum/Community:** https://github.com/TanStack/query/discussions
- **Stack Overflow Tag:** `react-query`

---

## 🚀 **EXEMPLOS PARA AUTO-INSTALADOR V3 LITE**

### **1. Sistema Completo de Gerenciamento de Containers**

```typescript
// src/hooks/useContainerManagement.ts
import { useQuery, useMutation, useQueryClient, useQueries } from '@tanstack/react-query';
import type { Container, ContainerStats, CreateContainerData } from '@types/container';

// Query Keys Factory
export const containerKeys = {
  all: ['containers'] as const,
  lists: () => [...containerKeys.all, 'list'] as const,
  list: (filters: string) => [...containerKeys.lists(), { filters }] as const,
  details: () => [...containerKeys.all, 'detail'] as const,
  detail: (id: string) => [...containerKeys.details(), id] as const,
  stats: (id: string) => [...containerKeys.detail(id), 'stats'] as const,
  logs: (id: string) => [...containerKeys.detail(id), 'logs'] as const
};

// API Functions
const containerAPI = {
  getAll: async (): Promise<Container[]> => {
    return await window.electronAPI.invoke('containers:list');
  },
  
  getById: async (id: string): Promise<Container> => {
    return await window.electronAPI.invoke('containers:get', id);
  },
  
  getStats: async (id: string): Promise<ContainerStats> => {
    return await window.electronAPI.invoke('containers:stats', id);
  },
  
  create: async (data: CreateContainerData): Promise<Container> => {
    return await window.electronAPI.invoke('containers:create', data);
  },
  
  start: async (id: string): Promise<void> => {
    await window.electronAPI.invoke('containers:start', id);
  },
  
  stop: async (id: string): Promise<void> => {
    await window.electronAPI.invoke('containers:stop', id);
  },
  
  remove: async (id: string): Promise<void> => {
    await window.electronAPI.invoke('containers:remove', id);
  }
};

// Custom Hooks
export function useContainers(filters?: string) {
  return useQuery({
    queryKey: containerKeys.list(filters || ''),
    queryFn: containerAPI.getAll,
    staleTime: 30 * 1000, // 30 segundos
    refetchInterval: 10 * 1000, // Refetch a cada 10 segundos
    refetchIntervalInBackground: true,
    select: (data) => {
      // Filtrar e ordenar containers
      let filtered = data;
      
      if (filters) {
        filtered = data.filter(container => 
          container.name.toLowerCase().includes(filters.toLowerCase()) ||
          container.image.toLowerCase().includes(filters.toLowerCase())
        );
      }
      
      return filtered.sort((a, b) => a.name.localeCompare(b.name));
    }
  });
}

export function useContainer(id: string) {
  return useQuery({
    queryKey: containerKeys.detail(id),
    queryFn: () => containerAPI.getById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutos
    retry: (failureCount, error) => {
      if (error.message.includes('not found')) return false;
      return failureCount < 3;
    }
  });
}

export function useContainerStats(id: string, enabled = true) {
  return useQuery({
    queryKey: containerKeys.stats(id),
    queryFn: () => containerAPI.getStats(id),
    enabled: enabled && !!id,
    refetchInterval: 2 * 1000, // 2 segundos
    refetchIntervalInBackground: true,
    staleTime: 1000,
    gcTime: 5 * 1000,
    retry: false
  });
}

// Mutations
export function useCreateContainer() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: containerAPI.create,
    onSuccess: (newContainer) => {
      // Adicionar à lista
      queryClient.setQueryData<Container[]>(containerKeys.lists(), (old) => {
        return old ? [...old, newContainer] : [newContainer];
      });
      
      // Definir dados individuais
      queryClient.setQueryData(containerKeys.detail(newContainer.id), newContainer);
      
      // Invalidar listas para garantir consistência
      queryClient.invalidateQueries({ queryKey: containerKeys.lists() });
    }
  });
}

export function useContainerActions() {
  const queryClient = useQueryClient();
  
  const startMutation = useMutation({
    mutationFn: containerAPI.start,
    onMutate: async (containerId) => {
      await queryClient.cancelQueries({ queryKey: containerKeys.detail(containerId) });
      
      const previousContainer = queryClient.getQueryData<Container>(
        containerKeys.detail(containerId)
      );
      
      // Update otimista
      queryClient.setQueryData<Container>(
        containerKeys.detail(containerId),
        (old) => old ? { ...old, status: 'starting' } : old
      );
      
      return { previousContainer };
    },
    onError: (err, containerId, context) => {
      if (context?.previousContainer) {
        queryClient.setQueryData(
          containerKeys.detail(containerId),
          context.previousContainer
        );
      }
    },
    onSuccess: (_, containerId) => {
      // Atualizar status para running
      queryClient.setQueryData<Container>(
        containerKeys.detail(containerId),
        (old) => old ? { ...old, status: 'running' } : old
      );
      
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: containerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: containerKeys.stats(containerId) });
    }
  });
  
  const stopMutation = useMutation({
    mutationFn: containerAPI.stop,
    onMutate: async (containerId) => {
      await queryClient.cancelQueries({ queryKey: containerKeys.detail(containerId) });
      
      const previousContainer = queryClient.getQueryData<Container>(
        containerKeys.detail(containerId)
      );
      
      queryClient.setQueryData<Container>(
        containerKeys.detail(containerId),
        (old) => old ? { ...old, status: 'stopping' } : old
      );
      
      return { previousContainer };
    },
    onError: (err, containerId, context) => {
      if (context?.previousContainer) {
        queryClient.setQueryData(
          containerKeys.detail(containerId),
          context.previousContainer
        );
      }
    },
    onSuccess: (_, containerId) => {
      queryClient.setQueryData<Container>(
        containerKeys.detail(containerId),
        (old) => old ? { ...old, status: 'stopped' } : old
      );
      
      queryClient.invalidateQueries({ queryKey: containerKeys.lists() });
    }
  });
  
  const removeMutation = useMutation({
    mutationFn: containerAPI.remove,
    onSuccess: (_, containerId) => {
      // Remover da lista
      queryClient.setQueryData<Container[]>(containerKeys.lists(), (old) => {
        return old?.filter(container => container.id !== containerId);
      });
      
      // Remover dados individuais
      queryClient.removeQueries({ queryKey: containerKeys.detail(containerId) });
      queryClient.removeQueries({ queryKey: containerKeys.stats(containerId) });
    }
  });
  
  return {
    start: startMutation,
    stop: stopMutation,
    remove: removeMutation
  };
}
```

### **2. Dashboard com Múltiplas Queries**

```typescript
// src/components/dashboard/ContainerDashboard.tsx
import { useContainers, useContainerStats } from '@hooks/useContainerManagement';
import { useQueries } from '@tanstack/react-query';
import { useMemo } from 'react';

export function ContainerDashboard() {
  const { data: containers, isLoading, error } = useContainers();
  
  // Buscar stats para containers em execução
  const runningContainers = useMemo(() => 
    containers?.filter(c => c.status === 'running') || [], 
    [containers]
  );
  
  const statsQueries = useQueries({
    queries: runningContainers.map(container => ({
      queryKey: ['containers', container.id, 'stats'],
      queryFn: () => containerAPI.getStats(container.id),
      refetchInterval: 3 * 1000,
      staleTime: 1000,
      enabled: container.status === 'running'
    }))
  });
  
  // Calcular estatísticas agregadas
  const aggregatedStats = useMemo(() => {
    const validStats = statsQueries
      .filter(query => query.data)
      .map(query => query.data!);
    
    if (validStats.length === 0) {
      return { totalCpu: 0, totalMemory: 0, avgCpu: 0, avgMemory: 0 };
    }
    
    const totalCpu = validStats.reduce((sum, stats) => sum + stats.cpuUsage, 0);
    const totalMemory = validStats.reduce((sum, stats) => sum + stats.memoryUsage, 0);
    
    return {
      totalCpu,
      totalMemory,
      avgCpu: totalCpu / validStats.length,
      avgMemory: totalMemory / validStats.length,
      containerCount: validStats.length
    };
  }, [statsQueries]);
  
  if (isLoading) {
    return <DashboardSkeleton />;
  }
  
  if (error) {
    return (
      <div className="error-state">
        <h2>Erro ao carregar dashboard</h2>
        <p>{error.message}</p>
        <button onClick={() => window.location.reload()}>
          Recarregar
        </button>
      </div>
    );
  }
  
  return (
    <div className="dashboard">
      {/* Estatísticas Gerais */}
      <div className="stats-grid">
        <StatCard
          title="Containers Ativos"
          value={runningContainers.length}
          total={containers?.length || 0}
          icon="📦"
        />
        
        <StatCard
          title="CPU Média"
          value={`${aggregatedStats.avgCpu.toFixed(1)}%`}
          icon="⚡"
        />
        
        <StatCard
          title="Memória Total"
          value={`${(aggregatedStats.totalMemory / 1024 / 1024 / 1024).toFixed(1)}GB`}
          icon="💾"
        />
        
        <StatCard
          title="Status Geral"
          value={runningContainers.length > 0 ? "Operacional" : "Parado"}
          icon="🟢"
        />
      </div>
      
      {/* Lista de Containers */}
      <div className="containers-section">
        <h2>Containers</h2>
        <div className="container-grid">
          {containers?.map((container, index) => (
            <ContainerCard
              key={container.id}
              container={container}
              stats={statsQueries[runningContainers.findIndex(c => c.id === container.id)]?.data}
              isLoadingStats={statsQueries[runningContainers.findIndex(c => c.id === container.id)]?.isLoading}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

function StatCard({ title, value, total, icon }: {
  title: string;
  value: string | number;
  total?: number;
  icon: string;
}) {
  return (
    <div className="stat-card">
      <div className="stat-icon">{icon}</div>
      <div className="stat-content">
        <h3>{title}</h3>
        <div className="stat-value">
          {value}
          {total && <span className="stat-total">/{total}</span>}
        </div>
      </div>
    </div>
  );
}
```

### **3. Formulário com Optimistic Updates**

```typescript
// src/components/forms/CreateContainerForm.tsx
import { useCreateContainer } from '@hooks/useContainerManagement';
import { useActionState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FormState {
  success: boolean;
  error?: string;
  data?: any;
}

export function CreateContainerForm({ onSuccess }: { onSuccess?: () => void }) {
  const createMutation = useCreateContainer();
  
  const [state, formAction, isPending] = useActionState<FormState>(
    async (previousState: FormState, formData: FormData): Promise<FormState> => {
      try {
        const containerData = {
          name: formData.get('name') as string,
          image: formData.get('image') as string,
          ports: (formData.get('ports') as string)?.split(',').filter(Boolean) || [],
          environment: Object.fromEntries(
            (formData.get('environment') as string)
              ?.split('\n')
              .filter(Boolean)
              .map(line => line.split('='))
              || []
          ),
          autoStart: formData.get('autoStart') === 'on',
          restartPolicy: formData.get('restartPolicy') as string || 'no'
        };
        
        // Validação
        if (!containerData.name || !containerData.image) {
          return { success: false, error: 'Nome e imagem são obrigatórios' };
        }
        
        // Criar container
        const result = await createMutation.mutateAsync(containerData);
        
        onSuccess?.();
        return { success: true, data: result };
        
      } catch (error: any) {
        return { 
          success: false, 
          error: error.message || 'Erro ao criar container' 
        };
      }
    },
    { success: false }
  );
  
  return (
    <div className="create-container-form">
      <form action={formAction} className="space-y-6">
        {/* Nome do Container */}
        <div className="form-group">
          <label htmlFor="name" className="form-label">
            Nome do Container *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            required
            className="form-input"
            placeholder="ex: web-server"
            disabled={isPending || createMutation.isPending}
          />
        </div>
        
        {/* Imagem Docker */}
        <div className="form-group">
          <label htmlFor="image" className="form-label">
            Imagem Docker *
          </label>
          <select
            id="image"
            name="image"
            required
            className="form-select"
            disabled={isPending || createMutation.isPending}
          >
            <option value="">Selecione uma imagem</option>
            <option value="nginx:latest">nginx:latest</option>
            <option value="redis:alpine">redis:alpine</option>
            <option value="postgres:13">postgres:13</option>
            <option value="node:18-alpine">node:18-alpine</option>
            <option value="python:3.11-slim">python:3.11-slim</option>
          </select>
        </div>
        
        {/* Portas */}
        <div className="form-group">
          <label htmlFor="ports" className="form-label">
            Mapeamento de Portas
          </label>
          <input
            type="text"
            id="ports"
            name="ports"
            className="form-input"
            placeholder="8080:80, 3000:3000"
            disabled={isPending || createMutation.isPending}
          />
          <p className="form-help">
            Formato: porta_host:porta_container, separadas por vírgula
          </p>
        </div>
        
        {/* Variáveis de Ambiente */}
        <div className="form-group">
          <label htmlFor="environment" className="form-label">
            Variáveis de Ambiente
          </label>
          <textarea
            id="environment"
            name="environment"
            rows={4}
            className="form-textarea"
            placeholder="NODE_ENV=production&#10;API_KEY=your-key"
            disabled={isPending || createMutation.isPending}
          />
          <p className="form-help">
            Uma variável por linha no formato CHAVE=valor
          </p>
        </div>
        
        {/* Opções Avançadas */}
        <div className="form-group">
          <h3 className="text-lg font-medium mb-4">Opções Avançadas</h3>
          
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="autoStart"
                className="form-checkbox"
                disabled={isPending || createMutation.isPending}
              />
              <span className="ml-2">Iniciar automaticamente</span>
            </label>
            
            <div>
              <label className="form-label">Política de Reinício</label>
              <select
                name="restartPolicy"
                className="form-select"
                disabled={isPending || createMutation.isPending}
              >
                <option value="no">Nunca</option>
                <option value="always">Sempre</option>
                <option value="unless-stopped">A menos que parado</option>
                <option value="on-failure">Em caso de falha</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* Status Messages */}
        <AnimatePresence>
          {state.error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="error-message"
            >
              {state.error}
            </motion.div>
          )}
          
          {state.success && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="success-message"
            >
              Container criado com sucesso!
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Actions */}
        <div className="form-actions">
          <button
            type="button"
            className="btn-secondary"
            disabled={isPending || createMutation.isPending}
            onClick={() => onSuccess?.()}
          >
            Cancelar
          </button>
          
          <button
            type="submit"
            className="btn-primary"
            disabled={isPending || createMutation.isPending}
          >
            {isPending || createMutation.isPending ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Criando...
              </>
            ) : (
              'Criar Container'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
```

### **4. Logs em Tempo Real com Infinite Query**

```typescript
// src/components/containers/ContainerLogs.tsx
import { useInfiniteQuery } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
}

interface LogsResponse {
  logs: LogEntry[];
  nextCursor?: string;
  hasMore: boolean;
}

async function fetchContainerLogs(
  containerId: string, 
  cursor?: string
): Promise<LogsResponse> {
  return await window.electronAPI.invoke('containers:logs', {
    containerId,
    cursor,
    limit: 50
  });
}

export function ContainerLogs({ containerId }: { containerId: string }) {
  const [autoScroll, setAutoScroll] = useState(true);
  const [filter, setFilter] = useState<string>('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const logsEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch
  } = useInfiniteQuery({
    queryKey: ['containers', containerId, 'logs', { filter, levelFilter }],
    queryFn: ({ pageParam }) => fetchContainerLogs(containerId, pageParam),
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    refetchInterval: 2 * 1000, // Refetch a cada 2 segundos para novos logs
    staleTime: 1000,
    enabled: !!containerId
  });
  
  // Auto scroll para o final
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [data, autoScroll]);
  
  // Detectar scroll manual para desabilitar auto scroll
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setAutoScroll(isAtBottom);
    };
    
    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Filtrar logs
  const filteredLogs = data?.pages.flatMap(page => 
    page.logs.filter(log => {
      const matchesText = !filter || 
        log.message.toLowerCase().includes(filter.toLowerCase());
      const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
      return matchesText && matchesLevel;
    })
  ) || [];
  
  if (isLoading) {
    return (
      <div className="logs-container">
        <div className="logs-loading">
          <div className="animate-pulse space-y-2">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="logs-error">
        <p>Erro ao carregar logs: {error.message}</p>
        <button onClick={() => refetch()} className="btn-primary">
          Tentar Novamente
        </button>
      </div>
    );
  }
  
  return (
    <div className="logs-container">
      {/* Controles */}
      <div className="logs-controls">
        <div className="flex items-center space-x-4">
          <input
            type="text"
            placeholder="Filtrar logs..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="form-input flex-1"
          />
          
          <select
            value={levelFilter}
            onChange={(e) => setLevelFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">Todos os níveis</option>
            <option value="error">Erro</option>
            <option value="warn">Aviso</option>
            <option value="info">Info</option>
            <option value="debug">Debug</option>
          </select>
          
          <button
            onClick={() => setAutoScroll(!autoScroll)}
            className={`btn-secondary ${autoScroll ? 'active' : ''}`}
          >
            Auto Scroll
          </button>
          
          <button
            onClick={() => refetch()}
            className="btn-secondary"
          >
            Atualizar
          </button>
        </div>
      </div>
      
      {/* Logs */}
      <div 
        ref={containerRef}
        className="logs-content"
        style={{ height: '400px', overflowY: 'auto' }}
      >
        {hasNextPage && (
          <button
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            className="load-more-button"
          >
            {isFetchingNextPage ? 'Carregando...' : 'Carregar Mais'}
          </button>
        )}
        
        <AnimatePresence>
          {filteredLogs.map((log, index) => (
            <motion.div
              key={`${log.id}-${index}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              className={`log-entry log-${log.level}`}
            >
              <span className="log-timestamp">
                {new Date(log.timestamp).toLocaleTimeString()}
              </span>
              <span className={`log-level log-level-${log.level}`}>
                {log.level.toUpperCase()}
              </span>
              <span className="log-message">{log.message}</span>
            </motion.div>
          ))}
        </AnimatePresence>
        
        <div ref={logsEndRef} />
      </div>
      
      {/* Status */}
      <div className="logs-status">
        <span>
          {filteredLogs.length} logs exibidos
          {filter && ` (filtrados por "${filter}")`}
        </span>
        
        {!autoScroll && (
          <button
            onClick={() => {
              setAutoScroll(true);
              logsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="btn-link"
          >
            Ir para o final
          </button>
        )}
      </div>
    </div>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - React Query 5.56.2 Examples
