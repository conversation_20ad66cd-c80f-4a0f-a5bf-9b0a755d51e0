#!/bin/bash
# Auto-Instalador V3 Lite - Setup Completo
# Otimizado para Intel i5 12ª Gen, 32GB RAM, SSD 512GB
# Bash Script para Linux/macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Parse command line arguments
SKIP_NODE_CHECK=false
SKIP_DOTNET_CHECK=false
FORCE=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-node-check)
      SKIP_NODE_CHECK=true
      shift
      ;;
    --skip-dotnet-check)
      SKIP_DOTNET_CHECK=true
      shift
      ;;
    --force)
      FORCE=true
      shift
      ;;
    *)
      echo "Unknown option $1"
      exit 1
      ;;
  esac
done

echo -e "${GREEN}🚀 Configurando Auto-Instalador V3 Lite Desktop...${NC}"
echo -e "${CYAN}Hardware Target: Intel i5 12ª Gen, 32GB RAM, SSD 512GB${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to execute command safely
safe_execute() {
    local cmd="$1"
    local description="$2"
    
    echo -e "${YELLOW}📦 $description...${NC}"
    
    if eval "$cmd"; then
        echo -e "${GREEN}✅ $description concluído${NC}"
    else
        echo -e "${RED}❌ Erro em: $description${NC}"
        exit 1
    fi
}

# 1. Check Node.js
if [ "$SKIP_NODE_CHECK" = false ]; then
    echo -e "${CYAN}🔍 Verificando Node.js...${NC}"
    
    if command_exists node; then
        NODE_VERSION=$(node --version)
        echo -e "${GREEN}Node.js encontrado: $NODE_VERSION${NC}"
        
        if [[ ! "$NODE_VERSION" =~ v22\.7\.0 ]]; then
            echo -e "${YELLOW}⚠️  Versão recomendada: v22.7.0${NC}"
            echo -e "${YELLOW}Instale via nvm: nvm install 22.7.0 && nvm use 22.7.0${NC}"
        fi
    else
        echo -e "${RED}❌ Node.js não encontrado!${NC}"
        echo -e "${YELLOW}Instale via nvm: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash${NC}"
        exit 1
    fi
fi

# 2. Check .NET SDK
if [ "$SKIP_DOTNET_CHECK" = false ]; then
    echo -e "${CYAN}🔍 Verificando .NET SDK...${NC}"
    
    if command_exists dotnet; then
        DOTNET_VERSION=$(dotnet --version)
        echo -e "${GREEN}.NET SDK encontrado: $DOTNET_VERSION${NC}"
        
        if [[ ! "$DOTNET_VERSION" =~ 9\.0\. ]]; then
            echo -e "${YELLOW}⚠️  Versão recomendada: 9.0.x${NC}"
            echo -e "${YELLOW}Instale via: https://dotnet.microsoft.com/download${NC}"
        fi
    else
        echo -e "${RED}❌ .NET SDK não encontrado!${NC}"
        echo -e "${YELLOW}Instale via: https://dotnet.microsoft.com/download${NC}"
        exit 1
    fi
fi

# 3. Create project structure
echo -e "${CYAN}📁 Criando estrutura do projeto...${NC}"

PROJECT_DIRS=(
    # Frontend structure
    "src/frontend/electron/main"
    "src/frontend/electron/preload"
    "src/frontend/electron/renderer"
    "src/frontend/renderer/components/ui"
    "src/frontend/renderer/components/layout"
    "src/frontend/renderer/components/features"
    "src/frontend/renderer/components/forms"
    "src/frontend/renderer/pages"
    "src/frontend/renderer/hooks"
    "src/frontend/renderer/services"
    "src/frontend/renderer/store"
    "src/frontend/renderer/types"
    "src/frontend/renderer/utils"
    "src/frontend/renderer/styles"
    "src/frontend/renderer/assets"

    # Backend structure
    "src/backend/src/AutoInstalador.API/Controllers"
    "src/backend/src/AutoInstalador.API/Middleware"
    "src/backend/src/AutoInstalador.API/Configuration"
    "src/backend/src/AutoInstalador.Core/Entities"
    "src/backend/src/AutoInstalador.Core/Interfaces/Repositories"
    "src/backend/src/AutoInstalador.Core/Interfaces/Services"
    "src/backend/src/AutoInstalador.Core/DTOs/Requests"
    "src/backend/src/AutoInstalador.Core/DTOs/Responses"
    "src/backend/src/AutoInstalador.Core/Enums"
    "src/backend/src/AutoInstalador.Core/Exceptions"
    "src/backend/src/AutoInstalador.Application/Services"
    "src/backend/src/AutoInstalador.Application/Validators"
    "src/backend/src/AutoInstalador.Application/Mappers"
    "src/backend/src/AutoInstalador.Infrastructure/Data/Repositories"
    "src/backend/src/AutoInstalador.Infrastructure/External"
    "src/backend/src/AutoInstalador.Infrastructure/Logging"
    "src/backend/src/AutoInstalador.Shared/Extensions"
    "src/backend/src/AutoInstalador.Shared/Helpers"
    "src/backend/tests/AutoInstalador.UnitTests/Services"
    "src/backend/tests/AutoInstalador.UnitTests/Controllers"
    "src/backend/tests/AutoInstalador.IntegrationTests/API"
    "src/backend/tests/AutoInstalador.E2ETests/Scenarios"

    # Shared resources
    "shared/types"
    "shared/contracts"
    "shared/schemas"

    # Frontend tests
    "tests/unit/components"
    "tests/unit/hooks"
    "tests/unit/services"
    "tests/integration/electron"
    "tests/integration/api"
    "tests/e2e/scenarios"
    "tests/contracts"

    # Build and assets
    "build/frontend"
    "build/backend"
    "build/release"
    "assets/images"
    "assets/icons"
)

for dir in "${PROJECT_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo -e "${GREEN}✅ Criado: $dir${NC}"
    fi
done

# 4. Initialize package.json if not exists
if [ ! -f "package.json" ] || [ "$FORCE" = true ]; then
    echo -e "${CYAN}📦 Inicializando package.json...${NC}"
    
    cat > package.json << 'EOF'
{
  "name": "auto-instalador-v3-lite-desktop",
  "version": "1.0.0",
  "description": "Auto-Instalador V3 Lite - Desktop Application (Versões Mais Recentes)",
  "main": "dist/electron/main/main.js",
  "homepage": "./",
  "engines": {
    "node": "22.7.0",
    "npm": "10.8.2"
  },
  "config": {
    "target_platform": "i5-12th-gen",
    "max_memory": "32gb",
    "storage": "512gb-ssd"
  },
  "scripts": {
    "dev": "concurrently \"npm run dev:react\" \"npm run dev:electron\"",
    "dev:react": "vite --port 3000 --host",
    "dev:electron": "wait-on http://localhost:3000 && electron .",
    "build": "npm run build:clean && npm run build:react && npm run build:electron",
    "build:clean": "rimraf dist release",
    "build:react": "vite build",
    "build:electron": "tsc -p tsconfig.electron.json",
    "dist": "npm run build && electron-builder",
    "dist:linux": "npm run build && electron-builder --linux",
    "dist:mac": "npm run build && electron-builder --mac",
    "test": "vitest run",
    "test:coverage": "vitest run --coverage",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"",
    "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""
  }
}
EOF
    
    echo -e "${GREEN}✅ package.json criado${NC}"
fi

# 5. Install production dependencies
echo -e "${CYAN}📦 Instalando dependências de produção...${NC}"

PROD_DEPS=(
    "react@19.2.0"
    "react-dom@19.2.0"
    "react-router-dom@6.26.0"
    "@tanstack/react-query@5.56.2"
    "framer-motion@11.5.4"
    "tailwindcss@4.0.0-beta.1"
    "lucide-react@0.445.0"
    "better-sqlite3@11.3.0"
    "ioredis@5.4.1"
    "zustand@5.0.0"
    "react-hook-form@7.52.2"
    "zod@3.23.8"
    "electron-updater@6.2.1"
    "electron-store@10.0.0"
    "electron-log@5.1.7"
    "axios@1.7.4"
    "date-fns@3.6.0"
    "uuid@10.0.0"
    "clsx@2.1.1"
    "tailwind-merge@2.5.2"
)

safe_execute "npm install ${PROD_DEPS[*]}" "Instalação de dependências de produção"

# 6. Install development dependencies
echo -e "${CYAN}🔧 Instalando dependências de desenvolvimento...${NC}"

DEV_DEPS=(
    "electron@37.1.2"
    "typescript@5.6.2"
    "@types/react@19.2.0"
    "@types/react-dom@19.2.0"
    "@types/node@22.5.0"
    "@types/uuid@10.0.0"
    "@types/better-sqlite3@7.6.11"
    "vite@5.4.2"
    "@vitejs/plugin-react@4.3.1"
    "electron-builder@25.0.5"
    "@playwright/test@1.47.0"
    "eslint@9.9.1"
    "@typescript-eslint/parser@8.2.0"
    "@typescript-eslint/eslint-plugin@8.2.0"
    "prettier@3.3.3"
    "vitest@2.0.5"
    "@vitest/ui@2.0.5"
    "@vitest/coverage-v8@2.0.5"
    "@testing-library/react@16.0.0"
    "@testing-library/jest-dom@6.4.8"
    "concurrently@8.2.2"
    "wait-on@7.2.0"
    "rimraf@5.0.10"
    "husky@9.1.4"
    "lint-staged@15.2.8"
)

safe_execute "npm install --save-dev ${DEV_DEPS[*]}" "Instalação de dependências de desenvolvimento"

# 7. Configure optimizations for i5 12th Gen
echo -e "${CYAN}🔧 Aplicando otimizações para i5 12ª Gen...${NC}"

# Create .env file
cat > .env << 'EOF'
# Otimizações para Intel i5 12ª Gen + 32GB RAM
NODE_OPTIONS=--max-old-space-size=4096 --max-semi-space-size=512
UV_THREADPOOL_SIZE=12
ELECTRON_DISABLE_SECURITY_WARNINGS=true

# .NET Optimizations
DOTNET_GCHeapCount=6
DOTNET_GCConcurrent=1
DOTNET_GCServer=0
DOTNET_EnableDiagnostics=0

# Development
NODE_ENV=development
VITE_PORT=3000
ELECTRON_IS_DEV=true
EOF

echo -e "${GREEN}✅ Configurações de ambiente criadas${NC}"

# 8. Create TypeScript configurations
echo -e "${CYAN}📝 Criando configurações TypeScript...${NC}"

# tsconfig.json for React
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/renderer/components/*"],
      "@pages/*": ["src/renderer/pages/*"],
      "@services/*": ["src/renderer/services/*"],
      "@hooks/*": ["src/renderer/hooks/*"],
      "@types/*": ["src/renderer/types/*"],
      "@utils/*": ["src/renderer/utils/*"]
    }
  },
  "include": ["src/renderer/**/*", "src/renderer/**/*.tsx", "src/renderer/**/*.ts"],
  "exclude": ["node_modules", "dist", "src/electron"]
}
EOF

# tsconfig.electron.json for Electron
cat > tsconfig.electron.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020"],
    "module": "CommonJS",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "dist/electron",
    "rootDir": "src/electron",
    "declaration": true,
    "sourceMap": false
  },
  "include": ["src/electron/**/*"],
  "exclude": ["node_modules", "dist"]
}
EOF

echo -e "${GREEN}✅ Configurações TypeScript criadas${NC}"

# 9. Install Playwright browsers
echo -e "${CYAN}🎭 Instalando browsers do Playwright...${NC}"
safe_execute "npx playwright install" "Instalação de browsers Playwright"

# 10. Configure Git hooks
if [ -d ".git" ]; then
    echo -e "${CYAN}🔧 Configurando Git hooks...${NC}"
    safe_execute "npx husky init" "Configuração Husky"
    
    # Create pre-commit hook
    cat > .husky/pre-commit << 'EOF'
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
EOF
    
    chmod +x .husky/pre-commit
    echo -e "${GREEN}✅ Git hooks configurados${NC}"
fi

# 11. Verify installation
echo -e "${CYAN}✅ Verificando instalação...${NC}"

echo ""
echo -e "${YELLOW}📋 Verificação final:${NC}"
echo -e "${GREEN}  - Node.js: $(node --version)${NC}"
echo -e "${GREEN}  - npm: $(npm --version)${NC}"
echo -e "${GREEN}  - .NET: $(dotnet --version)${NC}"

# Check if dependencies were installed
if [ -d "node_modules" ]; then
    PACKAGE_COUNT=$(find node_modules -maxdepth 1 -type d | wc -l)
    echo -e "${GREEN}  - Packages instalados: $PACKAGE_COUNT${NC}"
else
    echo -e "${RED}  - ❌ node_modules não encontrado${NC}"
fi

# 12. Show next steps
echo ""
echo -e "${GREEN}🎉 Setup concluído com sucesso!${NC}"
echo ""
echo -e "${YELLOW}📋 Próximos passos:${NC}"
echo -e "${CYAN}1. Configurar arquivos de código fonte (main.ts, preload.ts, App.tsx)${NC}"
echo -e "${CYAN}2. Configurar Vite (vite.config.ts)${NC}"
echo -e "${CYAN}3. Configurar Electron Builder (electron-builder.json)${NC}"
echo -e "${CYAN}4. Executar: npm run dev${NC}"
echo ""
echo -e "${YELLOW}💾 Consumo estimado no seu hardware:${NC}"
echo -e "${GREEN}   - RAM: ~12GB (37.5% dos 32GB disponíveis)${NC}"
echo -e "${GREEN}   - CPU: ~4-6 cores (33-50% dos 12 cores)${NC}"
echo -e "${GREEN}   - Storage: ~15GB (3% dos 512GB)${NC}"
echo ""
echo -e "${GREEN}🚀 Hardware otimizado para:${NC}"
echo -e "${CYAN}   - Intel i5 12ª Gen (6P + 6E cores)${NC}"
echo -e "${CYAN}   - 32GB RAM (configurações otimizadas)${NC}"
echo -e "${CYAN}   - SSD 512GB (cache expandido)${NC}"

echo ""
echo -e "${GREEN}✨ Auto-Instalador V3 Lite Desktop pronto para desenvolvimento!${NC}"
