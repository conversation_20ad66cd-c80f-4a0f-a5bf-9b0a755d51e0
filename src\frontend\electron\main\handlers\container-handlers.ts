/**
 * Container Handlers - Electron Main Process
 * Auto-Instalador V3 Lite
 * 
 * @description Handlers IPC para comunicação com a API de containers
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import { ipcMain } from 'electron';
import { ApiClient } from '../services/api-client';
import type {
  ContainerListRequest,
  ContainerRunRequest,
  ContainerLogsRequest,
  ContainerEngineInstallRequest,
  ContainerEngine
} from '../../../../shared/types/api.types';

// ============================================================================
// CONSTANTS
// ============================================================================

const CONTAINER_CHANNELS = {
  // Container Management
  LIST_CONTAINERS: 'container:list',
  GET_CONTAINER: 'container:get',
  RUN_CONTAINER: 'container:run',
  START_CONTAINER: 'container:start',
  STOP_CONTAINER: 'container:stop',
  RESTART_CONTAINER: 'container:restart',
  PAUSE_CONTAINER: 'container:pause',
  UNPAUSE_CONTAINER: 'container:unpause',
  REMOVE_CONTAINER: 'container:remove',
  
  // Container Logs & Stats
  GET_CONTAINER_LOGS: 'container:logs',
  GET_CONTAINER_STATS: 'container:stats',
  EXEC_CONTAINER: 'container:exec',
  
  // Container Images
  LIST_IMAGES: 'container:images:list',
  PULL_IMAGE: 'container:images:pull',
  REMOVE_IMAGE: 'container:images:remove',
  SEARCH_IMAGES: 'container:images:search',
  
  // Container Engines
  LIST_ENGINES: 'container:engines:list',
  GET_ENGINE_STATUS: 'container:engines:status',
  GET_ENGINE_INFO: 'container:engines:info',
  INSTALL_ENGINE: 'container:engines:install',
  DETECT_ENGINES: 'container:engines:detect',
  
  // Utility
  IS_ENGINE_AVAILABLE: 'container:engine:available',
  GET_RECOMMENDED_ENGINE: 'container:engine:recommended'
} as const;

// ============================================================================
// CONTAINER HANDLERS CLASS
// ============================================================================

export class ContainerHandlers {
  private apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // Container Management
    ipcMain.handle(CONTAINER_CHANNELS.LIST_CONTAINERS, async (_, request: ContainerListRequest) => {
      try {
        const params = new URLSearchParams();
        if (request.all) params.append('all', 'true');
        if (request.engine) params.append('engine', request.engine);
        if (request.filters?.status) {
          request.filters.status.forEach(status => params.append('status', status));
        }
        if (request.filters?.name) params.append('name', request.filters.name);
        if (request.filters?.image) params.append('image', request.filters.image);

        return await this.apiClient.get(`/api/containers?${params.toString()}`);
      } catch (error) {
        console.error('Error listing containers:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.GET_CONTAINER, async (_, { id, engine }: { id: string; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (engine) params.append('engine', engine);

        return await this.apiClient.get(`/api/containers/${id}?${params.toString()}`);
      } catch (error) {
        console.error('Error getting container:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.RUN_CONTAINER, async (_, request: ContainerRunRequest) => {
      try {
        return await this.apiClient.post('/api/containers/run', request);
      } catch (error) {
        console.error('Error running container:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.START_CONTAINER, async (_, { id, engine }: { id: string; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (engine) params.append('engine', engine);

        return await this.apiClient.post(`/api/containers/${id}/start?${params.toString()}`);
      } catch (error) {
        console.error('Error starting container:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.STOP_CONTAINER, async (_, { id, timeout, engine }: { id: string; timeout?: number; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (timeout) params.append('timeout', timeout.toString());
        if (engine) params.append('engine', engine);

        return await this.apiClient.post(`/api/containers/${id}/stop?${params.toString()}`);
      } catch (error) {
        console.error('Error stopping container:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.RESTART_CONTAINER, async (_, { id, timeout, engine }: { id: string; timeout?: number; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (timeout) params.append('timeout', timeout.toString());
        if (engine) params.append('engine', engine);

        return await this.apiClient.post(`/api/containers/${id}/restart?${params.toString()}`);
      } catch (error) {
        console.error('Error restarting container:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.REMOVE_CONTAINER, async (_, { id, force, engine }: { id: string; force?: boolean; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (force) params.append('force', 'true');
        if (engine) params.append('engine', engine);

        return await this.apiClient.delete(`/api/containers/${id}?${params.toString()}`);
      } catch (error) {
        console.error('Error removing container:', error);
        throw error;
      }
    });

    // Container Logs & Stats
    ipcMain.handle(CONTAINER_CHANNELS.GET_CONTAINER_LOGS, async (_, request: ContainerLogsRequest) => {
      try {
        const params = new URLSearchParams();
        if (request.tail) params.append('tail', request.tail.toString());
        if (request.follow) params.append('follow', 'true');
        if (request.timestamps) params.append('timestamps', 'true');
        if (request.since) params.append('since', request.since.toISOString());
        if (request.until) params.append('until', request.until.toISOString());
        if (request.engine) params.append('engine', request.engine);

        return await this.apiClient.get(`/api/containers/${request.containerId}/logs?${params.toString()}`);
      } catch (error) {
        console.error('Error getting container logs:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.GET_CONTAINER_STATS, async (_, { id, engine }: { id: string; engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (engine) params.append('engine', engine);

        return await this.apiClient.get(`/api/containers/${id}/stats?${params.toString()}`);
      } catch (error) {
        console.error('Error getting container stats:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.EXEC_CONTAINER, async (_, { id, command, args, interactive, tty, engine }: {
      id: string;
      command: string;
      args?: string[];
      interactive?: boolean;
      tty?: boolean;
      engine?: ContainerEngine;
    }) => {
      try {
        return await this.apiClient.post(`/api/containers/${id}/exec`, {
          command,
          args,
          interactive,
          tty,
          engine
        });
      } catch (error) {
        console.error('Error executing command in container:', error);
        throw error;
      }
    });

    // Container Images
    ipcMain.handle(CONTAINER_CHANNELS.LIST_IMAGES, async (_, { engine }: { engine?: ContainerEngine }) => {
      try {
        const params = new URLSearchParams();
        if (engine) params.append('engine', engine);

        return await this.apiClient.get(`/api/containers/images?${params.toString()}`);
      } catch (error) {
        console.error('Error listing images:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.PULL_IMAGE, async (_, { imageName, tag, engine }: {
      imageName: string;
      tag?: string;
      engine?: ContainerEngine;
    }) => {
      try {
        const params = new URLSearchParams();
        if (tag) params.append('tag', tag);
        if (engine) params.append('engine', engine);

        return await this.apiClient.post(`/api/containers/images/pull?${params.toString()}`, { imageName });
      } catch (error) {
        console.error('Error pulling image:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.REMOVE_IMAGE, async (_, { imageId, force, engine }: {
      imageId: string;
      force?: boolean;
      engine?: ContainerEngine;
    }) => {
      try {
        const params = new URLSearchParams();
        if (force) params.append('force', 'true');
        if (engine) params.append('engine', engine);

        return await this.apiClient.delete(`/api/containers/images/${imageId}?${params.toString()}`);
      } catch (error) {
        console.error('Error removing image:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.SEARCH_IMAGES, async (_, { searchTerm, limit, engine }: {
      searchTerm: string;
      limit?: number;
      engine?: ContainerEngine;
    }) => {
      try {
        const params = new URLSearchParams();
        params.append('q', searchTerm);
        if (limit) params.append('limit', limit.toString());
        if (engine) params.append('engine', engine);

        return await this.apiClient.get(`/api/containers/images/search?${params.toString()}`);
      } catch (error) {
        console.error('Error searching images:', error);
        throw error;
      }
    });

    // Container Engines
    ipcMain.handle(CONTAINER_CHANNELS.LIST_ENGINES, async () => {
      try {
        return await this.apiClient.get('/api/containers/engines');
      } catch (error) {
        console.error('Error listing engines:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.GET_ENGINE_STATUS, async (_, { engine }: { engine: ContainerEngine }) => {
      try {
        return await this.apiClient.get(`/api/containers/engines/${engine}/status`);
      } catch (error) {
        console.error('Error getting engine status:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.GET_ENGINE_INFO, async (_, { engine }: { engine: ContainerEngine }) => {
      try {
        return await this.apiClient.get(`/api/containers/engines/${engine}/info`);
      } catch (error) {
        console.error('Error getting engine info:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.INSTALL_ENGINE, async (_, request: ContainerEngineInstallRequest) => {
      try {
        return await this.apiClient.post('/api/containers/engines/install', request);
      } catch (error) {
        console.error('Error installing engine:', error);
        throw error;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.DETECT_ENGINES, async () => {
      try {
        return await this.apiClient.get('/api/containers/engines/detect');
      } catch (error) {
        console.error('Error detecting engines:', error);
        throw error;
      }
    });

    // Utility
    ipcMain.handle(CONTAINER_CHANNELS.IS_ENGINE_AVAILABLE, async (_, { engine }: { engine: ContainerEngine }) => {
      try {
        const response = await this.apiClient.get(`/api/containers/engines/${engine}/installed`);
        return response.installed;
      } catch (error) {
        console.error('Error checking engine availability:', error);
        return false;
      }
    });

    ipcMain.handle(CONTAINER_CHANNELS.GET_RECOMMENDED_ENGINE, async () => {
      try {
        const response = await this.apiClient.get('/api/containers/engines/detect');
        return response.recommendedEngine;
      } catch (error) {
        console.error('Error getting recommended engine:', error);
        return null;
      }
    });
  }

  /**
   * Remove todos os handlers registrados
   */
  public dispose(): void {
    Object.values(CONTAINER_CHANNELS).forEach(channel => {
      ipcMain.removeAllListeners(channel);
    });
  }
}
