/**
 * Not Found Page - Página 404
 * Auto-Instalador V3 Lite
 * 
 * @description Página exibida quando uma rota não é encontrada
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';

export const NotFoundPage: React.FC = () => {
  return (
    <div className="h-full bg-gray-900 text-white flex items-center justify-center">
      <div className="text-center">
        <div className="text-8xl mb-4">🔍</div>
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <h2 className="text-xl font-semibold mb-4 text-gray-300">
          Página não encontrada
        </h2>
        <p className="text-gray-400 mb-8 max-w-md">
          A página que você está procurando não existe ou foi movida.
        </p>
        <a 
          href="/"
          className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          🏠 Voltar ao Início
        </a>
      </div>
    </div>
  );
};
