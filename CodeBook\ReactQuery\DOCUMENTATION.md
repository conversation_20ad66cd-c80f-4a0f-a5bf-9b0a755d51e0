# React Query 5.56.2 - Documentação Oficial Resumida

**📅 Data da Pesquisa:** 04 de Agosto de 2025  
**🔗 Versão Documentada:** 5.56.2  
**👤 Preparado por:** Augment Agent  

## 🌐 Links Oficiais
- **Site Oficial:** https://tanstack.com/query/latest
- **GitHub:** https://github.com/TanStack/query
- **Documentação:** https://tanstack.com/query/latest/docs/framework/react/overview
- **NPM/Package:** https://www.npmjs.com/package/@tanstack/react-query
- **Fórum/Community:** https://github.com/TanStack/query/discussions
- **Stack Overflow Tag:** `react-query`

---

## 📚 **DOCUMENTAÇÃO CORE REACT QUERY 5.56**

### **1. Query Basics**

#### **useQuery Hook**
```typescript
import { useQuery } from '@tanstack/react-query';

function BasicQuery() {
  const { 
    data, 
    isLoading, 
    error, 
    isError, 
    isSuccess,
    refetch,
    isFetching 
  } = useQuery({
    queryKey: ['containers'],
    queryFn: fetchContainers,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000,   // 10 minutos (era cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    enabled: true,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    refetchOnMount: true
  });

  if (isLoading) return <div>Carregando...</div>;
  if (isError) return <div>Erro: {error.message}</div>;
  
  return (
    <div>
      {data?.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

#### **useSuspenseQuery Hook (React 19.2)**
```typescript
import { useSuspenseQuery } from '@tanstack/react-query';
import { Suspense } from 'react';

function SuspenseQuery() {
  // Não precisa de loading state - Suspense cuida disso
  const { data, error, refetch } = useSuspenseQuery({
    queryKey: ['containers'],
    queryFn: fetchContainers,
    staleTime: 5 * 60 * 1000
  });

  return (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}

// Uso com Suspense
function App() {
  return (
    <Suspense fallback={<div>Carregando containers...</div>}>
      <SuspenseQuery />
    </Suspense>
  );
}
```

---

### **2. Mutations**

#### **useMutation Hook**
```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query';

function MutationExample() {
  const queryClient = useQueryClient();
  
  const mutation = useMutation({
    mutationFn: createContainer,
    onMutate: async (newContainer) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['containers'] });
      
      // Snapshot previous value
      const previousContainers = queryClient.getQueryData(['containers']);
      
      // Optimistically update
      queryClient.setQueryData(['containers'], (old) => {
        return old ? [...old, { ...newContainer, id: 'temp-id' }] : [newContainer];
      });
      
      return { previousContainers };
    },
    onError: (err, newContainer, context) => {
      // Rollback on error
      queryClient.setQueryData(['containers'], context?.previousContainers);
    },
    onSuccess: (data, variables, context) => {
      // Update with real data
      queryClient.setQueryData(['containers'], (old) => {
        return old?.map(item => 
          item.id === 'temp-id' ? data : item
        );
      });
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    }
  });

  return (
    <button 
      onClick={() => mutation.mutate({ name: 'new-container' })}
      disabled={mutation.isPending}
    >
      {mutation.isPending ? 'Criando...' : 'Criar Container'}
    </button>
  );
}
```

---

### **3. Query Keys**

#### **Query Key Patterns**
```typescript
// Hierarquia de query keys
const queryKeys = {
  // Todos os containers
  containers: ['containers'] as const,
  
  // Lista de containers com filtros
  containersList: (filters?: ContainerFilters) => 
    ['containers', 'list', filters] as const,
  
  // Container específico
  container: (id: string) => 
    ['containers', 'detail', id] as const,
  
  // Stats de container específico
  containerStats: (id: string) => 
    ['containers', 'stats', id] as const,
  
  // Logs de container
  containerLogs: (id: string, options?: LogOptions) => 
    ['containers', 'logs', id, options] as const
};

// Uso das query keys
function useContainer(id: string) {
  return useQuery({
    queryKey: queryKeys.container(id),
    queryFn: () => fetchContainer(id),
    enabled: !!id
  });
}

// Invalidação específica
function useInvalidateContainer() {
  const queryClient = useQueryClient();
  
  return (id: string) => {
    // Invalidar container específico
    queryClient.invalidateQueries({ 
      queryKey: queryKeys.container(id) 
    });
    
    // Invalidar todos os containers
    queryClient.invalidateQueries({ 
      queryKey: queryKeys.containers 
    });
  };
}
```

---

### **4. Query Options**

#### **Configurações Avançadas**
```typescript
// Configurações por tipo de dados
const queryConfigs = {
  // Dados em tempo real
  realtime: {
    staleTime: 0,
    gcTime: 2 * 60 * 1000,
    refetchInterval: 2 * 1000,
    refetchIntervalInBackground: true,
    retry: false
  },
  
  // Dados semi-estáticos
  semiStatic: {
    staleTime: 2 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
    refetchInterval: 30 * 1000,
    refetchIntervalInBackground: false,
    retry: 3
  },
  
  // Dados estáticos
  static: {
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
    refetchInterval: false,
    refetchOnWindowFocus: false,
    retry: 1
  }
};

// Aplicação das configurações
function useContainerStats(id: string) {
  return useQuery({
    queryKey: queryKeys.containerStats(id),
    queryFn: () => fetchContainerStats(id),
    ...queryConfigs.realtime,
    enabled: !!id
  });
}
```

---

### **5. Infinite Queries**

#### **useInfiniteQuery Hook**
```typescript
import { useInfiniteQuery } from '@tanstack/react-query';

interface LogsResponse {
  logs: LogEntry[];
  nextCursor?: string;
  hasMore: boolean;
}

function useContainerLogs(containerId: string) {
  return useInfiniteQuery({
    queryKey: ['containers', containerId, 'logs'],
    queryFn: ({ pageParam }) => fetchContainerLogs(containerId, pageParam),
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.previousCursor;
    },
    maxPages: 10, // Limitar páginas para performance
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000
  });
}

// Componente com infinite scroll
function ContainerLogs({ containerId }: { containerId: string }) {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error
  } = useContainerLogs(containerId);

  if (isLoading) return <div>Carregando logs...</div>;
  if (error) return <div>Erro: {error.message}</div>;

  return (
    <div className="logs-container">
      {data?.pages.map((page, i) => (
        <div key={i}>
          {page.logs.map((log) => (
            <div key={log.id} className="log-entry">
              <span className="timestamp">{log.timestamp}</span>
              <span className="message">{log.message}</span>
            </div>
          ))}
        </div>
      ))}
      
      {hasNextPage && (
        <button
          onClick={() => fetchNextPage()}
          disabled={isFetchingNextPage}
        >
          {isFetchingNextPage ? 'Carregando...' : 'Carregar Mais'}
        </button>
      )}
    </div>
  );
}
```

---

### **6. Query Client**

#### **QueryClient Configuration**
```typescript
import { QueryClient } from '@tanstack/react-query';

// Configuração otimizada para desktop
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      retry: (failureCount, error) => {
        // Retry customizado baseado no erro
        if (error.status === 404) return false;
        if (error.status >= 500) return failureCount < 3;
        return failureCount < 1;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      networkMode: 'online',
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
      refetchOnMount: true
    },
    mutations: {
      retry: 1,
      networkMode: 'online'
    }
  }
});

// Métodos do QueryClient
function QueryClientMethods() {
  const queryClient = useQueryClient();
  
  // Invalidar queries
  const invalidateContainers = () => {
    queryClient.invalidateQueries({ queryKey: ['containers'] });
  };
  
  // Refetch queries
  const refetchContainers = () => {
    queryClient.refetchQueries({ queryKey: ['containers'] });
  };
  
  // Cancelar queries
  const cancelContainers = () => {
    queryClient.cancelQueries({ queryKey: ['containers'] });
  };
  
  // Remover queries do cache
  const removeContainers = () => {
    queryClient.removeQueries({ queryKey: ['containers'] });
  };
  
  // Definir dados no cache
  const setContainerData = (id: string, data: Container) => {
    queryClient.setQueryData(['containers', id], data);
  };
  
  // Obter dados do cache
  const getContainerData = (id: string) => {
    return queryClient.getQueryData<Container>(['containers', id]);
  };
  
  return {
    invalidateContainers,
    refetchContainers,
    cancelContainers,
    removeContainers,
    setContainerData,
    getContainerData
  };
}
```

---

### **7. Error Handling**

#### **Error Boundaries e Retry Logic**
```typescript
import { QueryErrorResetBoundary } from '@tanstack/react-query';
import { ErrorBoundary } from 'react-error-boundary';

// Error boundary para queries
function QueryErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ error, resetErrorBoundary }) => (
            <div className="error-boundary">
              <h2>Algo deu errado:</h2>
              <pre>{error.message}</pre>
              <button onClick={resetErrorBoundary}>
                Tentar novamente
              </button>
            </div>
          )}
        >
          {children}
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  );
}

// Hook para tratamento de erros
function useErrorHandler() {
  return (error: Error, query: any) => {
    console.error('Query error:', error, query);
    
    // Log para sistema de monitoramento
    if (window.electronAPI) {
      window.electronAPI.invoke('log:error', {
        error: error.message,
        stack: error.stack,
        query: query.queryKey
      });
    }
    
    // Notificação para usuário
    if (error.status >= 500) {
      showNotification({
        type: 'error',
        title: 'Erro do Servidor',
        message: 'Tente novamente em alguns instantes'
      });
    }
  };
}
```

---

### **8. Optimistic Updates**

#### **Padrões de Optimistic Updates**
```typescript
function useOptimisticContainerUpdate() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: updateContainer,
    onMutate: async (updatedContainer) => {
      // Cancel queries
      await queryClient.cancelQueries({ 
        queryKey: ['containers', updatedContainer.id] 
      });
      
      // Snapshot
      const previousContainer = queryClient.getQueryData([
        'containers', 
        updatedContainer.id
      ]);
      
      // Optimistic update
      queryClient.setQueryData(
        ['containers', updatedContainer.id], 
        updatedContainer
      );
      
      // Update list
      queryClient.setQueryData(['containers'], (old: Container[]) => {
        return old?.map(container => 
          container.id === updatedContainer.id 
            ? updatedContainer 
            : container
        );
      });
      
      return { previousContainer };
    },
    onError: (err, updatedContainer, context) => {
      // Rollback
      if (context?.previousContainer) {
        queryClient.setQueryData(
          ['containers', updatedContainer.id],
          context.previousContainer
        );
      }
    },
    onSettled: (data, error, updatedContainer) => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: ['containers', updatedContainer.id] 
      });
    }
  });
}
```

---

### **9. Background Updates**

#### **Background Refetching**
```typescript
// Configuração para updates em background
function useBackgroundUpdates() {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    // Refetch periódico em background
    const interval = setInterval(() => {
      queryClient.refetchQueries({
        queryKey: ['containers'],
        type: 'active' // Apenas queries ativas
      });
    }, 30 * 1000); // A cada 30 segundos
    
    return () => clearInterval(interval);
  }, [queryClient]);
  
  // Refetch quando janela ganha foco
  useEffect(() => {
    const handleFocus = () => {
      queryClient.refetchQueries({
        queryKey: ['containers'],
        stale: true // Apenas queries stale
      });
    };
    
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [queryClient]);
}
```

---

### **10. React 19.2 Integration**

#### **Integration with New React Features**
```typescript
import { use, useActionState } from 'react';
import { useSuspenseQuery, useMutation } from '@tanstack/react-query';

// Suspense query com use() hook
function SuspenseWithUse({ containerPromise }: { containerPromise: Promise<Container> }) {
  const container = use(containerPromise);
  const { data: stats } = useSuspenseQuery({
    queryKey: ['containers', container.id, 'stats'],
    queryFn: () => fetchContainerStats(container.id)
  });
  
  return (
    <div>
      <h3>{container.name}</h3>
      <p>CPU: {stats.cpuUsage}%</p>
    </div>
  );
}

// Integration with useActionState
function FormWithQuery() {
  const createMutation = useMutation({
    mutationFn: createContainer
  });
  
  const [state, formAction, isPending] = useActionState(
    async (previousState: any, formData: FormData) => {
      try {
        const containerData = Object.fromEntries(formData);
        const result = await createMutation.mutateAsync(containerData);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: error.message };
      }
    },
    { success: false }
  );
  
  return (
    <form action={formAction}>
      <input name="name" required />
      <button type="submit" disabled={isPending || createMutation.isPending}>
        {isPending ? 'Criando...' : 'Criar'}
      </button>
      
      {state.error && <div className="error">{state.error}</div>}
      {state.success && <div className="success">Container criado!</div>}
    </form>
  );
}
```

---

**📝 Preparado por:** Augment Agent  
**📅 Data:** 04 de Agosto de 2025  
**🔄 Versão:** 1.0 - React Query 5.56.2 Documentation
