using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;
using AutoInstalador.Core.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace AutoInstalador.API.Controllers;

/// <summary>
/// Controller para gerenciamento de engines de container
/// </summary>
[ApiController]
[Route("api/containers/engines")]
[Produces("application/json")]
public class ContainerEnginesController : ControllerBase
{
    private readonly IContainerEngineService _engineService;
    private readonly ILogger<ContainerEnginesController> _logger;

    public ContainerEnginesController(
        IContainerEngineService engineService,
        ILogger<ContainerEnginesController> logger)
    {
        _engineService = engineService;
        _logger = logger;
    }

    /// <summary>
    /// Lista engines de container disponíveis
    /// </summary>
    /// <returns>Lista de engines</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ContainerEngineListResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerEngineListResponse>> ListEngines(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _engineService.ListEnginesAsync(cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar engines de container");
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Verifica o status de um engine específico
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <returns>Status do engine</returns>
    [HttpGet("{engine}/status")]
    [ProducesResponseType(typeof(ContainerEngineStatusResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerEngineStatusResponse>> GetEngineStatus(
        [FromRoute] ContainerEngine engine,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _engineService.GetEngineStatusAsync(engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter status do engine {Engine}", engine);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Obtém informações detalhadas de um engine
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <returns>Informações do engine</returns>
    [HttpGet("{engine}/info")]
    [ProducesResponseType(typeof(ContainerEngineInfoResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerEngineInfoResponse>> GetEngineInfo(
        [FromRoute] ContainerEngine engine,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _engineService.GetEngineInfoAsync(engine, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter informações do engine {Engine}", engine);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Instala um engine de container
    /// </summary>
    /// <param name="request">Parâmetros de instalação</param>
    /// <returns>Resultado da instalação</returns>
    [HttpPost("install")]
    [ProducesResponseType(typeof(ContainerEngineInstallResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerEngineInstallResponse>> InstallEngine(
        [FromBody] ContainerEngineInstallRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new BaseContainerResponse
                {
                    Success = false,
                    Message = "Dados inválidos",
                    Errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList()
                });
            }

            var response = await _engineService.InstallEngineAsync(request, cancellationToken);
            
            if (!response.Success)
                return BadRequest(response);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao instalar engine {Engine}", request.Engine);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Detecta engines de container instalados
    /// </summary>
    /// <returns>Engines detectados</returns>
    [HttpGet("detect")]
    [ProducesResponseType(typeof(ContainerEngineDetectionResponse), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult<ContainerEngineDetectionResponse>> DetectEngines(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _engineService.DetectEnginesAsync(cancellationToken);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao detectar engines de container");
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Verifica se um engine específico está instalado
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <returns>Status de instalação</returns>
    [HttpGet("{engine}/installed")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 500)]
    public async Task<ActionResult> IsEngineInstalled(
        [FromRoute] ContainerEngine engine,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var status = await _engineService.GetEngineStatusAsync(engine, cancellationToken);
            
            return Ok(new
            {
                engine = engine.ToString(),
                installed = status.IsAvailable,
                running = status.IsRunning,
                version = status.Version
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar instalação do engine {Engine}", engine);
            return StatusCode(500, new BaseContainerResponse
            {
                Success = false,
                Message = "Erro interno do servidor",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Obtém comandos de instalação para um engine
    /// </summary>
    /// <param name="engine">Engine de container</param>
    /// <param name="platform">Plataforma do sistema</param>
    /// <param name="packageManager">Gerenciador de pacotes</param>
    /// <returns>Comandos de instalação</returns>
    [HttpGet("{engine}/install-commands")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(typeof(BaseContainerResponse), 400)]
    public ActionResult GetInstallCommands(
        [FromRoute] ContainerEngine engine,
        [FromQuery] Platform platform,
        [FromQuery] PackageManager? packageManager = null)
    {
        try
        {
            // Esta seria uma implementação simplificada
            // Na prática, você usaria o ContainerEngineDetector
            var commands = GetInstallCommandsForPlatform(engine, platform, packageManager);
            
            return Ok(new
            {
                engine = engine.ToString(),
                platform = platform.ToString(),
                packageManager = packageManager?.ToString(),
                commands = commands,
                requiresAdmin = true,
                estimatedTimeMinutes = 5
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter comandos de instalação para {Engine}", engine);
            return BadRequest(new BaseContainerResponse
            {
                Success = false,
                Message = "Erro ao obter comandos de instalação",
                Errors = { ex.Message }
            });
        }
    }

    /// <summary>
    /// Método auxiliar para obter comandos de instalação
    /// </summary>
    private string[] GetInstallCommandsForPlatform(ContainerEngine engine, Platform platform, PackageManager? packageManager)
    {
        return platform switch
        {
            Platform.Windows => engine switch
            {
                ContainerEngine.Docker => packageManager switch
                {
                    PackageManager.Chocolatey => new[] { "choco install docker-desktop -y" },
                    _ => new[] { "winget install Docker.DockerDesktop" }
                },
                ContainerEngine.Podman => packageManager switch
                {
                    PackageManager.Chocolatey => new[] { "choco install podman -y" },
                    _ => new[] { "winget install RedHat.Podman" }
                },
                _ => throw new ArgumentException($"Engine não suportado: {engine}")
            },
            Platform.MacOS => engine switch
            {
                ContainerEngine.Docker => new[] { "brew install --cask docker" },
                ContainerEngine.Podman => new[] { "brew install podman" },
                _ => throw new ArgumentException($"Engine não suportado: {engine}")
            },
            Platform.Linux => engine switch
            {
                ContainerEngine.Docker => packageManager switch
                {
                    PackageManager.Apt => new[] { "apt-get update && apt-get install -y docker.io" },
                    PackageManager.Dnf => new[] { "dnf install -y docker" },
                    PackageManager.Yum => new[] { "yum install -y docker" },
                    _ => new[] { "apt-get update && apt-get install -y docker.io" }
                },
                ContainerEngine.Podman => packageManager switch
                {
                    PackageManager.Apt => new[] { "apt-get update && apt-get install -y podman" },
                    PackageManager.Dnf => new[] { "dnf install -y podman" },
                    PackageManager.Yum => new[] { "yum install -y podman" },
                    _ => new[] { "apt-get update && apt-get install -y podman" }
                },
                _ => throw new ArgumentException($"Engine não suportado: {engine}")
            },
            _ => throw new PlatformNotSupportedException($"Plataforma não suportada: {platform}")
        };
    }
}
