/**
 * Dashboard Page - Página principal do dashboard
 * Auto-Instalador V3 Lite
 * 
 * @description Página principal com visão geral do sistema
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';

export const DashboardPage: React.FC = () => {
  return (
    <div className="h-full bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          🏠 Dashboard - Auto-Instalador V3 Lite
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card de Containers */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">📦</span>
              <h2 className="text-lg font-semibold">Containers</h2>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Gerencie containers Docker e <PERSON>
            </p>
            <a 
              href="/containers"
              className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Acessar
            </a>
          </div>

          {/* Card de Pacotes */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">📦</span>
              <h2 className="text-lg font-semibold">Pacotes</h2>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Instale e gerencie software
            </p>
            <a 
              href="/packages"
              className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Acessar
            </a>
          </div>

          {/* Card de Monitoramento */}
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">📊</span>
              <h2 className="text-lg font-semibold">Monitoramento</h2>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Estatísticas e logs do sistema
            </p>
            <a 
              href="/monitoring"
              className="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Acessar
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
