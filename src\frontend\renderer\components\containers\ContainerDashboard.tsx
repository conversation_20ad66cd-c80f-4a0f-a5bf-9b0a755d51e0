/**
 * Container Dashboard - Página principal de gerenciamento de containers
 * Auto-Instalador V3 Lite
 * 
 * @description Dashboard principal inspirado no Docker Desktop para gerenciamento de containers
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { useContainers, useContainerStats } from '../../services/container-service';
import { ContainerCard } from './ContainerCard';
import { ContainerSidebar } from './ContainerSidebar';
import { ContainerTopNav } from './ContainerTopNav';
import { ContainerToolbar } from './ContainerToolbar';
import { ContainerLogs } from './ContainerLogs';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { ErrorMessage } from '../common/ErrorMessage';
import type { ContainerEngine, ContainerStatus } from '../../../../shared/types/api.types';

interface ContainerDashboardProps {
  className?: string;
}

export const ContainerDashboard: React.FC<ContainerDashboardProps> = ({ className = '' }) => {
  // Estados locais
  const [selectedEngine, setSelectedEngine] = useState<ContainerEngine>('docker');
  const [activeTab, setActiveTab] = useState<'containers' | 'images' | 'volumes' | 'networks' | 'templates'>('containers');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ContainerStatus | 'all'>('all');
  const [selectedContainer, setSelectedContainer] = useState<string | null>(null);

  // Hooks de dados
  const { 
    data: containers, 
    isLoading: containersLoading, 
    error: containersError 
  } = useContainers({
    all: true,
    engine: selectedEngine,
    filters: {
      status: statusFilter !== 'all' ? [statusFilter] : undefined,
      name: searchTerm || undefined
    }
  });

  // Filtrar containers baseado na busca
  const filteredContainers = React.useMemo(() => {
    if (!containers) return [];
    
    return containers.filter(container => {
      const matchesSearch = !searchTerm || 
        container.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        container.image.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    });
  }, [containers, searchTerm, statusFilter]);

  // Handlers
  const handleContainerSelect = (containerId: string) => {
    setSelectedContainer(containerId);
  };

  const handleEngineChange = (engine: ContainerEngine) => {
    setSelectedEngine(engine);
  };

  const handleTabChange = (tab: typeof activeTab) => {
    setActiveTab(tab);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleStatusFilter = (status: ContainerStatus | 'all') => {
    setStatusFilter(status);
  };

  // Renderização condicional baseada na aba ativa
  const renderContent = () => {
    switch (activeTab) {
      case 'containers':
        return (
          <>
            <ContainerToolbar
              searchTerm={searchTerm}
              statusFilter={statusFilter}
              onSearch={handleSearch}
              onStatusFilter={handleStatusFilter}
              selectedEngine={selectedEngine}
              onEngineChange={handleEngineChange}
            />
            
            <div className="flex-1 overflow-y-auto">
              {containersLoading && (
                <div className="flex items-center justify-center h-64">
                  <LoadingSpinner size="lg" />
                </div>
              )}
              
              {containersError && (
                <ErrorMessage 
                  message="Erro ao carregar containers"
                  details={containersError.message}
                />
              )}
              
              {!containersLoading && !containersError && (
                <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-5 p-6">
                  {filteredContainers.map((container) => (
                    <ContainerCard
                      key={container.id}
                      container={container}
                      onSelect={handleContainerSelect}
                      isSelected={selectedContainer === container.id}
                    />
                  ))}
                  
                  {filteredContainers.length === 0 && (
                    <div className="col-span-full flex flex-col items-center justify-center py-16 text-gray-400">
                      <div className="text-6xl mb-4">📦</div>
                      <h3 className="text-lg font-medium mb-2">Nenhum container encontrado</h3>
                      <p className="text-sm text-center max-w-md">
                        {searchTerm || statusFilter !== 'all' 
                          ? 'Tente ajustar os filtros de busca ou status.'
                          : 'Crie seu primeiro container para começar.'
                        }
                      </p>
                    </div>
                  )}
                </div>
              )}
              
              {/* Seção de logs */}
              {selectedContainer && (
                <ContainerLogs 
                  containerId={selectedContainer}
                  engine={selectedEngine}
                />
              )}
            </div>
          </>
        );
      
      case 'images':
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">🖼️</div>
              <h3 className="text-lg font-medium mb-2">Gerenciamento de Imagens</h3>
              <p className="text-sm">Em desenvolvimento...</p>
            </div>
          </div>
        );
      
      case 'volumes':
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">💾</div>
              <h3 className="text-lg font-medium mb-2">Gerenciamento de Volumes</h3>
              <p className="text-sm">Em desenvolvimento...</p>
            </div>
          </div>
        );
      
      case 'networks':
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">🌐</div>
              <h3 className="text-lg font-medium mb-2">Gerenciamento de Redes</h3>
              <p className="text-sm">Em desenvolvimento...</p>
            </div>
          </div>
        );
      
      case 'templates':
        return (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-lg font-medium mb-2">Templates de Container</h3>
              <p className="text-sm">Em desenvolvimento...</p>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={`flex flex-col h-full bg-gray-900 text-white ${className}`}>
      {/* Navegação superior */}
      <ContainerTopNav
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />
      
      {/* Layout principal */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <ContainerSidebar
          activeSection="containers"
          selectedEngine={selectedEngine}
          onEngineChange={handleEngineChange}
        />
        
        {/* Área de conteúdo */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-800">
          {/* Header do conteúdo */}
          <div className="bg-gray-700 border-b border-gray-600 px-6 py-4">
            <h2 className="text-lg font-semibold text-white capitalize">
              {activeTab === 'containers' ? 'Containers' : 
               activeTab === 'images' ? 'Imagens' :
               activeTab === 'volumes' ? 'Volumes' :
               activeTab === 'networks' ? 'Redes' : 'Templates'}
            </h2>
          </div>
          
          {/* Conteúdo principal */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};
