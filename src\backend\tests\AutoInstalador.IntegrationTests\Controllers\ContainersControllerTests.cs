using AutoInstalador.Core.DTOs.Requests;
using AutoInstalador.Core.DTOs.Responses;
using AutoInstalador.Core.Enums;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace AutoInstalador.IntegrationTests.Controllers;

/// <summary>
/// Testes de integração para ContainersController
/// </summary>
public class ContainersControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;
    private readonly JsonSerializerOptions _jsonOptions;

    public ContainersControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    [Fact]
    public async Task GET_Containers_ShouldReturnOk()
    {
        // Act
        var response = await _client.GetAsync("/api/containers");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ContainerListResponse>(content, _jsonOptions);
        
        Assert.NotNull(result);
        Assert.NotNull(result.Containers);
    }

    [Fact]
    public async Task GET_Containers_WithFilters_ShouldReturnFilteredResults()
    {
        // Arrange
        var queryParams = "?all=true&name=test&status=running";

        // Act
        var response = await _client.GetAsync($"/api/containers{queryParams}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<ContainerListResponse>(content, _jsonOptions);
        
        Assert.NotNull(result);
        Assert.NotNull(result.Containers);
    }

    [Fact]
    public async Task POST_RunContainer_WithValidRequest_ShouldReturnCreated()
    {
        // Arrange
        var request = new ContainerRunRequest
        {
            Image = "hello-world:latest",
            Name = "test-hello-world",
            Engine = ContainerEngine.Docker,
            Detached = true,
            RemoveAfterRun = true,
            Ports = new List<ContainerPortMapping>(),
            Volumes = new List<ContainerVolumeMapping>(),
            Environment = new Dictionary<string, string>(),
            Labels = new Dictionary<string, string>()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/containers/run", request, _jsonOptions);

        // Assert
        // Note: This might fail if Docker is not available in test environment
        // In a real scenario, you'd mock the container service
        if (response.StatusCode == HttpStatusCode.BadRequest)
        {
            // Expected if no container engine is available
            var errorContent = await response.Content.ReadAsStringAsync();
            var errorResult = JsonSerializer.Deserialize<BaseContainerResponse>(errorContent, _jsonOptions);
            Assert.NotNull(errorResult);
            Assert.False(errorResult.Success);
        }
        else
        {
            Assert.Equal(HttpStatusCode.Created, response.StatusCode);
            
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ContainerRunResponse>(content, _jsonOptions);
            
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotEmpty(result.ContainerId);
        }
    }

    [Fact]
    public async Task GET_ContainerById_WithInvalidId_ShouldReturnNotFound()
    {
        // Arrange
        var invalidId = "invalid-container-id";

        // Act
        var response = await _client.GetAsync($"/api/containers/{invalidId}");

        // Assert
        Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
    }

    [Fact]
    public async Task POST_StartContainer_WithInvalidId_ShouldReturnBadRequest()
    {
        // Arrange
        var invalidId = "invalid-container-id";

        // Act
        var response = await _client.PostAsync($"/api/containers/{invalidId}/start", null);

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task GET_ContainerLogs_WithValidId_ShouldReturnLogs()
    {
        // Arrange
        var containerId = "test-container-id";
        var queryParams = "?tail=100&timestamps=true";

        // Act
        var response = await _client.GetAsync($"/api/containers/{containerId}/logs{queryParams}");

        // Assert
        // This will likely return BadRequest if container doesn't exist
        // but we're testing the endpoint structure
        Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task GET_ContainerStats_WithValidId_ShouldReturnStats()
    {
        // Arrange
        var containerId = "test-container-id";

        // Act
        var response = await _client.GetAsync($"/api/containers/{containerId}/stats");

        // Assert
        // This will likely return BadRequest if container doesn't exist
        // but we're testing the endpoint structure
        Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task POST_ExecContainer_WithValidRequest_ShouldExecuteCommand()
    {
        // Arrange
        var containerId = "test-container-id";
        var execRequest = new ContainerExecRequest
        {
            Command = "echo",
            Args = new List<string> { "Hello World" },
            Interactive = false,
            Tty = false
        };

        // Act
        var response = await _client.PostAsJsonAsync($"/api/containers/{containerId}/exec", execRequest, _jsonOptions);

        // Assert
        // This will likely return BadRequest if container doesn't exist
        // but we're testing the endpoint structure
        Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Theory]
    [InlineData("start")]
    [InlineData("stop")]
    [InlineData("restart")]
    public async Task POST_ContainerActions_WithValidId_ShouldReturnAppropriateResponse(string action)
    {
        // Arrange
        var containerId = "test-container-id";

        // Act
        var response = await _client.PostAsync($"/api/containers/{containerId}/{action}", null);

        // Assert
        // This will likely return BadRequest if container doesn't exist
        // but we're testing the endpoint structure
        Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task DELETE_Container_WithValidId_ShouldRemoveContainer()
    {
        // Arrange
        var containerId = "test-container-id";
        var queryParams = "?force=true";

        // Act
        var response = await _client.DeleteAsync($"/api/containers/{containerId}{queryParams}");

        // Assert
        // This will likely return BadRequest if container doesn't exist
        // but we're testing the endpoint structure
        Assert.True(response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.BadRequest);
    }
}
