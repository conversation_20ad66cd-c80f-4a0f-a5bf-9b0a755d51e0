/**
 * Container List Component Tests
 * Auto-Instalador V3 Lite
 * 
 * @description Testes para o componente ContainerList
 * <AUTHOR> Agent
 * @date 2025-08-04
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ContainerList } from '../../../components/features/containers/ContainerList';
import type { Container, ContainerEngine, ContainerStatus } from '../../../../../shared/types/api.types';

// ============================================================================
// MOCKS
// ============================================================================

// Mock do window.containerAPI
const mockContainerAPI = {
  listContainers: vi.fn(),
  startContainer: vi.fn(),
  stopContainer: vi.fn(),
  restartContainer: vi.fn(),
  pauseContainer: vi.fn(),
  unpauseContainer: vi.fn(),
  removeContainer: vi.fn(),
  onContainerStatusChanged: vi.fn(() => () => {}),
  onContainerStatsUpdate: vi.fn(() => () => {}),
  onEngineStatusChanged: vi.fn(() => () => {})
};

// Mock do window object
Object.defineProperty(window, 'containerAPI', {
  value: mockContainerAPI,
  writable: true
});

// Mock do react-hot-toast
vi.mock('react-hot-toast', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// ============================================================================
// TEST DATA
// ============================================================================

const mockContainers: Container[] = [
  {
    id: 'container1',
    name: 'nginx-web',
    image: 'nginx:latest',
    status: 'running' as ContainerStatus,
    engine: 'docker' as ContainerEngine,
    created: new Date('2024-01-01T10:00:00Z'),
    ports: [
      {
        hostPort: 8080,
        containerPort: 80,
        protocol: 'tcp',
        hostIp: '0.0.0.0'
      }
    ],
    mounts: [],
    networks: [],
    labels: {},
    environment: {}
  },
  {
    id: 'container2',
    name: 'redis-cache',
    image: 'redis:alpine',
    status: 'exited' as ContainerStatus,
    engine: 'docker' as ContainerEngine,
    created: new Date('2024-01-01T09:00:00Z'),
    ports: [],
    mounts: [],
    networks: [],
    labels: {},
    environment: {}
  }
];

// ============================================================================
// TEST UTILITIES
// ============================================================================

const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

// ============================================================================
// TESTS
// ============================================================================

describe('ContainerList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render loading state initially', () => {
    mockContainerAPI.listContainers.mockImplementation(() => new Promise(() => {})); // Never resolves

    renderWithQueryClient(<ContainerList />);

    expect(screen.getByText('Carregando containers...')).toBeInTheDocument();
  });

  it('should render containers when data is loaded', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('nginx-web')).toBeInTheDocument();
      expect(screen.getByText('redis-cache')).toBeInTheDocument();
    });

    expect(screen.getByText('2 containers encontrados')).toBeInTheDocument();
  });

  it('should render empty state when no containers', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: [],
      totalCount: 0,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('Nenhum container encontrado')).toBeInTheDocument();
    });
  });

  it('should render error state when API fails', async () => {
    mockContainerAPI.listContainers.mockRejectedValue(new Error('API Error'));

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('Erro ao carregar containers')).toBeInTheDocument();
    });
  });

  it('should filter containers by search term', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('nginx-web')).toBeInTheDocument();
    });

    // Search for nginx
    const searchInput = screen.getByPlaceholderText('Buscar containers...');
    fireEvent.change(searchInput, { target: { value: 'nginx' } });

    // Should still show nginx container (filtering is done client-side)
    expect(screen.getByText('nginx-web')).toBeInTheDocument();
  });

  it('should show action buttons based on container status', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('nginx-web')).toBeInTheDocument();
    });

    // Running container should have stop, restart, pause buttons
    const runningContainer = screen.getByText('nginx-web').closest('.bg-white');
    expect(runningContainer).toBeInTheDocument();

    // Stopped container should have start, remove buttons
    const stoppedContainer = screen.getByText('redis-cache').closest('.bg-white');
    expect(stoppedContainer).toBeInTheDocument();
  });

  it('should call onCreateContainer when create button is clicked', async () => {
    const onCreateContainer = vi.fn();
    
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: [],
      totalCount: 0,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList onCreateContainer={onCreateContainer} />);

    await waitFor(() => {
      expect(screen.getByText('Criar Container')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Criar Container'));
    expect(onCreateContainer).toHaveBeenCalledTimes(1);
  });

  it('should call onViewContainer when container name is clicked', async () => {
    const onViewContainer = vi.fn();
    
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList onViewContainer={onViewContainer} />);

    await waitFor(() => {
      expect(screen.getByText('nginx-web')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('nginx-web'));
    expect(onViewContainer).toHaveBeenCalledWith(mockContainers[0]);
  });

  it('should handle container actions', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    mockContainerAPI.startContainer.mockResolvedValue({
      success: true,
      containerId: 'container2',
      message: 'Container iniciado com sucesso'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('redis-cache')).toBeInTheDocument();
    });

    // Find and click start button for stopped container
    const stoppedContainer = screen.getByText('redis-cache').closest('.bg-white');
    const startButton = stoppedContainer?.querySelector('[title="Iniciar"]');
    
    if (startButton) {
      fireEvent.click(startButton);
      
      await waitFor(() => {
        expect(mockContainerAPI.startContainer).toHaveBeenCalledWith('container2', 'docker');
      });
    }
  });

  it('should update filters correctly', async () => {
    mockContainerAPI.listContainers.mockResolvedValue({
      success: true,
      containers: mockContainers,
      totalCount: 2,
      engine: 'docker'
    });

    renderWithQueryClient(<ContainerList />);

    await waitFor(() => {
      expect(screen.getByText('nginx-web')).toBeInTheDocument();
    });

    // Change status filter
    const statusSelect = screen.getByDisplayValue('Todos os status');
    fireEvent.change(statusSelect, { target: { value: 'running' } });

    // Change engine filter
    const engineSelect = screen.getByDisplayValue('Todos os engines');
    fireEvent.change(engineSelect, { target: { value: 'podman' } });

    // Toggle show all
    const showAllCheckbox = screen.getByLabelText('Mostrar parados');
    fireEvent.click(showAllCheckbox);

    // Verify filters are applied (this would trigger new API calls in real usage)
    expect(statusSelect).toHaveValue('running');
    expect(engineSelect).toHaveValue('podman');
    expect(showAllCheckbox).toBeChecked();
  });
});
