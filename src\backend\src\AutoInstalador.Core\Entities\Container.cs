using AutoInstalador.Core.Enums;

namespace AutoInstalador.Core.Entities;

/// <summary>
/// Entidade que representa um container
/// </summary>
public class Container : BaseEntity
{
    /// <summary>
    /// ID único do container
    /// </summary>
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Imagem utilizada pelo container
    /// </summary>
    public string Image { get; set; } = string.Empty;

    /// <summary>
    /// ID da imagem
    /// </summary>
    public string ImageId { get; set; } = string.Empty;

    /// <summary>
    /// Status atual do container
    /// </summary>
    public ContainerStatus Status { get; set; }

    /// <summary>
    /// Estado atual do container
    /// </summary>
    public ContainerState State { get; set; }

    /// <summary>
    /// Engine de container utilizado
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Plataforma do container
    /// </summary>
    public string Platform { get; set; } = string.Empty;

    /// <summary>
    /// Comando executado no container
    /// </summary>
    public string Command { get; set; } = string.Empty;

    /// <summary>
    /// Argumentos do comando
    /// </summary>
    public string[] Args { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Diretório de trabalho
    /// </summary>
    public string? WorkingDir { get; set; }

    /// <summary>
    /// Usuário do container
    /// </summary>
    public string? User { get; set; }

    /// <summary>
    /// Modo de rede
    /// </summary>
    public string NetworkMode { get; set; } = "bridge";

    /// <summary>
    /// Política de reinicialização
    /// </summary>
    public RestartPolicy RestartPolicy { get; set; }

    /// <summary>
    /// Data de criação do container
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Data de início do container
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Data de finalização do container
    /// </summary>
    public DateTime? FinishedAt { get; set; }

    /// <summary>
    /// Código de saída do container
    /// </summary>
    public int? ExitCode { get; set; }

    /// <summary>
    /// Portas do container
    /// </summary>
    public List<ContainerPort> Ports { get; set; } = new();

    /// <summary>
    /// Volumes do container
    /// </summary>
    public List<ContainerVolume> Volumes { get; set; } = new();

    /// <summary>
    /// Variáveis de ambiente
    /// </summary>
    public Dictionary<string, string> Environment { get; set; } = new();

    /// <summary>
    /// Labels do container
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Recursos do container
    /// </summary>
    public ContainerResources? Resources { get; set; }
}

/// <summary>
/// Porta de um container
/// </summary>
public class ContainerPort
{
    /// <summary>
    /// Porta do container
    /// </summary>
    public int ContainerPortNumber { get; set; }

    /// <summary>
    /// Porta do host (opcional)
    /// </summary>
    public int? HostPort { get; set; }

    /// <summary>
    /// IP do host (opcional)
    /// </summary>
    public string? HostIp { get; set; }

    /// <summary>
    /// Protocolo da porta
    /// </summary>
    public NetworkProtocol Protocol { get; set; } = NetworkProtocol.Tcp;

    /// <summary>
    /// Tipo da porta
    /// </summary>
    public PortType Type { get; set; } = PortType.Exposed;
}

/// <summary>
/// Volume de um container
/// </summary>
public class ContainerVolume
{
    /// <summary>
    /// Caminho de origem (host)
    /// </summary>
    public string Source { get; set; } = string.Empty;

    /// <summary>
    /// Caminho de destino (container)
    /// </summary>
    public string Destination { get; set; } = string.Empty;

    /// <summary>
    /// Modo de acesso
    /// </summary>
    public VolumeMode Mode { get; set; } = VolumeMode.ReadWrite;

    /// <summary>
    /// Tipo do volume
    /// </summary>
    public VolumeType Type { get; set; } = VolumeType.Bind;
}

/// <summary>
/// Recursos de um container
/// </summary>
public class ContainerResources
{
    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long? Memory { get; set; }

    /// <summary>
    /// Limite de memória + swap em bytes
    /// </summary>
    public long? MemorySwap { get; set; }

    /// <summary>
    /// Shares de CPU
    /// </summary>
    public int? CpuShares { get; set; }

    /// <summary>
    /// Quota de CPU
    /// </summary>
    public long? CpuQuota { get; set; }

    /// <summary>
    /// Período de CPU
    /// </summary>
    public long? CpuPeriod { get; set; }

    /// <summary>
    /// CPUs permitidas
    /// </summary>
    public string? CpusetCpus { get; set; }

    /// <summary>
    /// Nós de memória permitidos
    /// </summary>
    public string? CpusetMems { get; set; }

    /// <summary>
    /// Peso de I/O de bloco
    /// </summary>
    public int? BlkioWeight { get; set; }

    /// <summary>
    /// Desabilitar OOM killer
    /// </summary>
    public bool? OomKillDisable { get; set; }
}

/// <summary>
/// Estatísticas de um container
/// </summary>
public class ContainerStats
{
    /// <summary>
    /// ID do container
    /// </summary>
    public string ContainerId { get; set; } = string.Empty;

    /// <summary>
    /// Nome do container
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Estatísticas de CPU
    /// </summary>
    public CpuStats Cpu { get; set; } = new();

    /// <summary>
    /// Estatísticas de memória
    /// </summary>
    public MemoryStats Memory { get; set; } = new();

    /// <summary>
    /// Estatísticas de rede
    /// </summary>
    public NetworkStats Network { get; set; } = new();

    /// <summary>
    /// Estatísticas de I/O de bloco
    /// </summary>
    public BlockIOStats BlockIO { get; set; } = new();

    /// <summary>
    /// Número de PIDs
    /// </summary>
    public int Pids { get; set; }

    /// <summary>
    /// Timestamp das estatísticas
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Estatísticas de CPU
/// </summary>
public class CpuStats
{
    /// <summary>
    /// Uso de CPU em porcentagem
    /// </summary>
    public double Usage { get; set; }

    /// <summary>
    /// Uso de CPU do sistema
    /// </summary>
    public long SystemUsage { get; set; }

    /// <summary>
    /// Número de CPUs online
    /// </summary>
    public int OnlineCpus { get; set; }

    /// <summary>
    /// Tempo de throttling
    /// </summary>
    public long ThrottledTime { get; set; }
}

/// <summary>
/// Estatísticas de memória
/// </summary>
public class MemoryStats
{
    /// <summary>
    /// Uso de memória em bytes
    /// </summary>
    public long Usage { get; set; }

    /// <summary>
    /// Limite de memória em bytes
    /// </summary>
    public long Limit { get; set; }

    /// <summary>
    /// Porcentagem de uso
    /// </summary>
    public double Percentage { get; set; }

    /// <summary>
    /// Cache de memória
    /// </summary>
    public long Cache { get; set; }

    /// <summary>
    /// Uso de swap
    /// </summary>
    public long? Swap { get; set; }
}

/// <summary>
/// Estatísticas de rede
/// </summary>
public class NetworkStats
{
    /// <summary>
    /// Bytes recebidos
    /// </summary>
    public long RxBytes { get; set; }

    /// <summary>
    /// Bytes transmitidos
    /// </summary>
    public long TxBytes { get; set; }

    /// <summary>
    /// Pacotes recebidos
    /// </summary>
    public long RxPackets { get; set; }

    /// <summary>
    /// Pacotes transmitidos
    /// </summary>
    public long TxPackets { get; set; }

    /// <summary>
    /// Erros de recepção
    /// </summary>
    public long RxErrors { get; set; }

    /// <summary>
    /// Erros de transmissão
    /// </summary>
    public long TxErrors { get; set; }
}

/// <summary>
/// Estatísticas de I/O de bloco
/// </summary>
public class BlockIOStats
{
    /// <summary>
    /// Bytes lidos
    /// </summary>
    public long ReadBytes { get; set; }

    /// <summary>
    /// Bytes escritos
    /// </summary>
    public long WriteBytes { get; set; }

    /// <summary>
    /// Operações de leitura
    /// </summary>
    public long ReadOps { get; set; }

    /// <summary>
    /// Operações de escrita
    /// </summary>
    public long WriteOps { get; set; }
}

/// <summary>
/// Imagem de container
/// </summary>
public class ContainerImage : BaseEntity
{
    /// <summary>
    /// ID da imagem
    /// </summary>
    public string ImageId { get; set; } = string.Empty;

    /// <summary>
    /// Repositório da imagem
    /// </summary>
    public string Repository { get; set; } = string.Empty;

    /// <summary>
    /// Tag da imagem
    /// </summary>
    public string Tag { get; set; } = "latest";

    /// <summary>
    /// Digest da imagem
    /// </summary>
    public string? Digest { get; set; }

    /// <summary>
    /// Tamanho da imagem em bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Data de criação da imagem
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Labels da imagem
    /// </summary>
    public Dictionary<string, string> Labels { get; set; } = new();

    /// <summary>
    /// Arquitetura da imagem
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// Sistema operacional da imagem
    /// </summary>
    public string Os { get; set; } = string.Empty;

    /// <summary>
    /// Engine de container
    /// </summary>
    public ContainerEngine Engine { get; set; }
}

/// <summary>
/// Log de container
/// </summary>
public class ContainerLog
{
    /// <summary>
    /// Timestamp do log
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Stream do log
    /// </summary>
    public LogStream Stream { get; set; }

    /// <summary>
    /// Mensagem do log
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// ID do container
    /// </summary>
    public string ContainerId { get; set; } = string.Empty;
}

/// <summary>
/// Informações de um engine de container
/// </summary>
public class ContainerEngineInfo
{
    /// <summary>
    /// Tipo do engine
    /// </summary>
    public ContainerEngine Engine { get; set; }

    /// <summary>
    /// Versão do engine
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Versão da API
    /// </summary>
    public string ApiVersion { get; set; } = string.Empty;

    /// <summary>
    /// Se o engine está disponível
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Se o engine está em execução
    /// </summary>
    public bool IsRunning { get; set; }

    /// <summary>
    /// Se é rootless
    /// </summary>
    public bool Rootless { get; set; }

    /// <summary>
    /// Driver de armazenamento
    /// </summary>
    public string StorageDriver { get; set; } = string.Empty;

    /// <summary>
    /// Versão do cgroup
    /// </summary>
    public string CgroupVersion { get; set; } = string.Empty;

    /// <summary>
    /// Informações da plataforma
    /// </summary>
    public PlatformInfo Platform { get; set; } = new();

    /// <summary>
    /// Informações do runtime
    /// </summary>
    public RuntimeInfo Runtime { get; set; } = new();
}

/// <summary>
/// Informações da plataforma
/// </summary>
public class PlatformInfo
{
    /// <summary>
    /// Nome da plataforma
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Arquitetura
    /// </summary>
    public string Architecture { get; set; } = string.Empty;

    /// <summary>
    /// Sistema operacional
    /// </summary>
    public string Os { get; set; } = string.Empty;
}

/// <summary>
/// Informações do runtime
/// </summary>
public class RuntimeInfo
{
    /// <summary>
    /// Nome do runtime
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Versão do runtime
    /// </summary>
    public string Version { get; set; } = string.Empty;
}
